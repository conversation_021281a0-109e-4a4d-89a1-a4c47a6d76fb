import mongoose, { Schema, Document } from 'mongoose';

export interface ITestimonial extends Document {
  _id: string;
  name: string;
  image: string;
  text: string;
  rating: number;
  service: string;
  featured: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const testimonialSchema = new Schema<ITestimonial>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  image: {
    type: String,
    trim: true,
    default: ''
  },
  text: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },
  service: {
    type: String,
    required: true,
    trim: true
  },
  featured: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better query performance
testimonialSchema.index({ featured: 1 });
testimonialSchema.index({ isActive: 1 });
testimonialSchema.index({ rating: -1 });

export const Testimonial = mongoose.model<ITestimonial>('Testimonial', testimonialSchema);
