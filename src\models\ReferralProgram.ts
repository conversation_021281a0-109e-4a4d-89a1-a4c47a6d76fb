import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IReferral extends Document {
  _id: string;
  referrer: Types.ObjectId;
  referralCode: string;
  referredUsers: Array<{
    user: Types.ObjectId;
    dateReferred: Date;
    status: 'pending' | 'completed';
    rewardEarned: number;
  }>;
  totalReferrals: number;
  totalRewards: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IReferralSettings extends Document {
  _id: string;
  referrerReward: number;
  referredReward: number;
  minimumPurchase: number;
  maxRewards: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const referralSchema = new Schema<IReferral>({
  referrer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  referralCode: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true
  },
  referredUsers: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    dateReferred: {
      type: Date,
      default: Date.now
    },
    status: {
      type: String,
      enum: ['pending', 'completed'],
      default: 'pending'
    },
    rewardEarned: {
      type: Number,
      default: 0
    }
  }],
  totalReferrals: {
    type: Number,
    default: 0
  },
  totalRewards: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

const referralSettingsSchema = new Schema<IReferralSettings>({
  referrerReward: {
    type: Number,
    required: true,
    default: 10
  },
  referredReward: {
    type: Number,
    required: true,
    default: 5
  },
  minimumPurchase: {
    type: Number,
    required: true,
    default: 50
  },
  maxRewards: {
    type: Number,
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Generate referral code before saving
referralSchema.pre('save', function(next) {
  if (!this.referralCode) {
    this.referralCode = 'REF' + Date.now().toString().slice(-6) + Math.random().toString(36).substring(2, 5).toUpperCase();
  }
  next();
});

// Index for better query performance
referralSchema.index({ referrer: 1 });
referralSchema.index({ referralCode: 1 });
referralSchema.index({ isActive: 1 });

export const Referral = mongoose.model<IReferral>('Referral', referralSchema);
export const ReferralSettings = mongoose.model<IReferralSettings>('ReferralSettings', referralSettingsSchema);
