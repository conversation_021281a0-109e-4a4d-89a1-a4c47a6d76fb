import { Request, Response } from 'express';
import { GiftCard, User } from '../models';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';
import { sendEmail } from '../utils/email';
import { AuthenticatedRequest } from '../types';

export class GiftCardController {
  static async createGiftCard(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { amount, recipientName, recipientEmail, message, senderName } = req.body;

      // Set expiry date to 1 year from now
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 1);

      const giftCard = await GiftCard.create({
        amount,
        recipientName,
        recipientEmail,
        message,
        senderName,
        purchasedBy: req.user._id,
        expiryDate
      });

      // Send gift card email to recipient
      try {
        const emailContent = {
          subject: `You've received a gift card from ${senderName}!`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #333;">You've Received a Gift Card!</h2>
              <p>Hello ${recipientName},</p>
              <p>${senderName} has sent you a gift card for MicroLocs!</p>
              <div style="background-color: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px; text-align: center;">
                <h3 style="color: #007bff;">Gift Card Code: ${giftCard.code}</h3>
                <p style="font-size: 18px; font-weight: bold;">Amount: $${amount}</p>
                <p>Expires: ${expiryDate.toDateString()}</p>
              </div>
              ${message ? `<p><strong>Personal Message:</strong> "${message}"</p>` : ''}
              <p>Use this code at checkout to redeem your gift card.</p>
              <p>Thank you for choosing MicroLocs!</p>
            </div>
          `,
          text: `You've received a $${amount} gift card from ${senderName}! Code: ${giftCard.code}. ${message || ''}`
        };

        await sendEmail({
          to: recipientEmail,
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text
        });
      } catch (error) {
        console.error('Failed to send gift card email:', error);
      }

      sendCreated(res, 'Gift card created successfully', giftCard);
    } catch (error) {
      console.error('Create gift card error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getGiftCardBalance(req: Request, res: Response): Promise<void> {
    try {
      const { code } = req.params;

      const giftCard = await GiftCard.findOne({ 
        code: code.toUpperCase(),
        isActive: true 
      });

      if (!giftCard) {
        sendNotFound(res, 'Gift card not found or inactive');
        return;
      }

      // Check if expired
      if (giftCard.expiryDate < new Date()) {
        sendError(res, 'Gift card has expired', undefined, 400);
        return;
      }

      sendSuccess(res, 'Gift card balance retrieved successfully', {
        code: giftCard.code,
        balance: giftCard.balance,
        originalAmount: giftCard.amount,
        recipientName: giftCard.recipientName,
        expiryDate: giftCard.expiryDate,
        isExpired: giftCard.expiryDate < new Date()
      });
    } catch (error) {
      console.error('Get gift card balance error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getUserGiftCards(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { page = 1, limit = 10 } = req.query;
      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [giftCards, total] = await Promise.all([
        GiftCard.find({ purchasedBy: req.user._id })
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        GiftCard.countDocuments({ purchasedBy: req.user._id })
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'User gift cards retrieved successfully', {
        giftCards,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get user gift cards error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async redeemGiftCard(code: string, amount: number): Promise<{ success: boolean; remainingBalance: number; error?: string }> {
    try {
      const giftCard = await GiftCard.findOne({ 
        code: code.toUpperCase(),
        isActive: true 
      });

      if (!giftCard) {
        return { success: false, remainingBalance: 0, error: 'Gift card not found or inactive' };
      }

      if (giftCard.expiryDate < new Date()) {
        return { success: false, remainingBalance: 0, error: 'Gift card has expired' };
      }

      if (giftCard.balance < amount) {
        return { success: false, remainingBalance: giftCard.balance, error: 'Insufficient gift card balance' };
      }

      // Deduct amount from gift card
      giftCard.balance -= amount;
      
      // Deactivate if balance is zero
      if (giftCard.balance === 0) {
        giftCard.isActive = false;
      }

      await giftCard.save();

      return { success: true, remainingBalance: giftCard.balance };
    } catch (error) {
      console.error('Redeem gift card error:', error);
      return { success: false, remainingBalance: 0, error: 'Failed to redeem gift card' };
    }
  }

  static async getAllGiftCards(req: Request, res: Response): Promise<void> {
    try {
      const { page = 1, limit = 20, status, search } = req.query;
      const filter: any = {};

      if (status === 'active') {
        filter.isActive = true;
        filter.expiryDate = { $gt: new Date() };
      } else if (status === 'expired') {
        filter.expiryDate = { $lte: new Date() };
      } else if (status === 'used') {
        filter.balance = 0;
      }

      if (search) {
        filter.$or = [
          { code: { $regex: search, $options: 'i' } },
          { recipientName: { $regex: search, $options: 'i' } },
          { recipientEmail: { $regex: search, $options: 'i' } }
        ];
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [giftCards, total] = await Promise.all([
        GiftCard.find(filter)
          .populate('purchasedBy', 'name email')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        GiftCard.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Gift cards retrieved successfully', {
        giftCards,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get all gift cards error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
