import { useState, useEffect } from 'react'
import ErrorBoundary from './components/ErrorBoundary'
import SEO from './components/SEO'
import { BrandingProvider } from './contexts/BrandingContext'
import Navbar from './components/Layout/Navbar'
import Footer from './components/Layout/Footer'
import Home from './pages/Home'
import Consultation from './pages/Consultation'
import Services from './pages/Services'
import Shop from './pages/Shop/Shop'
import ProductDetail from './pages/Shop/ProductDetail'
import Cart from './pages/Shop/Cart'
import Login from './pages/Auth/Login'
import SignUp from './pages/Auth/SignUp'
import ForgotPassword from './pages/Auth/ForgotPassword'
import UserDashboard from './pages/Dashboard/UserDashboard'
import AdminDashboard from './pages/Dashboard/AdminDashboard'
import { authService, referralService } from './services'
import { useRouter, ROUTES } from './hooks/useRouter'
import './App.css'

function App() {
  const { currentRoute, navigate, navigateToSubRoute, parseCurrentRoute, goBack, canGoBack } = useRouter('home')
  const [selectedProductId, setSelectedProductId] = useState(null)
  const [user, setUser] = useState(null) // null, 'user', or 'admin'
  const [isAuthLoading, setIsAuthLoading] = useState(true)
  const [userProfile, setUserProfile] = useState(null)

  // SEO data for different pages
  const getSEOData = () => {
    const baseData = {
      title: 'Goldie Locs By Tina - Professional Locs & Natural Hair Care',
      description: 'Transform your hair with professional locs installation and maintenance. Specializing in micro locs, traditional locs, and natural hair care in Atlanta, GA.',
      keywords: 'locs, micro locs, natural hair, hair care, Atlanta, dreadlocks, loc maintenance, loc installation'
    }

    switch (currentRoute) {
      case 'services':
        return {
          ...baseData,
          title: 'Hair Services - Goldie Locs By Tina',
          description: 'Professional locs installation, maintenance, and natural hair care services. Micro locs, traditional locs, consultations, and more.',
          keywords: 'locs services, micro locs installation, loc maintenance, natural hair care, Atlanta hair salon'
        }
      case 'consultation':
        return {
          ...baseData,
          title: 'Book Consultation - Goldie Locs By Tina',
          description: 'Schedule your personalized hair consultation. Get expert advice on locs installation, maintenance, and natural hair care.',
          keywords: 'hair consultation, locs consultation, book appointment, Atlanta hair stylist'
        }
      case 'shop':
        return {
          ...baseData,
          title: 'Hair Care Products - Goldie Locs By Tina',
          description: 'Premium natural hair care products for locs and natural hair. Shampoos, oils, treatments, and styling products.',
          keywords: 'hair care products, locs products, natural hair products, hair oil, loc shampoo'
        }
      case 'login':
        return {
          ...baseData,
          title: 'Login - Goldie Locs By Tina',
          description: 'Sign in to your account to manage appointments, view order history, and access exclusive member benefits.'
        }
      case 'signup':
        return {
          ...baseData,
          title: 'Sign Up - Goldie Locs By Tina',
          description: 'Create your account to book appointments, track your locs journey, and get personalized hair care recommendations.'
        }
      case 'forgot-password':
        return {
          ...baseData,
          title: 'Reset Password - Goldie Locs By Tina',
          description: 'Reset your password to regain access to your account and continue your hair care journey.'
        }
      default:
        return baseData
    }
  }

  // Handle navigation with scroll to top
  const handleNavigation = (view) => {
    navigate(view)
  }

  // Load user session on app start and verify authentication
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check for referral code in URL
        const referralCode = referralService.extractReferralCodeFromURL()
        if (referralCode) {
          referralService.storeReferralCode(referralCode)
        }

        // Check if user is authenticated
        if (authService.isAuthenticated()) {
          const currentUser = await authService.getCurrentUser()
          if (currentUser) {
            const userType = currentUser.role === 'admin' ? 'admin' : 'user'
            setUser(userType)
            setUserProfile(currentUser)
          } else {
            // Token is invalid, clear it
            await authService.logout()
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error)
        // Clear invalid auth state
        await authService.logout()
      } finally {
        setIsAuthLoading(false)
      }
    }

    initializeAuth()
  }, [])

  // Handle global authentication errors
  useEffect(() => {
    const handleAuthError = (event) => {
      console.log('Global auth error handler:', event.detail)
      // Clear user state
      setUser(null)
      setUserProfile(null)
      // The API service already handles the redirect
    }

    window.addEventListener('auth-error', handleAuthError)
    return () => window.removeEventListener('auth-error', handleAuthError)
  }, [])

  // Handle global JavaScript errors
  useEffect(() => {
    const handleGlobalError = (event) => {
      console.error('Global error:', event.error)
      // Show user-friendly notification for critical errors
      if (event.error && event.error.message) {
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'error',
            message: 'Something went wrong. Please refresh the page if the issue persists.'
          }
        }))
      }
    }

    const handleUnhandledRejection = (event) => {
      console.error('Unhandled promise rejection:', event.reason)
      // Prevent the default browser behavior (logging to console)
      event.preventDefault()

      // Show user-friendly notification
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'A network error occurred. Please check your connection and try again.'
        }
      }))
    }

    window.addEventListener('error', handleGlobalError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleGlobalError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  // Handle user login
  const handleSetUser = async (userType, rememberMe = false) => {
    setUser(userType)

    // Get updated user profile
    try {
      const currentUser = await authService.getCurrentUser()
      setUserProfile(currentUser)
    } catch (error) {
      console.error('Error getting user profile:', error)
    }

    // Legacy storage for backward compatibility (can be removed later)
    if (rememberMe) {
      localStorage.setItem('rememberedUser', userType)
      sessionStorage.removeItem('currentUser')
    } else {
      sessionStorage.setItem('currentUser', userType)
      localStorage.removeItem('rememberedUser')
    }
  }

  // Handle user logout
  const handleLogout = async () => {
    try {
      await authService.logout()
    } catch (error) {
      console.error('Logout error:', error)
    }

    setUser(null)
    setUserProfile(null)

    // Clear legacy storage for backward compatibility
    localStorage.removeItem('rememberedUser')
    sessionStorage.removeItem('currentUser')
    localStorage.removeItem('currentUser')

    handleNavigation('home')
  }

  const renderCurrentView = () => {
    // Parse main route and sub-route
    const parts = currentRoute.split('#')
    const mainRoute = parts[0]

    switch (mainRoute) {
      case 'home':
        return <Home onNavigate={handleNavigation} />
      case 'consultation':
        return <Consultation />
      case 'services':
        return <Services onNavigate={handleNavigation} />
      case 'shop':
        return <Shop onNavigate={handleNavigation} onSelectProduct={setSelectedProductId} />
      case 'product':
        return <ProductDetail productId={selectedProductId} onNavigate={handleNavigation} />
      case 'cart':
        return <Cart onNavigate={handleNavigation} />
      case 'login':
        return <Login onNavigate={handleNavigation} onLogin={handleSetUser} />
      case 'signup':
        return <SignUp onNavigate={handleNavigation} />
      case 'forgot-password':
        return <ForgotPassword onNavigate={handleNavigation} />
      case 'user-dashboard':
        return <UserDashboard onNavigate={handleNavigation} onLogout={handleLogout} />
      case 'admin-dashboard':
        return <AdminDashboard onNavigate={handleNavigation} onLogout={handleLogout} />
      default:
        return <Home onNavigate={handleNavigation} />
    }
  }

  // Show loading screen while checking authentication
  if (isAuthLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <BrandingProvider>
      <ErrorBoundary onNavigate={handleNavigation}>
        <SEO {...getSEOData()} />
        <div className="min-h-screen bg-gray-50">
          <Navbar
            currentView={currentRoute}
            onNavigate={handleNavigation}
            user={user}
            userProfile={userProfile}
            onLogout={handleLogout}
            goBack={goBack}
            canGoBack={canGoBack}
          />
          <main>
            {renderCurrentView()}
          </main>
          <Footer onNavigate={handleNavigation} />
        </div>
      </ErrorBoundary>
    </BrandingProvider>
  )
}

export default App
