import { useState, useEffect } from 'react'
import ErrorBoundary from './components/ErrorBoundary'
import SEO from './components/SEO'
import Navbar from './components/Layout/Navbar'
import Footer from './components/Layout/Footer'
import Home from './pages/Home'
import Consultation from './pages/Consultation'
import Services from './pages/Services'
import Shop from './pages/Shop/Shop'
import ProductDetail from './pages/Shop/ProductDetail'
import Cart from './pages/Shop/Cart'
import Login from './pages/Auth/Login'
import SignUp from './pages/Auth/SignUp'
import UserDashboard from './pages/Dashboard/UserDashboard'
import AdminDashboard from './pages/Dashboard/AdminDashboard'
import './App.css'

function App() {
  const [currentView, setCurrentView] = useState('home')
  const [selectedProductId, setSelectedProductId] = useState(null)
  const [user, setUser] = useState(null) // null, 'user', or 'admin'

  // SEO data for different pages
  const getSEOData = () => {
    const baseData = {
      title: 'Goldie Locs By Tina - Professional Locs & Natural Hair Care',
      description: 'Transform your hair with professional locs installation and maintenance. Specializing in micro locs, traditional locs, and natural hair care in Atlanta, GA.',
      keywords: 'locs, micro locs, natural hair, hair care, Atlanta, dreadlocks, loc maintenance, loc installation'
    }

    switch (currentView) {
      case 'services':
        return {
          ...baseData,
          title: 'Hair Services - Goldie Locs By Tina',
          description: 'Professional locs installation, maintenance, and natural hair care services. Micro locs, traditional locs, consultations, and more.',
          keywords: 'locs services, micro locs installation, loc maintenance, natural hair care, Atlanta hair salon'
        }
      case 'consultation':
        return {
          ...baseData,
          title: 'Book Consultation - Goldie Locs By Tina',
          description: 'Schedule your personalized hair consultation. Get expert advice on locs installation, maintenance, and natural hair care.',
          keywords: 'hair consultation, locs consultation, book appointment, Atlanta hair stylist'
        }
      case 'shop':
        return {
          ...baseData,
          title: 'Hair Care Products - Goldie Locs By Tina',
          description: 'Premium natural hair care products for locs and natural hair. Shampoos, oils, treatments, and styling products.',
          keywords: 'hair care products, locs products, natural hair products, hair oil, loc shampoo'
        }
      case 'login':
        return {
          ...baseData,
          title: 'Login - Goldie Locs By Tina',
          description: 'Sign in to your account to manage appointments, view order history, and access exclusive member benefits.'
        }
      case 'signup':
        return {
          ...baseData,
          title: 'Sign Up - Goldie Locs By Tina',
          description: 'Create your account to book appointments, track your locs journey, and get personalized hair care recommendations.'
        }
      default:
        return baseData
    }
  }

  // Handle navigation with scroll to top
  const handleNavigation = (view) => {
    setCurrentView(view)
    // Scroll to top when navigating to a new page
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // Load user session on app start - only if "Remember me" was checked
  useEffect(() => {
    // Check if user chose to be remembered (localStorage)
    const rememberedUser = localStorage.getItem('rememberedUser')
    if (rememberedUser) {
      setUser(rememberedUser)
      return
    }

    // Check for temporary session (sessionStorage) - for current browser session only
    const sessionUser = sessionStorage.getItem('currentUser')
    if (sessionUser) {
      setUser(sessionUser)
    }
  }, [])

  // Handle user login
  const handleSetUser = (userType, rememberMe = false) => {
    setUser(userType)

    if (rememberMe) {
      // Save to localStorage for persistent login across browser sessions
      localStorage.setItem('rememberedUser', userType)
      // Remove any temporary session
      sessionStorage.removeItem('currentUser')
    } else {
      // Save to sessionStorage for current browser session only
      sessionStorage.setItem('currentUser', userType)
      // Make sure no persistent login is saved
      localStorage.removeItem('rememberedUser')
    }
  }

  // Handle user logout
  const handleLogout = () => {
    setUser(null)
    // Clear both persistent and session storage
    localStorage.removeItem('rememberedUser')
    sessionStorage.removeItem('currentUser')
    // Also clear the old key for backward compatibility
    localStorage.removeItem('currentUser')
    handleNavigation('home')
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'home':
        return <Home onNavigate={handleNavigation} />
      case 'consultation':
        return <Consultation />
      case 'services':
        return <Services onNavigate={handleNavigation} />
      case 'shop':
        return <Shop onNavigate={handleNavigation} onSelectProduct={setSelectedProductId} />
      case 'product':
        return <ProductDetail productId={selectedProductId} onNavigate={handleNavigation} />
      case 'cart':
        return <Cart onNavigate={handleNavigation} />
      case 'login':
        return <Login onNavigate={handleNavigation} onLogin={handleSetUser} />
      case 'signup':
        return <SignUp onNavigate={handleNavigation} onSignUp={handleSetUser} />
      case 'user-dashboard':
        return <UserDashboard onNavigate={handleNavigation} onLogout={handleLogout} />
      case 'admin-dashboard':
        return <AdminDashboard onNavigate={handleNavigation} onLogout={handleLogout} />
      default:
        return <Home onNavigate={handleNavigation} />
    }
  }

  return (
    <ErrorBoundary onNavigate={handleNavigation}>
      <SEO {...getSEOData()} />
      <div className="min-h-screen bg-gray-50">
        <Navbar
          currentView={currentView}
          onNavigate={handleNavigation}
          user={user}
          onLogout={handleLogout}
        />
        <main>
          {renderCurrentView()}
        </main>
        <Footer onNavigate={handleNavigation} />
      </div>
    </ErrorBoundary>
  )
}

export default App
