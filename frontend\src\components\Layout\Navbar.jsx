import { useState } from 'react'
import { FiMenu, FiX, FiShoppingCart, FiUser } from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'

const Navbar = ({ currentView, onNavigate, user, onLogout }) => {
  const { branding } = useBranding()
  const [isOpen, setIsOpen] = useState(false)

  const isActive = (view) => currentView === view

  const navLinks = [
    { name: 'Home', view: 'home' },
    { name: 'Services', view: 'services' },
    { name: 'Consultation', view: 'consultation' },
    { name: 'Shop', view: 'shop' },
  ]

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <button
            onClick={() => onNavigate('home')}
            className="flex items-center space-x-2 hover:opacity-80 transition-opacity duration-200"
          >
            <div className="text-2xl font-bold" style={{ color: branding.colors.secondary }}>
              {branding.businessName}
            </div>
            {branding.tagline && (
              <div className="text-sm text-gray-600 hidden sm:block">
                {branding.tagline}
              </div>
            )}
          </button>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              <button
                key={link.name}
                onClick={() => onNavigate(link.view)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                  isActive(link.view)
                    ? 'bg-yellow-50'
                    : 'text-gray-700 hover:bg-yellow-50'
                }`}
                style={isActive(link.view) ? { color: branding.colors.secondary } : {}}
              >
                {link.name}
              </button>
            ))}
          </div>

          {/* Desktop Auth & Cart */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              onClick={() => onNavigate('cart')}
              className="p-2 text-gray-700 transition-colors duration-200"
              onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
              onMouseLeave={(e) => e.target.style.color = ''}
            >
              <FiShoppingCart className="w-5 h-5" />
            </button>
            {user ? (
              <>
                <button
                  onClick={() => onNavigate(user === 'admin' ? 'admin-dashboard' : 'user-dashboard')}
                  className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 transition-colors duration-200"
                  onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
                  onMouseLeave={(e) => e.target.style.color = ''}
                >
                  <FiUser className="w-4 h-4 mr-2" />
                  Dashboard
                </button>
                <button
                  onClick={() => {
                    onLogout()
                    onNavigate('home')
                  }}
                  className="px-4 py-2 text-sm font-medium text-gray-700 transition-colors duration-200"
                  onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
                  onMouseLeave={(e) => e.target.style.color = ''}
                >
                  Logout
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => onNavigate('login')}
                  className="px-4 py-2 text-sm font-medium text-gray-700 transition-colors duration-200"
                  onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
                  onMouseLeave={(e) => e.target.style.color = ''}
                >
                  Login
                </button>
                <button
                  onClick={() => onNavigate('signup')}
                  className="px-4 py-2 text-sm font-medium text-black rounded-md transition-colors duration-200"
                  style={{ backgroundColor: branding.colors.secondary }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
                  onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
                >
                  Sign Up
                </button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="p-2 text-gray-700 hover:text-amber-600 transition-colors duration-200"
            >
              {isOpen ? <FiX className="w-6 h-6" /> : <FiMenu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              {navLinks.map((link) => (
                <button
                  key={link.name}
                  onClick={() => {
                    onNavigate(link.view)
                    setIsOpen(false)
                  }}
                  className={`block w-full text-left px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                    isActive(link.view)
                      ? 'bg-yellow-50'
                      : 'text-gray-700 hover:bg-yellow-50'
                  }`}
                  style={isActive(link.view) ? { color: branding.colors.secondary } : {}}
                >
                  {link.name}
                </button>
              ))}
              <div className="border-t pt-3 mt-3">
                <button
                  onClick={() => {
                    onNavigate('cart')
                    setIsOpen(false)
                  }}
                  className="flex items-center w-full px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-amber-600 hover:bg-amber-50 transition-colors duration-200"
                >
                  <FiShoppingCart className="w-5 h-5 mr-2" />
                  Cart
                </button>
                {user ? (
                  <>
                    <button
                      onClick={() => {
                        onNavigate(user === 'admin' ? 'admin-dashboard' : 'user-dashboard')
                        setIsOpen(false)
                      }}
                      className="flex items-center w-full px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-amber-600 hover:bg-amber-50 transition-colors duration-200"
                    >
                      <FiUser className="w-5 h-5 mr-2" />
                      Dashboard
                    </button>
                    <button
                      onClick={() => {
                        onLogout()
                        onNavigate('home')
                        setIsOpen(false)
                      }}
                      className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-amber-600 hover:bg-amber-50 transition-colors duration-200"
                    >
                      Logout
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => {
                        onNavigate('login')
                        setIsOpen(false)
                      }}
                      className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-amber-600 hover:bg-amber-50 transition-colors duration-200"
                    >
                      Login
                    </button>
                    <button
                      onClick={() => {
                        onNavigate('signup')
                        setIsOpen(false)
                      }}
                      className="block w-full text-left px-3 py-2 mt-2 rounded-md text-base font-medium text-black transition-colors duration-200"
                      style={{ backgroundColor: branding.colors.secondary }}
                      onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
                      onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
                    >
                      Sign Up
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

export default Navbar
