import { connectDatabase, disconnectDatabase } from '../config/database';
import { User, Service, Product } from '../models';
import { config } from '../config';

const seedUsers = async (): Promise<void> => {
  console.log('Seeding users...');
  
  // Create admin user
  const adminExists = await User.findOne({ email: config.ADMIN.EMAIL });
  if (!adminExists) {
    await User.create({
      name: 'Admin User',
      email: config.ADMIN.EMAIL,
      phone: '+1234567890',
      password: config.ADMIN.PASSWORD,
      role: 'admin',
      isVerified: true
    });
    console.log('Admin user created');
  }

  // Create sample users
  const sampleUsers = [
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567891',
      password: 'password123',
      role: 'user',
      isVerified: true
    },
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567892',
      password: 'password123',
      role: 'user',
      isVerified: true
    }
  ];

  for (const userData of sampleUsers) {
    const userExists = await User.findOne({ email: userData.email });
    if (!userExists) {
      await User.create(userData);
      console.log(`User ${userData.name} created`);
    }
  }
};

const seedServices = async (): Promise<void> => {
  console.log('Seeding services...');
  
  const services = [
    {
      name: 'Hair Consultation',
      description: 'Professional hair analysis and styling consultation',
      category: 'consultation',
      duration: 60,
      price: 50,
      isActive: true
    },
    {
      name: 'Loc Maintenance',
      description: 'Professional dreadlock maintenance and care',
      category: 'treatment',
      duration: 120,
      price: 80,
      isActive: true
    },
    {
      name: 'Hair Treatment',
      description: 'Deep conditioning and repair treatment',
      category: 'treatment',
      duration: 90,
      price: 65,
      isActive: true
    },
    {
      name: 'Scalp Therapy',
      description: 'Therapeutic scalp massage and treatment',
      category: 'therapy',
      duration: 45,
      price: 40,
      isActive: true
    },
    {
      name: 'Hair Styling',
      description: 'Professional hair styling service',
      category: 'beauty',
      duration: 75,
      price: 55,
      isActive: true
    }
  ];

  for (const serviceData of services) {
    const serviceExists = await Service.findOne({ name: serviceData.name });
    if (!serviceExists) {
      await Service.create(serviceData);
      console.log(`Service ${serviceData.name} created`);
    }
  }
};

const seedProducts = async (): Promise<void> => {
  console.log('Seeding products...');
  
  const products = [
    {
      name: 'Natural Hair Oil',
      description: 'Organic hair oil for nourishment and growth',
      category: 'haircare',
      price: 25.99,
      stock: 50,
      images: ['hair-oil.jpg'],
      isActive: true
    },
    {
      name: 'Loc Shampoo',
      description: 'Specialized shampoo for dreadlocks',
      category: 'haircare',
      price: 18.99,
      stock: 30,
      images: ['loc-shampoo.jpg'],
      isActive: true
    },
    {
      name: 'Scalp Moisturizer',
      description: 'Hydrating moisturizer for healthy scalp',
      category: 'skincare',
      price: 22.50,
      stock: 40,
      images: ['scalp-moisturizer.jpg'],
      isActive: true
    },
    {
      name: 'Hair Growth Serum',
      description: 'Advanced serum to promote hair growth',
      category: 'haircare',
      price: 35.00,
      stock: 25,
      images: ['growth-serum.jpg'],
      isActive: true
    },
    {
      name: 'Vitamin Supplements',
      description: 'Hair and nail vitamin supplements',
      category: 'supplements',
      price: 29.99,
      stock: 60,
      images: ['vitamins.jpg'],
      isActive: true
    },
    {
      name: 'Silk Hair Wrap',
      description: 'Protective silk wrap for hair care',
      category: 'accessories',
      price: 15.99,
      stock: 35,
      images: ['silk-wrap.jpg'],
      isActive: true
    }
  ];

  for (const productData of products) {
    const productExists = await Product.findOne({ name: productData.name });
    if (!productExists) {
      await Product.create(productData);
      console.log(`Product ${productData.name} created`);
    }
  }
};

const seedDatabase = async (): Promise<void> => {
  try {
    console.log('Starting database seeding...');
    
    await connectDatabase();
    
    await seedUsers();
    await seedServices();
    await seedProducts();
    
    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await disconnectDatabase();
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

export { seedDatabase };
