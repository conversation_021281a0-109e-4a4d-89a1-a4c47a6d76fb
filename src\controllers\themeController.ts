import { Request, Response } from 'express';
import { ThemeSettings } from '../models';
import { sendSuccess, sendError } from '../utils/response';

export class ThemeController {
  static async getThemeSettings(req: Request, res: Response): Promise<void> {
    try {
      let theme = await ThemeSettings.findOne({ isActive: true });
      
      if (!theme) {
        // Create default theme if none exists
        theme = await ThemeSettings.create({});
      }

      sendSuccess(res, 'Theme settings retrieved successfully', theme);
    } catch (error) {
      console.error('Get theme settings error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateThemeSettings(req: Request, res: Response): Promise<void> {
    try {
      const updateData = req.body;

      let theme = await ThemeSettings.findOne({ isActive: true });
      
      if (!theme) {
        theme = await ThemeSettings.create(updateData);
      } else {
        Object.assign(theme, updateData);
        await theme.save();
      }

      sendSuccess(res, 'Theme settings updated successfully', theme);
    } catch (error) {
      console.error('Update theme settings error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getPublicTheme(req: Request, res: Response): Promise<void> {
    try {
      const theme = await ThemeSettings.findOne({ isActive: true });
      
      if (!theme) {
        // Return default theme
        sendSuccess(res, 'Public theme retrieved successfully', {
          primaryColor: '#007bff',
          secondaryColor: '#6c757d',
          accentColor: '#28a745',
          backgroundColor: '#ffffff',
          textColor: '#333333',
          fontFamily: 'Inter, sans-serif',
          fontSize: '16px',
          buttonStyle: 'rounded',
          cardStyle: 'elevated',
          borderRadius: '8px',
          spacing: 'normal'
        });
        return;
      }

      // Return theme without sensitive data
      const publicTheme = {
        primaryColor: theme.primaryColor,
        secondaryColor: theme.secondaryColor,
        accentColor: theme.accentColor,
        backgroundColor: theme.backgroundColor,
        textColor: theme.textColor,
        fontFamily: theme.fontFamily,
        fontSize: theme.fontSize,
        buttonStyle: theme.buttonStyle,
        cardStyle: theme.cardStyle,
        borderRadius: theme.borderRadius,
        spacing: theme.spacing
      };

      sendSuccess(res, 'Public theme retrieved successfully', publicTheme);
    } catch (error) {
      console.error('Get public theme error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async resetTheme(req: Request, res: Response): Promise<void> {
    try {
      // Deactivate current theme
      await ThemeSettings.updateMany({}, { isActive: false });

      // Create new default theme
      const defaultTheme = await ThemeSettings.create({});

      sendSuccess(res, 'Theme reset to default successfully', defaultTheme);
    } catch (error) {
      console.error('Reset theme error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
