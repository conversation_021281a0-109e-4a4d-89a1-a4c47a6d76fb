import { Request, Response } from 'express';
import { Product } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';
import { ProductQuery } from '../types';

export class ProductController {
  static async getAllProducts(req: Request, res: Response): Promise<void> {
    try {
      const {
        category,
        page = 1,
        limit = 10,
        search,
        minPrice,
        maxPrice,
        sortBy = 'name',
        sortOrder = 'asc'
      } = req.query as ProductQuery & { sortBy?: string; sortOrder?: string };

      const filter: any = { isActive: true };

      // Category filter
      if (category) {
        filter.category = category;
      }

      // Price range filter
      if (minPrice || maxPrice) {
        filter.price = {};
        if (minPrice) filter.price.$gte = Number(minPrice);
        if (maxPrice) filter.price.$lte = Number(maxPrice);
      }

      // Search filter
      if (search) {
        filter.$text = { $search: search };
      }

      // Pagination
      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      // Sort
      const sort: any = {};
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

      const [products, total] = await Promise.all([
        Product.find(filter)
          .sort(sort)
          .skip(skip)
          .limit(limitNum),
        Product.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Products retrieved successfully', {
        products,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get all products error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getProductById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const product = await Product.findById(id);

      if (!product) {
        sendNotFound(res, 'Product not found');
        return;
      }

      sendSuccess(res, 'Product retrieved successfully', product);
    } catch (error) {
      console.error('Get product by ID error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getProductCategories(req: Request, res: Response): Promise<void> {
    try {
      const categories = await Product.distinct('category', { isActive: true });

      sendSuccess(res, 'Product categories retrieved successfully', categories);
    } catch (error) {
      console.error('Get product categories error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createProduct(req: Request, res: Response): Promise<void> {
    try {
      const productData = req.body;

      const product = await Product.create(productData);

      sendSuccess(res, 'Product created successfully', product, 201);
    } catch (error) {
      console.error('Create product error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateProduct(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const product = await Product.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!product) {
        sendNotFound(res, 'Product not found');
        return;
      }

      sendSuccess(res, 'Product updated successfully', product);
    } catch (error) {
      console.error('Update product error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteProduct(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const product = await Product.findByIdAndDelete(id);

      if (!product) {
        sendNotFound(res, 'Product not found');
        return;
      }

      sendSuccess(res, 'Product deleted successfully');
    } catch (error) {
      console.error('Delete product error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
