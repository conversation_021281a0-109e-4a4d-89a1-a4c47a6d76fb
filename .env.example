# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/microlocsbackend
MONGODB_TEST_URI=mongodb://localhost:27017/microlocsbackend_test

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-token-secret-here
JWT_REFRESH_EXPIRES_IN=30d

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Payment Configuration (Stripe)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Redis Configuration (Optional - for production caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# Push Notifications (Firebase - Optional)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your_session_secret_key

# Analytics Configuration (Optional)
GOOGLE_ANALYTICS_ID=GA_XXXXXXXXX
FACEBOOK_PIXEL_ID=your_facebook_pixel_id

# External APIs (Optional)
WEATHER_API_KEY=your_weather_api_key
MAPS_API_KEY=your_google_maps_api_key
