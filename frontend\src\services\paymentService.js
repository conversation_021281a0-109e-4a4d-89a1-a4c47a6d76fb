import apiService from './api.js'

/**
 * Payment service for handling payment operations
 */
class PaymentService {
  /**
   * Get payment methods for user
   */
  async getPaymentMethods() {
    try {
      const response = await apiService.get('/users/payment-methods')
      return response
    } catch (error) {
      console.error('Get payment methods error:', error)
      throw error
    }
  }

  /**
   * Add payment method
   */
  async addPaymentMethod(paymentMethodData) {
    try {
      const response = await apiService.post('/users/payment-methods', paymentMethodData)
      return response
    } catch (error) {
      console.error('Add payment method error:', error)
      throw error
    }
  }

  /**
   * Update payment method
   */
  async updatePaymentMethod(paymentMethodId, paymentMethodData) {
    try {
      const response = await apiService.put(`/users/payment-methods/${paymentMethodId}`, paymentMethodData)
      return response
    } catch (error) {
      console.error('Update payment method error:', error)
      throw error
    }
  }

  /**
   * Delete payment method
   */
  async deletePaymentMethod(paymentMethodId) {
    try {
      const response = await apiService.delete(`/users/payment-methods/${paymentMethodId}`)
      return response
    } catch (error) {
      console.error('Delete payment method error:', error)
      throw error
    }
  }

  /**
   * Process payment for order
   */
  async processPayment(paymentData) {
    try {
      const response = await apiService.post('/orders/payment', paymentData)
      return response
    } catch (error) {
      console.error('Process payment error:', error)
      throw error
    }
  }

  /**
   * Create payment intent (for Stripe, etc.)
   */
  async createPaymentIntent(orderData) {
    try {
      const response = await apiService.post('/payment/intent', orderData)
      return response
    } catch (error) {
      console.error('Create payment intent error:', error)
      throw error
    }
  }

  /**
   * Confirm payment
   */
  async confirmPayment(paymentIntentId, paymentMethodId) {
    try {
      const response = await apiService.post('/payment/confirm', {
        paymentIntentId,
        paymentMethodId
      })
      return response
    } catch (error) {
      console.error('Confirm payment error:', error)
      throw error
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(paymentId) {
    try {
      const response = await apiService.get(`/payment/status/${paymentId}`)
      return response
    } catch (error) {
      console.error('Get payment status error:', error)
      throw error
    }
  }

  /**
   * Request refund
   */
  async requestRefund(paymentId, refundData) {
    try {
      const response = await apiService.post(`/payment/${paymentId}/refund`, refundData)
      return response
    } catch (error) {
      console.error('Request refund error:', error)
      throw error
    }
  }

  /**
   * Get payment history
   */
  async getPaymentHistory(params = {}) {
    try {
      const response = await apiService.get('/users/payment-history', params)
      return response
    } catch (error) {
      console.error('Get payment history error:', error)
      throw error
    }
  }

  // Admin methods

  /**
   * Get payment settings (Admin)
   */
  async getPaymentSettings() {
    try {
      const response = await apiService.get('/admin/payment-settings')
      return response
    } catch (error) {
      console.error('Get payment settings error:', error)
      throw error
    }
  }

  /**
   * Update payment settings (Admin)
   */
  async updatePaymentSettings(settingsData) {
    try {
      const response = await apiService.put('/admin/payment-settings', settingsData)
      return response
    } catch (error) {
      console.error('Update payment settings error:', error)
      throw error
    }
  }

  /**
   * Get all payments (Admin)
   */
  async getAdminPayments(params = {}) {
    try {
      const response = await apiService.get('/admin/payments', params)
      return response
    } catch (error) {
      console.error('Get admin payments error:', error)
      throw error
    }
  }

  /**
   * Process refund (Admin)
   */
  async processRefund(paymentId, refundData) {
    try {
      const response = await apiService.post(`/admin/payments/${paymentId}/refund`, refundData)
      return response
    } catch (error) {
      console.error('Process refund error:', error)
      throw error
    }
  }

  // Utility methods

  /**
   * Validate credit card number
   */
  validateCreditCard(cardNumber) {
    // Remove spaces and dashes
    const cleanNumber = cardNumber.replace(/[\s-]/g, '')
    
    // Check if it's all digits
    if (!/^\d+$/.test(cleanNumber)) {
      return { valid: false, error: 'Card number must contain only digits' }
    }

    // Check length
    if (cleanNumber.length < 13 || cleanNumber.length > 19) {
      return { valid: false, error: 'Card number must be between 13 and 19 digits' }
    }

    // Luhn algorithm
    let sum = 0
    let isEven = false
    
    for (let i = cleanNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cleanNumber[i])
      
      if (isEven) {
        digit *= 2
        if (digit > 9) {
          digit -= 9
        }
      }
      
      sum += digit
      isEven = !isEven
    }

    const valid = sum % 10 === 0
    return {
      valid,
      error: valid ? null : 'Invalid card number',
      type: this.getCardType(cleanNumber)
    }
  }

  /**
   * Get card type from number
   */
  getCardType(cardNumber) {
    const patterns = {
      visa: /^4/,
      mastercard: /^5[1-5]/,
      amex: /^3[47]/,
      discover: /^6(?:011|5)/,
      dinersclub: /^3[0689]/,
      jcb: /^35/
    }

    for (const [type, pattern] of Object.entries(patterns)) {
      if (pattern.test(cardNumber)) {
        return type
      }
    }

    return 'unknown'
  }

  /**
   * Validate expiry date
   */
  validateExpiryDate(month, year) {
    const now = new Date()
    const currentMonth = now.getMonth() + 1
    const currentYear = now.getFullYear()

    const expMonth = parseInt(month)
    const expYear = parseInt(year)

    if (expMonth < 1 || expMonth > 12) {
      return { valid: false, error: 'Invalid month' }
    }

    if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
      return { valid: false, error: 'Card has expired' }
    }

    return { valid: true }
  }

  /**
   * Validate CVV
   */
  validateCVV(cvv, cardType = 'visa') {
    const cleanCVV = cvv.replace(/\D/g, '')
    
    if (cardType === 'amex') {
      return {
        valid: cleanCVV.length === 4,
        error: cleanCVV.length === 4 ? null : 'CVV must be 4 digits for American Express'
      }
    } else {
      return {
        valid: cleanCVV.length === 3,
        error: cleanCVV.length === 3 ? null : 'CVV must be 3 digits'
      }
    }
  }
}

// Create and export singleton instance
const paymentService = new PaymentService()
export default paymentService
