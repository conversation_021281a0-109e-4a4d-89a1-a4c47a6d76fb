import apiService from './api.js'

/**
 * Service for handling hair services and appointments
 */
class ServiceService {
  /**
   * Get all services
   */
  async getServices(params = {}) {
    try {
      const response = await apiService.get('/services', params)
      return response
    } catch (error) {
      console.error('Get services error:', error)
      throw error
    }
  }

  /**
   * Get service by ID
   */
  async getService(serviceId) {
    try {
      const response = await apiService.get(`/services/${serviceId}`)
      return response
    } catch (error) {
      console.error('Get service error:', error)
      throw error
    }
  }

  /**
   * Get service categories
   */
  async getServiceCategories() {
    try {
      const response = await apiService.get('/services/categories')
      return response
    } catch (error) {
      console.error('Get service categories error:', error)
      throw error
    }
  }

  /**
   * Get service add-ons
   */
  async getServiceAddons(serviceId) {
    try {
      const response = await apiService.get(`/services/${serviceId}/add-ons`)
      return response
    } catch (error) {
      console.error('Get service add-ons error:', error)
      throw error
    }
  }

  // Admin methods for managing services

  /**
   * Create new service (Admin)
   */
  async createService(serviceData) {
    try {
      const response = await apiService.post('/admin/services', serviceData)
      return response
    } catch (error) {
      console.error('Create service error:', error)
      throw error
    }
  }

  /**
   * Update service (Admin)
   */
  async updateService(serviceId, serviceData) {
    try {
      const response = await apiService.put(`/admin/services/${serviceId}`, serviceData)
      return response
    } catch (error) {
      console.error('Update service error:', error)
      throw error
    }
  }

  /**
   * Delete service (Admin)
   */
  async deleteService(serviceId) {
    try {
      const response = await apiService.delete(`/admin/services/${serviceId}`)
      return response
    } catch (error) {
      console.error('Delete service error:', error)
      throw error
    }
  }

  /**
   * Create service category (Admin)
   */
  async createServiceCategory(categoryData) {
    try {
      const response = await apiService.post('/admin/services/categories', categoryData)
      return response
    } catch (error) {
      console.error('Create service category error:', error)
      throw error
    }
  }

  /**
   * Update service category (Admin)
   */
  async updateServiceCategory(categoryId, categoryData) {
    try {
      const response = await apiService.put(`/admin/services/categories/${categoryId}`, categoryData)
      return response
    } catch (error) {
      console.error('Update service category error:', error)
      throw error
    }
  }

  /**
   * Delete service category (Admin)
   */
  async deleteServiceCategory(categoryId) {
    try {
      const response = await apiService.delete(`/admin/services/categories/${categoryId}`)
      return response
    } catch (error) {
      console.error('Delete service category error:', error)
      throw error
    }
  }

  /**
   * Create service add-on (Admin)
   */
  async createServiceAddon(serviceId, addonData) {
    try {
      const response = await apiService.post(`/admin/services/${serviceId}/add-ons`, addonData)
      return response
    } catch (error) {
      console.error('Create service add-on error:', error)
      throw error
    }
  }

  /**
   * Update service add-on (Admin)
   */
  async updateServiceAddon(addonId, addonData) {
    try {
      const response = await apiService.put(`/admin/services/add-ons/${addonId}`, addonData)
      return response
    } catch (error) {
      console.error('Update service add-on error:', error)
      throw error
    }
  }

  /**
   * Delete service add-on (Admin)
   */
  async deleteServiceAddon(addonId) {
    try {
      const response = await apiService.delete(`/admin/services/add-ons/${addonId}`)
      return response
    } catch (error) {
      console.error('Delete service add-on error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const serviceService = new ServiceService()
export default serviceService
