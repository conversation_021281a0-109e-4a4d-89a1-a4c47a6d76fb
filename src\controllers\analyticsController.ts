import { Request, Response } from 'express';
import { User, Appointment, Order, Product, Service } from '../models';
import { sendSuccess, sendError } from '../utils/response';

export class AnalyticsController {
  static async getBusinessAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const { period = 'month', start, end } = req.query;

      let startDate: Date;
      let endDate: Date = new Date();

      // Set date range based on period
      if (start && end) {
        startDate = new Date(start as string);
        endDate = new Date(end as string);
      } else {
        const now = new Date();
        switch (period) {
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
          case 'quarter':
            const quarter = Math.floor(now.getMonth() / 3);
            startDate = new Date(now.getFullYear(), quarter * 3, 1);
            break;
          case 'year':
            startDate = new Date(now.getFullYear(), 0, 1);
            break;
          default:
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }
      }

      // Revenue analytics
      const revenueData = await Order.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate },
            paymentStatus: 'paid'
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$totalAmount' },
            count: { $sum: 1 }
          }
        }
      ]);

      // Revenue by service (from appointments)
      const serviceRevenue = await Appointment.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate },
            status: 'completed'
          }
        },
        {
          $lookup: {
            from: 'services',
            localField: 'service',
            foreignField: '_id',
            as: 'serviceData'
          }
        },
        {
          $unwind: '$serviceData'
        },
        {
          $group: {
            _id: '$serviceData.name',
            value: { $sum: '$serviceData.price' },
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            name: '$_id',
            value: 1,
            count: 1,
            _id: 0
          }
        },
        { $sort: { value: -1 } }
      ]);

      // Revenue by product
      const productRevenue = await Order.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate },
            paymentStatus: 'paid'
          }
        },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.name',
            value: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
            quantity: { $sum: '$items.quantity' }
          }
        },
        {
          $project: {
            name: '$_id',
            value: 1,
            quantity: 1,
            _id: 0
          }
        },
        { $sort: { value: -1 } }
      ]);

      // Appointment analytics
      const appointmentData = await Appointment.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            completed: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
            }
          }
        }
      ]);

      // Appointments by service
      const appointmentsByService = await Appointment.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $lookup: {
            from: 'services',
            localField: 'service',
            foreignField: '_id',
            as: 'serviceData'
          }
        },
        {
          $unwind: '$serviceData'
        },
        {
          $group: {
            _id: '$serviceData.name',
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            name: '$_id',
            count: 1,
            _id: 0
          }
        },
        { $sort: { count: -1 } }
      ]);

      // Customer analytics
      const customerData = await User.aggregate([
        {
          $match: {
            role: 'user'
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            new: {
              $sum: {
                $cond: [
                  { $gte: ['$createdAt', startDate] },
                  1,
                  0
                ]
              }
            }
          }
        }
      ]);

      // Returning customers (customers with more than one order)
      const returningCustomers = await Order.aggregate([
        {
          $group: {
            _id: '$user',
            orderCount: { $sum: 1 }
          }
        },
        {
          $match: {
            orderCount: { $gt: 1 }
          }
        },
        {
          $count: 'returningCustomers'
        }
      ]);

      // Top selling products
      const topSellingProducts = await Order.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate },
            paymentStatus: 'paid'
          }
        },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.name',
            quantity: { $sum: '$items.quantity' },
            revenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
          }
        },
        {
          $project: {
            name: '$_id',
            quantity: 1,
            revenue: 1,
            _id: 0
          }
        },
        { $sort: { quantity: -1 } },
        { $limit: 10 }
      ]);

      const analytics = {
        revenue: {
          total: revenueData[0]?.total || 0,
          byService: serviceRevenue,
          byProduct: productRevenue
        },
        appointments: {
          total: appointmentData[0]?.total || 0,
          byService: appointmentsByService,
          completionRate: appointmentData[0] 
            ? (appointmentData[0].completed / appointmentData[0].total) * 100 
            : 0
        },
        customers: {
          total: customerData[0]?.total || 0,
          new: customerData[0]?.new || 0,
          returning: returningCustomers[0]?.returningCustomers || 0
        },
        products: {
          totalSold: topSellingProducts.reduce((sum, product) => sum + product.quantity, 0),
          topSelling: topSellingProducts
        }
      };

      sendSuccess(res, 'Business analytics retrieved successfully', analytics);
    } catch (error) {
      console.error('Get business analytics error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
