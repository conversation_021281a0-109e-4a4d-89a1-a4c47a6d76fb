import apiService from './api.js'

/**
 * Cart service for handling shopping cart operations
 */
class CartService {
  /**
   * Get current cart
   */
  async getCart() {
    try {
      const response = await apiService.get('/cart')
      return response
    } catch (error) {
      console.error('Get cart error:', error)
      throw error
    }
  }

  /**
   * Add item to cart
   */
  async addToCart(itemData) {
    try {
      const response = await apiService.post('/cart/items', itemData)
      return response
    } catch (error) {
      console.error('Add to cart error:', error)
      throw error
    }
  }

  /**
   * Update cart item
   */
  async updateCartItem(itemId, updateData) {
    try {
      const response = await apiService.put(`/cart/items/${itemId}`, updateData)
      return response
    } catch (error) {
      console.error('Update cart item error:', error)
      throw error
    }
  }

  /**
   * Remove item from cart
   */
  async removeFromCart(itemId) {
    try {
      const response = await apiService.delete(`/cart/items/${itemId}`)
      return response
    } catch (error) {
      console.error('Remove from cart error:', error)
      throw error
    }
  }

  /**
   * Clear entire cart
   */
  async clearCart() {
    try {
      const response = await apiService.delete('/cart')
      return response
    } catch (error) {
      console.error('Clear cart error:', error)
      throw error
    }
  }

  /**
   * Apply discount code
   */
  async applyDiscount(discountCode) {
    try {
      const response = await apiService.post('/cart/discount', {
        code: discountCode
      })
      return response
    } catch (error) {
      console.error('Apply discount error:', error)
      throw error
    }
  }

  /**
   * Remove discount code
   */
  async removeDiscount() {
    try {
      const response = await apiService.delete('/cart/discount')
      return response
    } catch (error) {
      console.error('Remove discount error:', error)
      throw error
    }
  }

  /**
   * Get cart summary (totals, taxes, etc.)
   */
  async getCartSummary() {
    try {
      const response = await apiService.get('/cart/summary')
      return response
    } catch (error) {
      console.error('Get cart summary error:', error)
      throw error
    }
  }

  /**
   * Validate cart before checkout
   */
  async validateCart() {
    try {
      const response = await apiService.post('/cart/validate')
      return response
    } catch (error) {
      console.error('Validate cart error:', error)
      throw error
    }
  }

  /**
   * Get shipping options
   */
  async getShippingOptions(address) {
    try {
      const response = await apiService.post('/cart/shipping-options', address)
      return response
    } catch (error) {
      console.error('Get shipping options error:', error)
      throw error
    }
  }

  /**
   * Calculate taxes
   */
  async calculateTaxes(address) {
    try {
      const response = await apiService.post('/cart/calculate-taxes', address)
      return response
    } catch (error) {
      console.error('Calculate taxes error:', error)
      throw error
    }
  }

  // Local storage methods for guest cart (when user is not logged in)

  /**
   * Get guest cart from local storage
   */
  getGuestCart() {
    try {
      const cart = localStorage.getItem('guestCart')
      return cart ? JSON.parse(cart) : { items: [], total: 0 }
    } catch (error) {
      console.error('Get guest cart error:', error)
      return { items: [], total: 0 }
    }
  }

  /**
   * Save guest cart to local storage
   */
  saveGuestCart(cart) {
    try {
      localStorage.setItem('guestCart', JSON.stringify(cart))
    } catch (error) {
      console.error('Save guest cart error:', error)
    }
  }

  /**
   * Clear guest cart from local storage
   */
  clearGuestCart() {
    try {
      localStorage.removeItem('guestCart')
    } catch (error) {
      console.error('Clear guest cart error:', error)
    }
  }

  /**
   * Merge guest cart with user cart after login
   */
  async mergeGuestCart() {
    try {
      const guestCart = this.getGuestCart()
      if (guestCart.items && guestCart.items.length > 0) {
        const response = await apiService.post('/cart/merge', guestCart)
        this.clearGuestCart()
        return response
      }
      return null
    } catch (error) {
      console.error('Merge guest cart error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const cartService = new CartService()
export default cartService
