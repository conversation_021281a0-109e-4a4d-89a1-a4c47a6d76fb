import { Request, Response } from 'express';
import { ConsultationService } from '../services/consultationService';
import { sendSuccess, sendError, sendCreated } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class ConsultationController {
  /**
   * Smart consultation booking endpoint
   * Handles guest users, existing users, and logged-in users seamlessly
   */
  static async bookConsultation(req: Request, res: Response): Promise<void> {
    try {
      const { name, firstName, lastName, email, phone, service, date, time, message } = req.body;

      // Validate required fields - either name OR (firstName AND lastName)
      if ((!name && (!firstName || !lastName)) || !email || !service || !date || !time) {
        sendError(res, 'Name (or first name and last name), email, service, date, and time are required');
        return;
      }

      // Check if user is authenticated (optional)
      const authReq = req as AuthenticatedRequest;
      const isLoggedIn = !!authReq.user;
      const userId = authReq.user?._id;

      const consultationData = {
        name: name?.trim(),
        firstName: firstName?.trim(),
        lastName: lastName?.trim(),
        email: email.trim().toLowerCase(),
        phone: phone?.trim(),
        service,
        date,
        time,
        message: message?.trim(),
        isLoggedIn,
        userId
      };

      const result = await ConsultationService.bookConsultation(consultationData);

      // Send appropriate response based on whether it's a new user or existing user
      if (result.isNewUser) {
        sendCreated(res, result.message, {
          appointment: result.appointment,
          user: result.user,
          token: result.token,
          refreshToken: result.refreshToken,
          isNewUser: true,
          // Note: In production, send password via email instead of response
          temporaryPassword: result.generatedPassword
        });
      } else {
        sendCreated(res, result.message, {
          appointment: result.appointment,
          user: result.user,
          isNewUser: false
        });
      }

    } catch (error) {
      console.error('Book consultation error:', error);
      
      // Handle specific error cases
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('time slot is already booked')) {
        sendError(res, 'This time slot is already booked. Please choose another time.');
      } else if (errorMessage.includes('Service not found')) {
        sendError(res, 'Selected service is not available.');
      } else if (errorMessage.includes('email already exists')) {
        sendError(res, 'An account with this email already exists. Please log in first.');
      } else {
        sendError(res, 'Failed to book consultation. Please try again.');
      }
    }
  }

  /**
   * Get available consultation time slots
   */
  static async getAvailability(req: Request, res: Response): Promise<void> {
    try {
      const { date, service } = req.query;

      if (!date) {
        sendError(res, 'Date parameter is required');
        return;
      }

      const availableSlots = await ConsultationService.getAvailability(
        date as string,
        service as string
      );

      sendSuccess(res, 'Available consultation slots retrieved successfully', {
        date,
        service,
        availableSlots
      });
    } catch (error) {
      console.error('Get consultation availability error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Guest consultation booking (public endpoint)
   * This is the main endpoint for the consultation form
   */
  static async bookGuestConsultation(req: Request, res: Response): Promise<void> {
    try {
      const { name, firstName, lastName, email, phone, service, date, time, message } = req.body;

      // Validate required fields - either name OR (firstName AND lastName)
      if ((!name && (!firstName || !lastName)) || !email || !service || !date || !time) {
        sendError(res, 'Name (or first name and last name), email, service, date, and time are required');
        return;
      }

      // Validate email format
      const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
      if (!emailRegex.test(email)) {
        sendError(res, 'Please enter a valid email address');
        return;
      }

      // Validate phone format if provided
      if (phone && !/^[\+]?[1-9][\d]{0,15}$/.test(phone)) {
        sendError(res, 'Please enter a valid phone number');
        return;
      }

      const consultationData = {
        name: name?.trim(),
        firstName: firstName?.trim(),
        lastName: lastName?.trim(),
        email: email.trim().toLowerCase(),
        phone: phone?.trim(),
        service,
        date,
        time,
        message: message?.trim(),
        isLoggedIn: false
      };

      const result = await ConsultationService.bookConsultation(consultationData);

      sendCreated(res, result.message, {
        appointment: result.appointment,
        user: {
          name: result.user.name,
          email: result.user.email,
          phone: result.user.phone
        },
        isNewUser: result.isNewUser,
        // Include auth tokens for automatic login if new user
        ...(result.isNewUser && {
          token: result.token,
          refreshToken: result.refreshToken,
          message: 'Account created and consultation booked! You are now logged in.'
        })
      });

    } catch (error) {
      console.error('Book guest consultation error:', error);
      
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('time slot is already booked')) {
        sendError(res, 'This time slot is already booked. Please choose another time.');
      } else if (errorMessage.includes('Service not found')) {
        sendError(res, 'Selected service is not available.');
      } else {
        sendError(res, 'Failed to book consultation. Please try again.');
      }
    }
  }
}
