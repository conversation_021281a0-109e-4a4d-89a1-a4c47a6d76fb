
import { useState, useEffect, useCallback, useRef } from 'react'
import { FiClock, FiDollarSign, FiCalendar, FiStar, FiAlertCircle } from 'react-icons/fi'
import { serviceService } from '../services'
import { FALLBACK_IMAGES } from '../utils/constants'
import { useApiDataFetching } from '../hooks/useDataFetching'
import { useBranding } from '../contexts/BrandingContext'
import Loading from '../components/Loading'

const Services = ({ onNavigate }) => {
  const { branding } = useBranding()
  // Use ref to prevent loading state from causing infinite loops
  const loadingRef = useRef(false)
  // Memoized function to load services data - excludes loading from dependencies
  const loadServicesData = useCallback(async () => {
    if (loadingRef.current) return

    try {
      loadingRef.current = true

      // Load services and categories in parallel
      const [servicesResponse, categoriesResponse] = await Promise.all([
        serviceService.getServices(),
        serviceService.getServiceCategories()
      ])

      const result = {
        services: [],
        categories: []
      }

      if (servicesResponse.success) {
        result.services = servicesResponse.data
      }

      if (categoriesResponse.success) {
        result.categories = categoriesResponse.data
      }

      return result
    } catch (error) {
      console.error('Error loading services:', error)
      throw error
    } finally {
      loadingRef.current = false
    }
  }, []) // No dependencies that would cause recreation

  // Use the custom hook for services data loading
  const {
    data: servicesData,
    loading: isLoading,
    error,
    refetch: refetchServicesData
  } = useApiDataFetching(
    '/services',
    {},
    loadServicesData,
    []
  )

  // Extract data from the hook result
  const services = servicesData?.services || []
  const categories = servicesData?.categories || []

  // Show loading state
  if (isLoading) {
    return <Loading message="Loading services..." />
  }

  // Show loading state
  if (isLoading) {
    return <Loading message="Loading services..." />
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {branding.content.servicesTitle}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {branding.content.servicesSubtitle}
          </p>
          <p className="text-lg text-gray-500 max-w-2xl mx-auto mt-4">
            {branding.content.servicesDescription}
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
            <FiAlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
            <FiAlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        {/* Services Grid */}
        {services.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            {services.map((service) => (
              <div key={service.id} className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="aspect-video bg-gray-200">
                  <img
                    src={service.image || service.images?.[0] || FALLBACK_IMAGES.service}
                    alt={service.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.src = FALLBACK_IMAGES.service
                    }}
                  />
                </div>
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">{service.name}</h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">{service.description}</p>

                  <div className="flex flex-wrap gap-4 mb-6">
                    {service.duration && (
                      <div className="flex items-center text-gray-600">
                        <FiClock className="w-4 h-4 mr-2" style={{ color: '#f3d016' }} />
                        <span className="text-sm">{service.duration}</span>
                      </div>
                    )}
                    {service.price && (
                      <div className="flex items-center text-gray-600">
                        <FiDollarSign className="w-4 h-4 mr-2" style={{ color: '#f3d016' }} />
                        <span className="text-sm font-semibold">
                          {typeof service.price === 'number' ? `$${service.price}` : service.price}
                        </span>
                      </div>
                    )}
                  </div>

                  {service.features && service.features.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-sm font-semibold text-gray-900 mb-3">What's Included:</h4>
                      <ul className="space-y-2">
                        {service.features.map((feature, index) => (
                          <li key={index} className="flex items-center text-sm text-gray-600">
                            <FiStar className="w-3 h-3 mr-2" style={{ color: '#f3d016' }} />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <button
                    onClick={() => onNavigate('consultation')}
                    className="w-full text-white py-3 px-6 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center"
                    style={{ backgroundColor: branding.colors.secondary }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
                    onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
                  >
                    <FiCalendar className="w-4 h-4 mr-2" />
                    {branding.content.bookNowButton}
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          !error && (
            <div className="text-center py-12">
              <p className="text-gray-600">No services available at the moment.</p>
            </div>
          )
        )}

        {/* Additional Info Section */}
        <div className="bg-white rounded-2xl p-8 lg:p-12 shadow-lg">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Why Choose Our Services?
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-1">
                    <FiStar className="w-3 h-3" style={{ color: '#f3d016' }} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">10+ Years Experience</h3>
                    <p className="text-gray-600">Extensive experience in locs and natural hair care</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                    <FiStar className="w-3 h-3" style={{ color: '#008000' }} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Personalized Approach</h3>
                    <p className="text-gray-600">Every service is customized to your unique hair needs</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                    <FiStar className="w-3 h-3" style={{ color: '#008000' }} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Quality Products</h3>
                    <p className="text-gray-600">Only the finest products for optimal hair health</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                    <FiStar className="w-3 h-3" style={{ color: '#008000' }} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Ongoing Support</h3>
                    <p className="text-gray-600">Comprehensive aftercare and maintenance guidance</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-8 text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Get Started?</h3>
              <p className="text-gray-600 mb-6">
                Book a consultation to discuss your hair goals and find the perfect service for you.
              </p>
              <button
                onClick={() => onNavigate('consultation')}
                className="inline-flex items-center px-6 py-3 text-white font-medium rounded-lg transition-colors duration-200"
                style={{ backgroundColor: branding.colors.secondary }}
                onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
                onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
              >
                <FiCalendar className="w-4 h-4 mr-2" />
                {branding.content.scheduleConsultationButton}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Services
