
import { FiClock, FiDollarSign, FiCalendar, FiStar } from 'react-icons/fi'

const Services = ({ onNavigate }) => {
  const services = [
    {
      id: 1,
      name: "Micro Locs Installation",
      description: "Precision micro locs installation for a natural, versatile look. Perfect for those wanting smaller, more manageable locs.",
      duration: "6-8 hours",
      price: "Starting at $300",
      features: [
        "Consultation included",
        "Custom parting pattern",
        "Aftercare instructions",
        "Follow-up appointment"
      ],
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      id: 2,
      name: "Traditional Locs Installation",
      description: "Classic loc installation using traditional methods. Ideal for those wanting thicker, more substantial locs.",
      duration: "4-6 hours",
      price: "Starting at $200",
      features: [
        "Consultation included",
        "Natural parting",
        "Styling options",
        "Maintenance guide"
      ],
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      id: 3,
      name: "Loc Maintenance",
      description: "Regular maintenance to keep your locs healthy, neat, and growing properly. Includes root maintenance and styling.",
      duration: "2-3 hours",
      price: "Starting at $80",
      features: [
        "Root maintenance",
        "Loc tightening",
        "Scalp treatment",
        "Styling included"
      ],
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      id: 4,
      name: "Loc Repair & Restoration",
      description: "Professional repair for damaged or broken locs. Restore your locs to their former glory with expert techniques.",
      duration: "3-5 hours",
      price: "Starting at $150",
      features: [
        "Damage assessment",
        "Repair techniques",
        "Strengthening treatment",
        "Prevention tips"
      ],
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      id: 5,
      name: "Loc Styling & Updos",
      description: "Creative styling for special occasions or everyday wear. Transform your locs into beautiful, elegant styles.",
      duration: "1-2 hours",
      price: "Starting at $60",
      features: [
        "Custom styling",
        "Special occasion looks",
        "Hair accessories",
        "Photo-ready finish"
      ],
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    },
    {
      id: 6,
      name: "Natural Hair Consultation",
      description: "Comprehensive consultation for natural hair care and loc preparation. Perfect for those considering starting their loc journey.",
      duration: "45-60 minutes",
      price: "$50",
      features: [
        "Hair analysis",
        "Loc readiness assessment",
        "Style recommendations",
        "Care plan development"
      ],
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our Services
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Professional locs and natural hair services designed to help you achieve and maintain beautiful, healthy hair.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {services.map((service) => (
            <div key={service.id} className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
              <div className="aspect-video bg-gray-200">
                <img
                  src={service.image}
                  alt={service.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-3">{service.name}</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">{service.description}</p>

                <div className="flex flex-wrap gap-4 mb-6">
                  <div className="flex items-center text-gray-600">
                    <FiClock className="w-4 h-4 mr-2" style={{ color: '#f3d016' }} />
                    <span className="text-sm">{service.duration}</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <FiDollarSign className="w-4 h-4 mr-2" style={{ color: '#f3d016' }} />
                    <span className="text-sm font-semibold">{service.price}</span>
                  </div>
                </div>

                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">What's Included:</h4>
                  <ul className="space-y-2">
                    {service.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <FiStar className="w-3 h-3 mr-2" style={{ color: '#f3d016' }} />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <button
                  onClick={() => onNavigate('consultation')}
                  className="w-full text-white py-3 px-6 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center"
                  style={{ backgroundColor: '#f3d016' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = '#d4b014'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = '#f3d016'}
                >
                  <FiCalendar className="w-4 h-4 mr-2" />
                  Book This Service
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Info Section */}
        <div className="bg-white rounded-2xl p-8 lg:p-12 shadow-lg">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Why Choose Our Services?
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center mt-1">
                    <FiStar className="w-3 h-3" style={{ color: '#f3d016' }} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">10+ Years Experience</h3>
                    <p className="text-gray-600">Extensive experience in locs and natural hair care</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                    <FiStar className="w-3 h-3" style={{ color: '#008000' }} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Personalized Approach</h3>
                    <p className="text-gray-600">Every service is customized to your unique hair needs</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                    <FiStar className="w-3 h-3" style={{ color: '#008000' }} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Quality Products</h3>
                    <p className="text-gray-600">Only the finest products for optimal hair health</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-1">
                    <FiStar className="w-3 h-3" style={{ color: '#008000' }} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Ongoing Support</h3>
                    <p className="text-gray-600">Comprehensive aftercare and maintenance guidance</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl p-8 text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Get Started?</h3>
              <p className="text-gray-600 mb-6">
                Book a consultation to discuss your hair goals and find the perfect service for you.
              </p>
              <button
                onClick={() => onNavigate('consultation')}
                className="inline-flex items-center px-6 py-3 text-white font-medium rounded-lg transition-colors duration-200"
                style={{ backgroundColor: '#f3d016' }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#d4b014'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#f3d016'}
              >
                <FiCalendar className="w-4 h-4 mr-2" />
                Schedule Consultation
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Services
