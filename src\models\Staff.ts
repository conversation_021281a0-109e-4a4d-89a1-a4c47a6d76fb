import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IStaff extends Document {
  _id: string;
  name: string;
  title: string;
  bio: string;
  image: string;
  email: string;
  phone: string;
  services: Types.ObjectId[];
  availability: Array<{
    day: string;
    startTime: string;
    endTime: string;
    isAvailable: boolean;
  }>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const staffSchema = new Schema<IStaff>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  bio: {
    type: String,
    trim: true,
    maxlength: 1000,
    default: ''
  },
  image: {
    type: String,
    trim: true,
    default: ''
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    default: ''
  },
  phone: {
    type: String,
    trim: true,
    default: ''
  },
  services: [{
    type: Schema.Types.ObjectId,
    ref: 'Service'
  }],
  availability: [{
    day: {
      type: String,
      required: true,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    },
    startTime: {
      type: String,
      required: function(this: any) { return this.isAvailable; },
      match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    endTime: {
      type: String,
      required: function(this: any) { return this.isAvailable; },
      match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    isAvailable: {
      type: Boolean,
      default: true
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better query performance
staffSchema.index({ isActive: 1 });
staffSchema.index({ services: 1 });

export const Staff = mongoose.model<IStaff>('Staff', staffSchema);
