import { Router } from 'express';
import { ConsultationController } from '../controllers/consultationController';
import { authenticate } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { body } from 'express-validator';

const router = Router();

// Validation for consultation booking
const consultationValidation = [
  // Either name OR (firstName AND lastName) is required
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please enter a valid email address'),
  body('phone')
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Please enter a valid phone number'),
  body('service')
    .isMongoId()
    .withMessage('Please select a valid service'),
  body('date')
    .isISO8601()
    .toDate()
    .custom((value) => {
      if (value < new Date()) {
        throw new Error('Consultation date cannot be in the past');
      }
      return true;
    })
    .withMessage('Please enter a valid future date'),
  body('time')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Please enter a valid time in HH:MM format'),
  body('message')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Message cannot be more than 500 characters'),
  // Custom validation to ensure either name OR (firstName AND lastName) is provided
  body().custom((value) => {
    if (!value.name && (!value.firstName || !value.lastName)) {
      throw new Error('Either name or both first name and last name are required');
    }
    return true;
  })
];

// GET /api/consultation/availability
// Public endpoint to get available consultation slots
router.get(
  '/availability',
  ConsultationController.getAvailability
);

// POST /api/consultation/book
// Public endpoint for guest consultation booking
// This is the main endpoint for the consultation form
router.post(
  '/book',
  validate(consultationValidation),
  ConsultationController.bookGuestConsultation
);

// POST /api/consultation/book-authenticated
// Authenticated endpoint for logged-in users
// Optional: for users who are already logged in
router.post(
  '/book-authenticated',
  authenticate,
  validate(consultationValidation),
  ConsultationController.bookConsultation
);

export default router;
