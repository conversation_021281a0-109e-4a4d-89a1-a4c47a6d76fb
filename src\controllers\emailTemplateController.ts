import { Request, Response } from 'express';
import { EmailTemplate } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';

export class EmailTemplateController {
  static async getEmailTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { type } = req.params;

      const template = await EmailTemplate.findOne({ type, isActive: true });

      if (!template) {
        sendNotFound(res, 'Email template not found');
        return;
      }

      sendSuccess(res, 'Email template retrieved successfully', template);
    } catch (error) {
      console.error('Get email template error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getAllEmailTemplates(req: Request, res: Response): Promise<void> {
    try {
      const { active } = req.query;
      const filter: any = {};

      if (active !== undefined) {
        filter.isActive = active === 'true';
      }

      const templates = await EmailTemplate.find(filter).sort({ type: 1 });

      sendSuccess(res, 'Email templates retrieved successfully', templates);
    } catch (error) {
      console.error('Get all email templates error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateEmailTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { type } = req.params;
      const { subject, content, variables, isActive } = req.body;

      let template = await EmailTemplate.findOne({ type });

      if (!template) {
        // Create new template if it doesn't exist
        template = await EmailTemplate.create({
          type,
          subject,
          content,
          variables: variables || [],
          isActive: isActive !== undefined ? isActive : true
        });
      } else {
        // Update existing template
        template.subject = subject || template.subject;
        template.content = content || template.content;
        template.variables = variables || template.variables;
        template.isActive = isActive !== undefined ? isActive : template.isActive;
        await template.save();
      }

      sendSuccess(res, 'Email template updated successfully', template);
    } catch (error) {
      console.error('Update email template error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createEmailTemplate(req: Request, res: Response): Promise<void> {
    try {
      const templateData = req.body;

      const template = await EmailTemplate.create(templateData);

      sendSuccess(res, 'Email template created successfully', template, 201);
    } catch (error) {
      console.error('Create email template error:', error);
      if ((error as any).code === 11000) {
        sendError(res, 'Email template for this type already exists');
      } else {
        sendError(res, (error as Error).message);
      }
    }
  }

  static async deleteEmailTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { type } = req.params;

      const template = await EmailTemplate.findOneAndDelete({ type });

      if (!template) {
        sendNotFound(res, 'Email template not found');
        return;
      }

      sendSuccess(res, 'Email template deleted successfully');
    } catch (error) {
      console.error('Delete email template error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async previewEmailTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { type } = req.params;
      const { variables } = req.body;

      const template = await EmailTemplate.findOne({ type, isActive: true });

      if (!template) {
        sendNotFound(res, 'Email template not found');
        return;
      }

      let previewContent = template.content;
      let previewSubject = template.subject;

      // Replace variables in content and subject
      if (variables && typeof variables === 'object') {
        Object.keys(variables).forEach(key => {
          const placeholder = `{{${key}}}`;
          previewContent = previewContent.replace(new RegExp(placeholder, 'g'), variables[key]);
          previewSubject = previewSubject.replace(new RegExp(placeholder, 'g'), variables[key]);
        });
      }

      sendSuccess(res, 'Email template preview generated successfully', {
        type: template.type,
        subject: previewSubject,
        content: previewContent,
        variables: template.variables
      });
    } catch (error) {
      console.error('Preview email template error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
