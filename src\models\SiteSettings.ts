import mongoose, { Schema, Document } from 'mongoose';

export interface ISiteSettings extends Document {
  _id: string;
  general: {
    siteName: string;
    siteDescription: string;
    contactEmail: string;
    contactPhone: string;
    address: string;
    timezone: string;
    currency: string;
    language: string;
  };
  features: {
    enableAppointments: boolean;
    enableEcommerce: boolean;
    enableReviews: boolean;
    enableLoyaltyProgram: boolean;
    enableGiftCards: boolean;
    enableWaitlist: boolean;
    enableReferrals: boolean;
  };
  notifications: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
    appointmentReminders: boolean;
    orderUpdates: boolean;
  };
  maintenance: {
    isMaintenanceMode: boolean;
    maintenanceMessage: string;
    allowedIPs: string[];
  };
  integrations: {
    googleAnalytics: string;
    facebookPixel: string;
    stripePublicKey: string;
    twilioAccountSid: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const siteSettingsSchema = new Schema<ISiteSettings>({
  general: {
    siteName: {
      type: String,
      default: 'MicroLocs',
      trim: true
    },
    siteDescription: {
      type: String,
      default: 'Professional hair care services',
      trim: true
    },
    contactEmail: {
      type: String,
      default: '<EMAIL>',
      trim: true,
      lowercase: true
    },
    contactPhone: {
      type: String,
      default: '',
      trim: true
    },
    address: {
      type: String,
      default: '',
      trim: true
    },
    timezone: {
      type: String,
      default: 'America/New_York',
      trim: true
    },
    currency: {
      type: String,
      default: 'USD',
      trim: true,
      uppercase: true
    },
    language: {
      type: String,
      default: 'en',
      trim: true,
      lowercase: true
    }
  },
  features: {
    enableAppointments: {
      type: Boolean,
      default: true
    },
    enableEcommerce: {
      type: Boolean,
      default: true
    },
    enableReviews: {
      type: Boolean,
      default: true
    },
    enableLoyaltyProgram: {
      type: Boolean,
      default: false
    },
    enableGiftCards: {
      type: Boolean,
      default: false
    },
    enableWaitlist: {
      type: Boolean,
      default: false
    },
    enableReferrals: {
      type: Boolean,
      default: false
    }
  },
  notifications: {
    emailNotifications: {
      type: Boolean,
      default: true
    },
    smsNotifications: {
      type: Boolean,
      default: false
    },
    pushNotifications: {
      type: Boolean,
      default: false
    },
    appointmentReminders: {
      type: Boolean,
      default: true
    },
    orderUpdates: {
      type: Boolean,
      default: true
    }
  },
  maintenance: {
    isMaintenanceMode: {
      type: Boolean,
      default: false
    },
    maintenanceMessage: {
      type: String,
      default: 'We are currently performing maintenance. Please check back soon.',
      trim: true
    },
    allowedIPs: [{
      type: String,
      trim: true
    }]
  },
  integrations: {
    googleAnalytics: {
      type: String,
      default: '',
      trim: true
    },
    facebookPixel: {
      type: String,
      default: '',
      trim: true
    },
    stripePublicKey: {
      type: String,
      default: '',
      trim: true
    },
    twilioAccountSid: {
      type: String,
      default: '',
      trim: true
    }
  }
}, {
  timestamps: true
});

export const SiteSettings = mongoose.model<ISiteSettings>('SiteSettings', siteSettingsSchema);
