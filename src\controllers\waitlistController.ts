import { Request, Response } from 'express';
import { Waitlist, Service } from '../models';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';
import { sendEmail } from '../utils/email';

export class WaitlistController {
  static async addToWaitlist(req: Request, res: Response): Promise<void> {
    try {
      const { name, email, phone, service, preferredDates } = req.body;

      // Verify service exists
      const serviceDoc = await Service.findById(service);
      if (!serviceDoc) {
        sendNotFound(res, 'Service not found');
        return;
      }

      // Check if user is already on waitlist for this service
      const existingEntry = await Waitlist.findOne({
        email,
        service,
        status: { $in: ['waiting', 'contacted'] }
      });

      if (existingEntry) {
        sendError(res, 'You are already on the waitlist for this service');
        return;
      }

      const waitlistEntry = await Waitlist.create({
        name,
        email,
        phone,
        service,
        preferredDates: preferredDates.map((date: string) => new Date(date))
      });

      await waitlistEntry.populate('service', 'name price duration');

      // Send confirmation email
      try {
        const emailContent = {
          subject: 'Waitlist Confirmation - MicroLocs',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #333;">Waitlist Confirmation</h2>
              <p>Hello ${name},</p>
              <p>You have been successfully added to the waitlist for <strong>${serviceDoc.name}</strong>.</p>
              <p>We will contact you as soon as an appointment becomes available on one of your preferred dates.</p>
              <p>Thank you for your patience!</p>
              <p>Best regards,<br>The MicroLocs Team</p>
            </div>
          `,
          text: `You have been added to the waitlist for ${serviceDoc.name}. We will contact you when an appointment becomes available.`
        };

        await sendEmail({
          to: email,
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text
        });
      } catch (error) {
        console.error('Failed to send waitlist confirmation email:', error);
      }

      sendCreated(res, 'Successfully added to waitlist', waitlistEntry);
    } catch (error) {
      console.error('Add to waitlist error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getWaitlist(req: Request, res: Response): Promise<void> {
    try {
      const { service, status, page = 1, limit = 20 } = req.query;
      const filter: any = {};

      if (service) {
        filter.service = service;
      }

      if (status) {
        filter.status = status;
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [waitlistEntries, total] = await Promise.all([
        Waitlist.find(filter)
          .populate('service', 'name price duration')
          .sort({ createdAt: 1 }) // First come, first served
          .skip(skip)
          .limit(limitNum),
        Waitlist.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Waitlist retrieved successfully', {
        waitlistEntries,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get waitlist error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateWaitlistStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status, notes } = req.body;

      const waitlistEntry = await Waitlist.findByIdAndUpdate(
        id,
        { status, notes },
        { new: true, runValidators: true }
      ).populate('service', 'name');

      if (!waitlistEntry) {
        sendNotFound(res, 'Waitlist entry not found');
        return;
      }

      sendSuccess(res, 'Waitlist status updated successfully', waitlistEntry);
    } catch (error) {
      console.error('Update waitlist status error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async removeFromWaitlist(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const waitlistEntry = await Waitlist.findByIdAndDelete(id);

      if (!waitlistEntry) {
        sendNotFound(res, 'Waitlist entry not found');
        return;
      }

      sendSuccess(res, 'Removed from waitlist successfully');
    } catch (error) {
      console.error('Remove from waitlist error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async notifyWaitlist(req: Request, res: Response): Promise<void> {
    try {
      const { service, date, message } = req.body;

      // Get waitlist entries for the service
      const waitlistEntries = await Waitlist.find({
        service,
        status: 'waiting'
      })
      .populate('service', 'name')
      .sort({ createdAt: 1 });

      let notificationsSent = 0;

      for (const entry of waitlistEntries) {
        try {
          const serviceDoc = entry.service as any;
          const emailContent = {
            subject: 'Appointment Available - MicroLocs',
            html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">Appointment Available!</h2>
                <p>Hello ${entry.name},</p>
                <p>Great news! An appointment slot has become available for <strong>${serviceDoc.name}</strong>.</p>
                <p><strong>Available Date:</strong> ${new Date(date).toDateString()}</p>
                ${message ? `<p><strong>Message:</strong> ${message}</p>` : ''}
                <p>Please contact us as soon as possible to book this appointment.</p>
                <p>Best regards,<br>The MicroLocs Team</p>
              </div>
            `,
            text: `Appointment available for ${serviceDoc.name} on ${new Date(date).toDateString()}. Contact us to book!`
          };

          await sendEmail({
            to: entry.email,
            subject: emailContent.subject,
            html: emailContent.html,
            text: emailContent.text
          });

          // Update status to contacted
          entry.status = 'contacted';
          await entry.save();

          notificationsSent++;
        } catch (error) {
          console.error(`Failed to notify ${entry.email}:`, error);
        }
      }

      sendSuccess(res, 'Waitlist notifications sent successfully', {
        notificationsSent,
        totalEntries: waitlistEntries.length
      });
    } catch (error) {
      console.error('Notify waitlist error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
