import { Request, Response } from 'express';
import { Appointment, User, Service } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';
import { sendEmail } from '../utils/email';
import { SMSService } from '../utils/sms';

export class AppointmentReminderController {
  static async sendReminder(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { method, message } = req.body;

      if (!['email', 'sms'].includes(method)) {
        sendError(res, 'Invalid reminder method. Must be "email" or "sms"');
        return;
      }

      const appointment = await Appointment.findById(id)
        .populate('user', 'name email phone')
        .populate('service', 'name duration price');

      if (!appointment) {
        sendNotFound(res, 'Appointment not found');
        return;
      }

      const user = appointment.user as any;
      const service = appointment.service as any;

      if (method === 'email') {
        const emailContent = {
          subject: 'Appointment Reminder - MicroLocs',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #333;">Appointment Reminder</h2>
              <p>Hello ${user.name},</p>
              <p>This is a reminder about your upcoming appointment:</p>
              <div style="background-color: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <p><strong>Service:</strong> ${service.name}</p>
                <p><strong>Date:</strong> ${appointment.date.toDateString()}</p>
                <p><strong>Time:</strong> ${appointment.time}</p>
                <p><strong>Duration:</strong> ${service.duration} minutes</p>
              </div>
              ${message ? `<p><strong>Additional Message:</strong> ${message}</p>` : ''}
              <p>Please arrive 10 minutes early for your appointment.</p>
              <p>If you need to reschedule or cancel, please contact us as soon as possible.</p>
              <p>Best regards,<br>The MicroLocs Team</p>
            </div>
          `,
          text: `Appointment Reminder: ${service.name} on ${appointment.date.toDateString()} at ${appointment.time}. ${message || ''}`
        };

        await sendEmail({
          to: user.email,
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text
        });

        sendSuccess(res, 'Email reminder sent successfully');
      } else if (method === 'sms') {
        // SMS implementation using production-ready SMS service
        try {
          const customMessage = message ?
            `Appointment Reminder: ${service.name} on ${appointment.date.toDateString()} at ${appointment.time}. ${message}` :
            undefined;

          const result = await SMSService.sendAppointmentReminder(
            user.phone,
            service.name,
            appointment.date.toDateString(),
            appointment.time,
            customMessage
          );

          if (result.success) {
            sendSuccess(res, 'SMS reminder sent successfully', { messageId: result.messageId });
          } else {
            sendError(res, result.error || 'Failed to send SMS reminder');
          }
        } catch (error) {
          console.error('SMS sending error:', error);
          sendError(res, 'Failed to send SMS reminder');
        }
      }
    } catch (error) {
      console.error('Send reminder error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async scheduleAutomaticReminders(req: Request, res: Response): Promise<void> {
    try {
      // Get all confirmed appointments for the next 24 hours
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);

      const dayAfterTomorrow = new Date(tomorrow);
      dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 1);

      const appointments = await Appointment.find({
        date: { $gte: tomorrow, $lt: dayAfterTomorrow },
        status: 'confirmed'
      })
      .populate('user', 'name email phone notificationPreferences')
      .populate('service', 'name duration');

      let emailsSent = 0;
      let smssSent = 0;

      for (const appointment of appointments) {
        const user = appointment.user as any;
        const service = appointment.service as any;

        // Send email reminder if user has email notifications enabled
        if (user.notificationPreferences?.email) {
          try {
            const emailContent = {
              subject: 'Appointment Reminder - Tomorrow',
              html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2 style="color: #333;">Appointment Reminder</h2>
                  <p>Hello ${user.name},</p>
                  <p>This is a reminder about your appointment tomorrow:</p>
                  <div style="background-color: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
                    <p><strong>Service:</strong> ${service.name}</p>
                    <p><strong>Date:</strong> ${appointment.date.toDateString()}</p>
                    <p><strong>Time:</strong> ${appointment.time}</p>
                  </div>
                  <p>Please arrive 10 minutes early for your appointment.</p>
                  <p>Best regards,<br>The MicroLocs Team</p>
                </div>
              `,
              text: `Appointment reminder: ${service.name} tomorrow at ${appointment.time}`
            };

            await sendEmail({
              to: user.email,
              subject: emailContent.subject,
              html: emailContent.html,
              text: emailContent.text
            });

            emailsSent++;
          } catch (error) {
            console.error(`Failed to send email reminder to ${user.email}:`, error);
          }
        }

        // SMS reminders implementation using production-ready service
        if (user.notificationPreferences?.sms && user.phone) {
          try {
            const tomorrow = new Date(appointment.date);
            const result = await SMSService.sendAppointmentReminder(
              user.phone,
              service.name,
              tomorrow.toDateString(),
              appointment.time
            );

            if (result.success) {
              smssSent++;
            } else {
              console.error(`Failed to send SMS reminder to ${user.phone}: ${result.error}`);
            }
          } catch (error) {
            console.error(`Failed to send SMS reminder to ${user.phone}:`, error);
          }
        }
      }

      sendSuccess(res, 'Automatic reminders processed successfully', {
        appointmentsProcessed: appointments.length,
        emailsSent,
        smssSent
      });
    } catch (error) {
      console.error('Schedule automatic reminders error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
