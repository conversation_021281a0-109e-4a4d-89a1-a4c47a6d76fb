import { COLORS } from '../utils/constants'

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  onClick,
  disabled = false,
  loading = false,
  icon = null,
  iconPosition = 'right',
  className = '',
  ariaLabel,
  type = 'button',
  ...props
}) => {
  const baseStyles = `
    inline-flex items-center justify-center font-medium rounded-lg 
    transition-all duration-200 
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500
    disabled:opacity-50 disabled:cursor-not-allowed
    hover:shadow-lg transform hover:-translate-y-0.5
    active:transform active:translate-y-0
  `

  const variants = {
    primary: `
      text-white
      ${disabled ? 'bg-gray-400' : ''}
    `,
    secondary: `
      bg-white border-2 text-gray-700
      hover:bg-gray-50
      ${disabled ? 'border-gray-300 text-gray-400' : ''}
    `,
    ghost: `
      bg-transparent text-gray-700
      hover:bg-gray-100
      ${disabled ? 'text-gray-400' : ''}
    `,
    danger: `
      bg-red-600 text-white
      hover:bg-red-700
      ${disabled ? 'bg-red-300' : ''}
    `
  }

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
    xl: 'px-10 py-5 text-xl'
  }

  const getVariantStyles = () => {
    if (disabled) return variants[variant]
    
    switch (variant) {
      case 'primary':
        return `${variants.primary} text-white`
      case 'secondary':
        return `${variants.secondary} border-yellow-500`
      default:
        return variants[variant]
    }
  }

  const handleClick = (e) => {
    if (disabled || loading) return
    onClick?.(e)
  }

  const handleMouseEnter = (e) => {
    if (disabled || loading) return
    
    switch (variant) {
      case 'primary':
        e.target.style.backgroundColor = COLORS.primaryHover
        break
      case 'secondary':
        e.target.style.backgroundColor = COLORS.accent
        e.target.style.color = COLORS.primary
        break
      default:
        break
    }
  }

  const handleMouseLeave = (e) => {
    if (disabled || loading) return
    
    switch (variant) {
      case 'primary':
        e.target.style.backgroundColor = COLORS.primary
        break
      case 'secondary':
        e.target.style.backgroundColor = 'white'
        e.target.style.color = COLORS.primary
        break
      default:
        break
    }
  }

  const renderIcon = () => {
    if (loading) {
      return (
        <svg 
          className="animate-spin w-4 h-4" 
          fill="none" 
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <circle 
            className="opacity-25" 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            strokeWidth="4"
          />
          <path 
            className="opacity-75" 
            fill="currentColor" 
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )
    }
    
    if (icon) {
      return <span className="w-4 h-4" aria-hidden="true">{icon}</span>
    }
    
    return null
  }

  const iconElement = renderIcon()

  return (
    <button
      type={type}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      disabled={disabled || loading}
      aria-label={ariaLabel}
      className={`${baseStyles} ${getVariantStyles()} ${sizes[size]} ${className}`}
      style={{
        backgroundColor: variant === 'primary' && !disabled ? COLORS.primary : undefined,
        borderColor: variant === 'secondary' && !disabled ? COLORS.primary : undefined,
        color: variant === 'secondary' && !disabled ? COLORS.primary : undefined
      }}
      {...props}
    >
      {iconPosition === 'left' && iconElement && (
        <span className={children ? 'mr-2' : ''}>{iconElement}</span>
      )}
      
      {loading ? 'Loading...' : children}
      
      {iconPosition === 'right' && iconElement && (
        <span className={children ? 'ml-2' : ''}>{iconElement}</span>
      )}
    </button>
  )
}

export default Button
