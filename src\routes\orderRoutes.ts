import { Router } from 'express';
import { OrderController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import {
  createOrderValidation,
  mongoIdValidation,
  paginationValidation
} from '../utils/validation';

const router = Router();

// POST /api/orders
router.post(
  '/',
  authenticate,
  validate(createOrderValidation),
  OrderController.createOrder
);

// GET /api/orders
router.get(
  '/',
  authenticate,
  validate(paginationValidation),
  OrderController.getUserOrders
);

// GET /api/orders/:id
router.get(
  '/:id',
  authenticate,
  validate(mongoIdValidation()),
  OrderController.getOrderById
);

// Admin routes
// PUT /api/orders/:id/status (admin only)
router.put(
  '/:id/status',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  OrderController.updateOrderStatus
);

// GET /api/orders/admin/all (admin only)
router.get(
  '/admin/all',
  authenticate,
  authorize('admin'),
  validate(paginationValidation),
  OrderController.getAllOrders
);

export default router;
