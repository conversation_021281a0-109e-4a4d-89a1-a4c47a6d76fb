import mongoose, { Schema, Document } from 'mongoose';

export interface IDiscountCode extends Document {
  _id: string;
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  minPurchase: number;
  maxUses: number;
  currentUses: number;
  expiryDate: Date;
  products: string[];
  services: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const discountCodeSchema = new Schema<IDiscountCode>({
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true,
    maxlength: 20
  },
  type: {
    type: String,
    required: true,
    enum: ['percentage', 'fixed']
  },
  value: {
    type: Number,
    required: true,
    min: 0
  },
  minPurchase: {
    type: Number,
    default: 0,
    min: 0
  },
  maxUses: {
    type: Number,
    default: null
  },
  currentUses: {
    type: Number,
    default: 0,
    min: 0
  },
  expiryDate: {
    type: Date,
    required: true
  },
  products: [{
    type: Schema.Types.ObjectId,
    ref: 'Product'
  }],
  services: [{
    type: Schema.Types.ObjectId,
    ref: 'Service'
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better query performance
discountCodeSchema.index({ code: 1 });
discountCodeSchema.index({ isActive: 1 });
discountCodeSchema.index({ expiryDate: 1 });

// Virtual to check if discount is valid
discountCodeSchema.virtual('isValid').get(function() {
  const now = new Date();
  const isNotExpired = this.expiryDate > now;
  const hasUsesLeft = this.maxUses === null || this.currentUses < this.maxUses;
  return this.isActive && isNotExpired && hasUsesLeft;
});

export const DiscountCode = mongoose.model<IDiscountCode>('DiscountCode', discountCodeSchema);
