import { Request, Response } from 'express';
import { Referral, ReferralSettings, User } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class ReferralController {
  static async getUserReferral(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      let referral = await Referral.findOne({ referrer: req.user._id })
        .populate('referredUsers.user', 'name email');

      if (!referral) {
        referral = await Referral.create({ referrer: req.user._id });
      }

      const settings = await ReferralSettings.findOne() || await ReferralSettings.create({});

      sendSuccess(res, 'User referral data retrieved successfully', {
        referralCode: referral.referralCode,
        totalReferrals: referral.totalReferrals,
        totalRewards: referral.totalRewards,
        referredUsers: referral.referredUsers,
        settings: {
          referrerReward: settings.referrerReward,
          referredReward: settings.referredReward,
          minimumPurchase: settings.minimumPurchase
        }
      });
    } catch (error) {
      console.error('Get user referral error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async validateReferralCode(req: Request, res: Response): Promise<void> {
    try {
      const { code } = req.body;

      if (!code) {
        sendError(res, 'Referral code is required');
        return;
      }

      const referral = await Referral.findOne({ 
        referralCode: code.toUpperCase(),
        isActive: true 
      }).populate('referrer', 'name');

      if (!referral) {
        sendError(res, 'Invalid referral code', undefined, 404);
        return;
      }

      const settings = await ReferralSettings.findOne() || await ReferralSettings.create({});

      sendSuccess(res, 'Referral code is valid', {
        referralCode: referral.referralCode,
        referrerName: (referral.referrer as any).name,
        reward: settings.referredReward,
        minimumPurchase: settings.minimumPurchase
      });
    } catch (error) {
      console.error('Validate referral code error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async processReferral(referralCode: string, newUserId: string, orderAmount: number): Promise<boolean> {
    try {
      const referral = await Referral.findOne({ 
        referralCode: referralCode.toUpperCase(),
        isActive: true 
      });

      if (!referral) {
        return false;
      }

      const settings = await ReferralSettings.findOne();
      if (!settings || !settings.isActive || orderAmount < settings.minimumPurchase) {
        return false;
      }

      // Check if user was already referred
      const existingReferral = referral.referredUsers.find(
        ref => ref.user.toString() === newUserId
      );

      if (existingReferral) {
        // Update existing referral to completed
        existingReferral.status = 'completed';
        existingReferral.rewardEarned = settings.referrerReward;
      } else {
        // Add new referral
        referral.referredUsers.push({
          user: newUserId,
          status: 'completed',
          rewardEarned: settings.referrerReward
        } as any);
      }

      referral.totalReferrals = referral.referredUsers.filter(ref => ref.status === 'completed').length;
      referral.totalRewards += settings.referrerReward;

      await referral.save();
      return true;
    } catch (error) {
      console.error('Process referral error:', error);
      return false;
    }
  }

  static async getReferralSettings(req: Request, res: Response): Promise<void> {
    try {
      let settings = await ReferralSettings.findOne();
      
      if (!settings) {
        settings = await ReferralSettings.create({});
      }

      sendSuccess(res, 'Referral settings retrieved successfully', settings);
    } catch (error) {
      console.error('Get referral settings error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateReferralSettings(req: Request, res: Response): Promise<void> {
    try {
      const updateData = req.body;

      let settings = await ReferralSettings.findOne();
      
      if (!settings) {
        settings = await ReferralSettings.create(updateData);
      } else {
        Object.assign(settings, updateData);
        await settings.save();
      }

      sendSuccess(res, 'Referral settings updated successfully', settings);
    } catch (error) {
      console.error('Update referral settings error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getAllReferrals(req: Request, res: Response): Promise<void> {
    try {
      const { page = 1, limit = 20, active } = req.query;
      const filter: any = {};

      if (active !== undefined) {
        filter.isActive = active === 'true';
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [referrals, total] = await Promise.all([
        Referral.find(filter)
          .populate('referrer', 'name email')
          .populate('referredUsers.user', 'name email')
          .sort({ totalReferrals: -1 })
          .skip(skip)
          .limit(limitNum),
        Referral.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Referrals retrieved successfully', {
        referrals,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get all referrals error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
