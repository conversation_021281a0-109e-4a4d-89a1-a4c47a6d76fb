import { Request, Response } from 'express';
import { PaymentSettings } from '../models';
import { sendSuccess, sendError } from '../utils/response';

export class PaymentController {
  static async getPaymentSettings(req: Request, res: Response): Promise<void> {
    try {
      let settings = await PaymentSettings.findOne();
      
      if (!settings) {
        // Create default settings if none exist
        settings = await PaymentSettings.create({});
      }

      sendSuccess(res, 'Payment settings retrieved successfully', settings);
    } catch (error) {
      console.error('Get payment settings error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updatePaymentSettings(req: Request, res: Response): Promise<void> {
    try {
      const updateData = req.body;

      let settings = await PaymentSettings.findOne();
      
      if (!settings) {
        settings = await PaymentSettings.create(updateData);
      } else {
        Object.assign(settings, updateData);
        await settings.save();
      }

      sendSuccess(res, 'Payment settings updated successfully', settings);
    } catch (error) {
      console.error('Update payment settings error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
