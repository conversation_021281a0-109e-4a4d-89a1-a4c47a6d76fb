import { useState, useEffect, useCallback } from 'react'

/**
 * Simple SPA router hook that handles browser navigation
 * Inspired by modern routing patterns but simplified for this app
 */
export const useRouter = (initialRoute = 'home') => {
  const [currentRoute, setCurrentRoute] = useState(() => {
    // Get initial route from URL hash or use default
    const hash = window.location.hash.slice(1) // Remove #
    return hash || initialRoute
  })

  // Parse route and sub-route from hash
  const parseCurrentRoute = useCallback(() => {
    const parts = currentRoute.split('#')
    const mainRoute = parts[0] || 'home'
    const subRoute = parts[1] || null
    return { mainRoute, subRoute, fullRoute: currentRoute }
  }, [currentRoute])

  // Handle browser back/forward buttons
  useEffect(() => {
    const handlePopState = () => {
      const hash = window.location.hash.slice(1)
      setCurrentRoute(hash || initialRoute)
    }

    window.addEventListener('popstate', handlePopState)
    return () => window.removeEventListener('popstate', handlePopState)
  }, [initialRoute])

  // Navigate to a new route
  const navigate = useCallback((route, options = {}) => {
    const { replace = false, scrollToTop = true } = options

    // Update URL
    if (replace) {
      window.history.replaceState(null, '', `#${route}`)
    } else {
      window.history.pushState(null, '', `#${route}`)
    }

    // Update state
    setCurrentRoute(route)

    // Scroll to top if requested
    if (scrollToTop) {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }, [])

  // Navigate to a sub-route within a dashboard
  const navigateToSubRoute = useCallback((mainRoute, subRoute, options = {}) => {
    const fullRoute = `${mainRoute}#${subRoute}`
    navigate(fullRoute, options)
  }, [navigate])

  // Go back in history
  const goBack = useCallback(() => {
    window.history.back()
  }, [])

  // Go forward in history
  const goForward = useCallback(() => {
    window.history.forward()
  }, [])

  // Check if we can go back
  const canGoBack = window.history.length > 1

  return {
    currentRoute,
    navigate,
    navigateToSubRoute,
    parseCurrentRoute,
    goBack,
    goForward,
    canGoBack
  }
}

/**
 * Route definitions for the application
 */
export const ROUTES = {
  HOME: 'home',
  SERVICES: 'services',
  CONSULTATION: 'consultation',
  SHOP: 'shop',
  PRODUCT: 'product',
  CART: 'cart',
  LOGIN: 'login',
  SIGNUP: 'signup',
  FORGOT_PASSWORD: 'forgot-password',
  USER_DASHBOARD: 'user-dashboard',
  ADMIN_DASHBOARD: 'admin-dashboard'
}

/**
 * Helper function to build route with parameters
 */
export const buildRoute = (route, params = {}) => {
  let finalRoute = route
  
  // Replace parameters in route
  Object.keys(params).forEach(key => {
    finalRoute = finalRoute.replace(`:${key}`, params[key])
  })
  
  return finalRoute
}

/**
 * Helper function to parse route parameters
 */
export const parseRoute = (route) => {
  const parts = route.split('/')
  const routeName = parts[0]
  const params = {}
  
  // Simple parameter parsing (can be enhanced)
  if (parts.length > 1) {
    for (let i = 1; i < parts.length; i += 2) {
      if (parts[i + 1]) {
        params[parts[i]] = parts[i + 1]
      }
    }
  }
  
  return { routeName, params }
}
