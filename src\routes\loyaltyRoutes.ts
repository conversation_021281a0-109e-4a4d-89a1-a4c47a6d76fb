import { Router } from 'express';
import { LoyaltyController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation, paginationValidation } from '../utils/validation';

const router = Router();

// GET /api/users/loyalty
router.get(
  '/',
  authenticate,
  LoyaltyController.getUserLoyalty
);

// POST /api/users/loyalty/redeem
router.post(
  '/redeem',
  authenticate,
  LoyaltyController.redeemReward
);

// GET /api/users/loyalty/transactions
router.get(
  '/transactions',
  authenticate,
  validate(paginationValidation),
  LoyaltyController.getLoyaltyTransactions
);

// GET /api/loyalty/rewards
router.get(
  '/rewards',
  LoyaltyController.getLoyaltyRewards
);

// Admin routes
// POST /api/admin/loyalty/rewards (admin only)
router.post(
  '/admin/rewards',
  authenticate,
  authorize('admin'),
  LoyaltyController.createLoyaltyReward
);

// PUT /api/admin/loyalty/rewards/:id (admin only)
router.put(
  '/admin/rewards/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  LoyaltyController.updateLoyaltyReward
);

// DELETE /api/admin/loyalty/rewards/:id (admin only)
router.delete(
  '/admin/rewards/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  LoyaltyController.deleteLoyaltyReward
);

export default router;
