import { Request, Response } from 'express';
import { Order, Product, Cart } from '../models';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';
import { sendEmail, emailTemplates } from '../utils/email';
import { AuthenticatedRequest, IOrderItem } from '../types';

export class OrderController {
  static async createOrder(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { items, shippingAddress, paymentMethod } = req.body;

      // Validate and calculate order items
      const orderItems: IOrderItem[] = [];
      let totalAmount = 0;

      for (const item of items) {
        const product = await Product.findById(item.productId);
        
        if (!product || !product.isActive) {
          sendError(res, `Product ${item.productId} not found or inactive`);
          return;
        }

        if (product.stock < item.quantity) {
          sendError(res, `Insufficient stock for product ${product.name}`);
          return;
        }

        const orderItem: IOrderItem = {
          product: item.productId,
          quantity: item.quantity,
          price: product.price,
          name: product.name
        };

        orderItems.push(orderItem);
        totalAmount += product.price * item.quantity;
      }

      // Create order
      const order = await Order.create({
        user: req.user._id,
        items: orderItems,
        totalAmount,
        shippingAddress,
        paymentMethod
      });

      // Update product stock
      for (const item of items) {
        await Product.findByIdAndUpdate(
          item.productId,
          { $inc: { stock: -item.quantity } }
        );
      }

      // Clear user's cart
      await Cart.findOneAndUpdate(
        { user: req.user._id },
        { items: [] }
      );

      await order.populate('items.product', 'name images');

      // Send order confirmation email
      try {
        const emailContent = emailTemplates.orderConfirmation(
          req.user.name,
          order.orderNumber,
          order.totalAmount
        );
        
        await sendEmail({
          to: req.user.email,
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text
        });
      } catch (error) {
        console.error('Failed to send order confirmation email:', error);
      }

      sendCreated(res, 'Order created successfully', order);
    } catch (error) {
      console.error('Create order error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getUserOrders(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { page = 1, limit = 10 } = req.query;
      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [orders, total] = await Promise.all([
        Order.find({ user: req.user._id })
          .populate('items.product', 'name images')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        Order.countDocuments({ user: req.user._id })
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Orders retrieved successfully', {
        orders,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get user orders error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getOrderById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;

      const order = await Order.findOne({
        _id: id,
        user: req.user._id
      }).populate('items.product', 'name images');

      if (!order) {
        sendNotFound(res, 'Order not found');
        return;
      }

      sendSuccess(res, 'Order retrieved successfully', order);
    } catch (error) {
      console.error('Get order by ID error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateOrderStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status } = req.body;

      const order = await Order.findByIdAndUpdate(
        id,
        { status },
        { new: true, runValidators: true }
      ).populate('user', 'name email')
       .populate('items.product', 'name images');

      if (!order) {
        sendNotFound(res, 'Order not found');
        return;
      }

      sendSuccess(res, 'Order status updated successfully', order);
    } catch (error) {
      console.error('Update order status error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getAllOrders(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        search
      } = req.query;

      const filter: any = {};

      if (status) {
        filter.status = status;
      }

      if (search) {
        filter.orderNumber = { $regex: search, $options: 'i' };
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [orders, total] = await Promise.all([
        Order.find(filter)
          .populate('user', 'name email phone')
          .populate('items.product', 'name images')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        Order.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Orders retrieved successfully', {
        orders,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get all orders error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
