import { Router } from 'express';
import { BrandingController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';

const router = Router();

// GET /api/branding
router.get(
  '/',
  BrandingController.getBrandingContent
);

// PUT /api/branding (admin only)
router.put(
  '/',
  authenticate,
  authorize('admin'),
  BrandingController.updateBrandingContent
);

// PUT /api/branding/:section (admin only)
router.put(
  '/:section',
  authenticate,
  authorize('admin'),
  BrandingController.updateBrandingSection
);

export default router;
