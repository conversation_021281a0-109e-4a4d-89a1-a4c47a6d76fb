import { Request, Response } from 'express';
import { CustomerNote, User } from '../models';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class CustomerNoteController {
  static async createCustomerNote(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params; // Customer ID
      const { content, isPrivate = true } = req.body;

      // Verify customer exists
      const customer = await User.findById(id);
      if (!customer) {
        sendNotFound(res, 'Customer not found');
        return;
      }

      const note = await CustomerNote.create({
        customer: id,
        content,
        createdBy: req.user._id,
        isPrivate
      });

      await note.populate('createdBy', 'name email');

      sendCreated(res, 'Customer note created successfully', note);
    } catch (error) {
      console.error('Create customer note error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getCustomerNotes(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params; // Customer ID
      const { page = 1, limit = 10 } = req.query;

      // Verify customer exists
      const customer = await User.findById(id);
      if (!customer) {
        sendNotFound(res, 'Customer not found');
        return;
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [notes, total] = await Promise.all([
        CustomerNote.find({ customer: id })
          .populate('createdBy', 'name email')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        CustomerNote.countDocuments({ customer: id })
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Customer notes retrieved successfully', {
        notes,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get customer notes error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateCustomerNote(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { noteId } = req.params;
      const { content, isPrivate } = req.body;

      const note = await CustomerNote.findById(noteId);
      if (!note) {
        sendNotFound(res, 'Note not found');
        return;
      }

      // Only allow the creator or admin to update the note
      if (note.createdBy.toString() !== req.user._id && req.user.role !== 'admin') {
        sendError(res, 'Not authorized to update this note', undefined, 403);
        return;
      }

      note.content = content || note.content;
      note.isPrivate = isPrivate !== undefined ? isPrivate : note.isPrivate;
      await note.save();

      await note.populate('createdBy', 'name email');

      sendSuccess(res, 'Customer note updated successfully', note);
    } catch (error) {
      console.error('Update customer note error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteCustomerNote(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { noteId } = req.params;

      const note = await CustomerNote.findById(noteId);
      if (!note) {
        sendNotFound(res, 'Note not found');
        return;
      }

      // Only allow the creator or admin to delete the note
      if (note.createdBy.toString() !== req.user._id && req.user.role !== 'admin') {
        sendError(res, 'Not authorized to delete this note', undefined, 403);
        return;
      }

      await CustomerNote.findByIdAndDelete(noteId);

      sendSuccess(res, 'Customer note deleted successfully');
    } catch (error) {
      console.error('Delete customer note error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
