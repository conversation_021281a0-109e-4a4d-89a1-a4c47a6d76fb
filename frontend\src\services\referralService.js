import apiService from './api.js'

/**
 * Referral service for handling referral program
 */
class ReferralService {
  /**
   * Get user referral data
   */
  async getUserReferralData() {
    try {
      const response = await apiService.get('/users/referral')
      return response
    } catch (error) {
      console.error('Get user referral data error:', error)
      throw error
    }
  }

  /**
   * Validate referral code
   */
  async validateReferralCode(code) {
    try {
      const response = await apiService.post('/referrals/validate', {
        code
      })
      return response
    } catch (error) {
      console.error('Validate referral code error:', error)
      throw error
    }
  }

  /**
   * Apply referral code during registration
   */
  async applyReferralCode(code) {
    try {
      const response = await apiService.post('/referrals/apply', {
        code
      })
      return response
    } catch (error) {
      console.error('Apply referral code error:', error)
      throw error
    }
  }

  /**
   * Generate referral link
   */
  async generateReferralLink() {
    try {
      const response = await apiService.post('/users/referral/generate-link')
      return response
    } catch (error) {
      console.error('Generate referral link error:', error)
      throw error
    }
  }

  /**
   * Get referral statistics
   */
  async getReferralStats() {
    try {
      const response = await apiService.get('/users/referral/stats')
      return response
    } catch (error) {
      console.error('Get referral stats error:', error)
      throw error
    }
  }

  /**
   * Get referral history
   */
  async getReferralHistory(params = {}) {
    try {
      const response = await apiService.get('/users/referral/history', params)
      return response
    } catch (error) {
      console.error('Get referral history error:', error)
      throw error
    }
  }

  /**
   * Get referred users
   */
  async getReferredUsers(params = {}) {
    try {
      const response = await apiService.get('/users/referral/referred-users', params)
      return response
    } catch (error) {
      console.error('Get referred users error:', error)
      throw error
    }
  }

  /**
   * Claim referral reward
   */
  async claimReferralReward(referralId) {
    try {
      const response = await apiService.post(`/users/referral/claim-reward/${referralId}`)
      return response
    } catch (error) {
      console.error('Claim referral reward error:', error)
      throw error
    }
  }

  /**
   * Share referral via email
   */
  async shareReferralEmail(emailData) {
    try {
      const response = await apiService.post('/users/referral/share/email', emailData)
      return response
    } catch (error) {
      console.error('Share referral email error:', error)
      throw error
    }
  }

  /**
   * Share referral via SMS
   */
  async shareReferralSMS(smsData) {
    try {
      const response = await apiService.post('/users/referral/share/sms', smsData)
      return response
    } catch (error) {
      console.error('Share referral SMS error:', error)
      throw error
    }
  }

  // Admin methods

  /**
   * Get all referrals (Admin)
   */
  async getAdminReferrals(params = {}) {
    try {
      const response = await apiService.get('/admin/referrals', params)
      return response
    } catch (error) {
      console.error('Get admin referrals error:', error)
      throw error
    }
  }

  /**
   * Get referral settings (Admin)
   */
  async getReferralSettings() {
    try {
      const response = await apiService.get('/admin/referrals/settings')
      return response
    } catch (error) {
      console.error('Get referral settings error:', error)
      throw error
    }
  }

  /**
   * Update referral settings (Admin)
   */
  async updateReferralSettings(settingsData) {
    try {
      const response = await apiService.put('/admin/referrals/settings', settingsData)
      return response
    } catch (error) {
      console.error('Update referral settings error:', error)
      throw error
    }
  }

  /**
   * Get referral analytics (Admin)
   */
  async getReferralAnalytics(params = {}) {
    try {
      const response = await apiService.get('/admin/referrals/analytics', params)
      return response
    } catch (error) {
      console.error('Get referral analytics error:', error)
      throw error
    }
  }

  /**
   * Approve referral reward (Admin)
   */
  async approveReferralReward(referralId) {
    try {
      const response = await apiService.put(`/admin/referrals/${referralId}/approve`)
      return response
    } catch (error) {
      console.error('Approve referral reward error:', error)
      throw error
    }
  }

  /**
   * Reject referral reward (Admin)
   */
  async rejectReferralReward(referralId, reason) {
    try {
      const response = await apiService.put(`/admin/referrals/${referralId}/reject`, {
        reason
      })
      return response
    } catch (error) {
      console.error('Reject referral reward error:', error)
      throw error
    }
  }

  // Utility methods

  /**
   * Generate shareable referral URL
   */
  generateReferralURL(referralCode, baseURL = window.location.origin) {
    return `${baseURL}?ref=${referralCode}`
  }

  /**
   * Extract referral code from URL
   */
  extractReferralCodeFromURL(url = window.location.href) {
    try {
      const urlObj = new URL(url)
      return urlObj.searchParams.get('ref')
    } catch (error) {
      console.error('Extract referral code error:', error)
      return null
    }
  }

  /**
   * Store referral code in local storage (for later use during registration)
   */
  storeReferralCode(code) {
    try {
      localStorage.setItem('referralCode', code)
      // Set expiration (30 days)
      const expiration = new Date()
      expiration.setDate(expiration.getDate() + 30)
      localStorage.setItem('referralCodeExpiration', expiration.toISOString())
    } catch (error) {
      console.error('Store referral code error:', error)
    }
  }

  /**
   * Get stored referral code
   */
  getStoredReferralCode() {
    try {
      const code = localStorage.getItem('referralCode')
      const expiration = localStorage.getItem('referralCodeExpiration')
      
      if (!code || !expiration) {
        return null
      }

      // Check if expired
      if (new Date() > new Date(expiration)) {
        this.clearStoredReferralCode()
        return null
      }

      return code
    } catch (error) {
      console.error('Get stored referral code error:', error)
      return null
    }
  }

  /**
   * Clear stored referral code
   */
  clearStoredReferralCode() {
    try {
      localStorage.removeItem('referralCode')
      localStorage.removeItem('referralCodeExpiration')
    } catch (error) {
      console.error('Clear stored referral code error:', error)
    }
  }

  /**
   * Generate social sharing URLs
   */
  generateSocialSharingURLs(referralURL, message = 'Check out this amazing service!') {
    const encodedURL = encodeURIComponent(referralURL)
    const encodedMessage = encodeURIComponent(message)

    return {
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedURL}`,
      twitter: `https://twitter.com/intent/tweet?url=${encodedURL}&text=${encodedMessage}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedURL}`,
      whatsapp: `https://wa.me/?text=${encodedMessage}%20${encodedURL}`,
      telegram: `https://t.me/share/url?url=${encodedURL}&text=${encodedMessage}`,
      email: `mailto:?subject=${encodedMessage}&body=${encodedURL}`
    }
  }

  /**
   * Copy referral link to clipboard
   */
  async copyReferralLink(referralURL) {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(referralURL)
        return true
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea')
        textArea.value = referralURL
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        const success = document.execCommand('copy')
        textArea.remove()
        return success
      }
    } catch (error) {
      console.error('Copy referral link error:', error)
      return false
    }
  }
}

// Create and export singleton instance
const referralService = new ReferralService()
export default referralService
