import { useState } from 'react'
import { FiMail, FiLock, FiUser, FiPhone, FiEye, FiEyeOff, FiAlertCircle } from 'react-icons/fi'
import { authService, referralService } from '../../services'
import { useBranding } from '../../contexts/BrandingContext'

const SignUp = ({ onNavigate, onSignUp }) => {
  const { branding } = useBranding()
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [agreedToTerms, setAgreedToTerms] = useState(false)
  const [error, setError] = useState('')
  const [validationErrors, setValidationErrors] = useState({})

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const validateForm = () => {
    const errors = {}

    // Validate first name
    if (!formData.firstName || formData.firstName.trim().length < 2) {
      errors.firstName = 'First name must be at least 2 characters long'
    }

    // Validate last name
    if (!formData.lastName || formData.lastName.trim().length < 2) {
      errors.lastName = 'Last name must be at least 2 characters long'
    }

    // Validate email
    if (!formData.email || !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address'
    }

    // Validate password
    if (!formData.password || formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters long'
    }

    // Validate password confirmation
    if (!formData.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }

    // Validate terms agreement
    if (!agreedToTerms) {
      errors.terms = 'Please agree to the Terms of Service and Privacy Policy'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    setValidationErrors({})

    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      // Check for referral code
      const referralCode = referralService.getStoredReferralCode()

      const userData = {
        name: `${formData.firstName} ${formData.lastName}`.trim(),
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        referralCode
      }

      const response = await authService.register(userData)

      if (response.success) {
        // Don't auto-remember for new signups
        onSignUp('user', false)
        onNavigate('user-dashboard')

        // Clear referral code after successful registration
        if (referralCode) {
          referralService.clearStoredReferralCode()
        }
      } else {
        // Handle specific validation errors from backend
        if (response.error && typeof response.error === 'string') {
          // Parse backend validation errors
          const errorMessages = response.error.split(', ')
          const backendErrors = {}

          errorMessages.forEach(msg => {
            if (msg.includes('Name must be')) {
              backendErrors.firstName = 'Name must be between 2 and 50 characters'
            } else if (msg.includes('Passwords do not match')) {
              backendErrors.confirmPassword = 'Passwords do not match'
            } else if (msg.includes('Email')) {
              backendErrors.email = msg
            } else if (msg.includes('Password')) {
              backendErrors.password = msg
            }
          })

          if (Object.keys(backendErrors).length > 0) {
            setValidationErrors(backendErrors)
          } else {
            setError(response.message || 'Registration failed')
          }
        } else {
          setError(response.message || 'Registration failed')
        }
      }
    } catch (error) {
      console.error('Registration error:', error)
      setError(error.message || 'Registration failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{branding.content.signupTitle}</h1>
          <p className="text-gray-600">{branding.content.signupSubtitle}</p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-lg sm:rounded-2xl sm:px-10">
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
              <FiAlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                  First Name
                </label>
                <div className="relative">
                  <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    id="firstName"
                    name="firstName"
                    type="text"
                    required
                    value={formData.firstName}
                    onChange={handleChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                    onFocus={(e) => {
                      e.target.style.outline = 'none'
                      e.target.style.borderColor = branding.colors.secondary
                      e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#d1d5db'
                      e.target.style.boxShadow = 'none'
                    }}
                    placeholder="First name"
                  />
                </div>
                {validationErrors.firstName && (
                  <p className="mt-1 text-sm text-red-600">{validationErrors.firstName}</p>
                )}
              </div>

              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                  Last Name
                </label>
                <div className="relative">
                  <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    id="lastName"
                    name="lastName"
                    type="text"
                    required
                    value={formData.lastName}
                    onChange={handleChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                    onFocus={(e) => {
                      e.target.style.outline = 'none'
                      e.target.style.borderColor = branding.colors.secondary
                      e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = '#d1d5db'
                      e.target.style.boxShadow = 'none'
                    }}
                    placeholder="Last name"
                  />
                </div>
                {validationErrors.lastName && (
                  <p className="mt-1 text-sm text-red-600">{validationErrors.lastName}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <div className="relative">
                <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.borderColor = branding.colors.secondary
                    e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db'
                    e.target.style.boxShadow = 'none'
                  }}
                  placeholder="Enter your email"
                />
              </div>
              {validationErrors.email && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.email}</p>
              )}
            </div>

            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <div className="relative">
                <FiPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.borderColor = branding.colors.secondary
                    e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db'
                    e.target.style.boxShadow = 'none'
                  }}
                  placeholder="Enter your phone number"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <FiLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.borderColor = branding.colors.secondary
                    e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db'
                    e.target.style.boxShadow = 'none'
                  }}
                  placeholder="Create a password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  {showPassword ? <FiEyeOff className="w-5 h-5" /> : <FiEye className="w-5 h-5" />}
                </button>
              </div>
              {validationErrors.password && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.password}</p>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                Confirm Password
              </label>
              <div className="relative">
                <FiLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.borderColor = branding.colors.secondary
                    e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db'
                    e.target.style.boxShadow = 'none'
                  }}
                  placeholder="Confirm your password"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  {showConfirmPassword ? <FiEyeOff className="w-5 h-5" /> : <FiEye className="w-5 h-5" />}
                </button>
              </div>
              {validationErrors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.confirmPassword}</p>
              )}
            </div>

            <div className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id="terms"
                  name="terms"
                  type="checkbox"
                  checked={agreedToTerms}
                  onChange={(e) => setAgreedToTerms(e.target.checked)}
                  className="h-4 w-4 border-gray-300 rounded"
                  style={{ accentColor: branding.colors.secondary }}
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.boxShadow = 'none'
                  }}
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="terms" className="text-gray-700">
                  I agree to the{' '}
                  <button
                    className="transition-colors duration-200"
                    style={{ color: branding.colors.secondary }}
                    onMouseEnter={(e) => e.target.style.color = branding.colors.accent}
                    onMouseLeave={(e) => e.target.style.color = branding.colors.secondary}
                  >
                    Terms of Service
                  </button>{' '}
                  and{' '}
                  <button
                    className="transition-colors duration-200"
                    style={{ color: branding.colors.secondary }}
                    onMouseEnter={(e) => e.target.style.color = branding.colors.accent}
                    onMouseLeave={(e) => e.target.style.color = branding.colors.secondary}
                  >
                    Privacy Policy
                  </button>
                </label>
              </div>
              {validationErrors.terms && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.terms}</p>
              )}
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading || !agreedToTerms}
                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ backgroundColor: branding.colors.secondary }}
                onMouseEnter={(e) => !isLoading && (e.target.style.backgroundColor = branding.colors.accent)}
                onMouseLeave={(e) => !isLoading && (e.target.style.backgroundColor = branding.colors.secondary)}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {branding.content.creatingAccountText}
                  </div>
                ) : (
                  branding.content.createAccountButton
                )}
              </button>
            </div>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              {branding.content.alreadyHaveAccountText}{' '}
              <button
                onClick={() => onNavigate('login')}
                className="font-medium transition-colors duration-200"
                style={{ color: branding.colors.secondary }}
                onMouseEnter={(e) => e.target.style.color = branding.colors.accent}
                onMouseLeave={(e) => e.target.style.color = branding.colors.secondary}
              >
                {branding.content.signInHereLink}
              </button>
            </p>
          </div>
        </div>
      </div>

      {/* Additional Links */}
      <div className="mt-8 text-center">
        <div className="flex justify-center space-x-6 text-sm text-gray-600">
          <button
            className="transition-colors duration-200"
            onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
            onMouseLeave={(e) => e.target.style.color = ''}
          >
            {branding.content.privacyPolicyLink}
          </button>
          <button
            className="transition-colors duration-200"
            onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
            onMouseLeave={(e) => e.target.style.color = ''}
          >
            {branding.content.termsOfServiceLink}
          </button>
          <button
            className="transition-colors duration-200"
            onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
            onMouseLeave={(e) => e.target.style.color = ''}
          >
            {branding.content.helpCenterLink}
          </button>
        </div>
      </div>
    </div>
  )
}

export default SignUp
