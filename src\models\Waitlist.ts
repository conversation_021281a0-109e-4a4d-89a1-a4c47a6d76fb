import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IWaitlist extends Document {
  _id: string;
  name: string;
  email: string;
  phone: string;
  service: Types.ObjectId;
  preferredDates: Date[];
  status: 'waiting' | 'contacted' | 'booked' | 'expired';
  notes: string;
  createdAt: Date;
  updatedAt: Date;
}

const waitlistSchema = new Schema<IWaitlist>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  phone: {
    type: String,
    required: true,
    trim: true
  },
  service: {
    type: Schema.Types.ObjectId,
    ref: 'Service',
    required: true
  },
  preferredDates: [{
    type: Date,
    required: true
  }],
  status: {
    type: String,
    enum: ['waiting', 'contacted', 'booked', 'expired'],
    default: 'waiting'
  },
  notes: {
    type: String,
    trim: true,
    maxlength: 500,
    default: ''
  }
}, {
  timestamps: true
});

// Index for better query performance
waitlistSchema.index({ service: 1 });
waitlistSchema.index({ status: 1 });
waitlistSchema.index({ email: 1 });
waitlistSchema.index({ createdAt: -1 });

export const Waitlist = mongoose.model<IWaitlist>('Waitlist', waitlistSchema);
