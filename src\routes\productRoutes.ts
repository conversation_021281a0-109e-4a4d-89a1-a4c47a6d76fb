import { Router } from 'express';
import { ProductController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation, paginationValidation } from '../utils/validation';

const router = Router();

// GET /api/products
router.get(
  '/',
  validate(paginationValidation),
  ProductController.getAllProducts
);

// GET /api/products/categories
router.get(
  '/categories',
  ProductController.getProductCategories
);

// GET /api/products/:id
router.get(
  '/:id',
  validate(mongoIdValidation()),
  ProductController.getProductById
);

// Admin routes
// POST /api/products (admin only)
router.post(
  '/',
  authenticate,
  authorize('admin'),
  ProductController.createProduct
);

// PUT /api/products/:id (admin only)
router.put(
  '/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  ProductController.updateProduct
);

// DELETE /api/products/:id (admin only)
router.delete(
  '/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  ProductController.deleteProduct
);

export default router;
