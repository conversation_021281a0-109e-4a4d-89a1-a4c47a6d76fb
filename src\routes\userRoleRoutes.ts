import { Router } from 'express';
import { User<PERSON>oleController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation, paginationValidation } from '../utils/validation';

const router = Router();

// All routes require admin authentication
router.use(authenticate);
router.use(authorize('admin'));

// GET /api/admin/users
router.get(
  '/',
  validate(paginationValidation),
  UserRoleController.getAllUsers
);

// GET /api/admin/users/:id
router.get(
  '/:id',
  validate(mongoIdValidation()),
  UserRoleController.getUserById
);

// PUT /api/admin/users/:id/role
router.put(
  '/:id/role',
  validate(mongoIdValidation()),
  UserRoleController.updateUserRole
);

// PUT /api/admin/users/:id/deactivate
router.put(
  '/:id/deactivate',
  validate(mongoIdValidation()),
  UserRoleController.deactivateUser
);

// PUT /api/admin/users/:id/activate
router.put(
  '/:id/activate',
  validate(mongoIdValidation()),
  UserRoleController.activateUser
);

export default router;
