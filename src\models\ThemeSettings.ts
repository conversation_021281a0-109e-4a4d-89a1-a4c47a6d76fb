import mongoose, { Schema, Document } from 'mongoose';

export interface IThemeSettings extends Document {
  _id: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  fontFamily: string;
  fontSize: string;
  buttonStyle: string;
  cardStyle: string;
  borderRadius: string;
  spacing: string;
  customCSS: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const themeSettingsSchema = new Schema<IThemeSettings>({
  primaryColor: {
    type: String,
    required: true,
    default: '#007bff',
    match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  },
  secondaryColor: {
    type: String,
    required: true,
    default: '#6c757d',
    match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  },
  accentColor: {
    type: String,
    required: true,
    default: '#28a745',
    match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  },
  backgroundColor: {
    type: String,
    required: true,
    default: '#ffffff',
    match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  },
  textColor: {
    type: String,
    required: true,
    default: '#333333',
    match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  },
  fontFamily: {
    type: String,
    required: true,
    default: 'Inter, sans-serif',
    trim: true
  },
  fontSize: {
    type: String,
    required: true,
    default: '16px',
    trim: true
  },
  buttonStyle: {
    type: String,
    required: true,
    default: 'rounded',
    enum: ['rounded', 'square', 'pill']
  },
  cardStyle: {
    type: String,
    required: true,
    default: 'elevated',
    enum: ['elevated', 'outlined', 'filled']
  },
  borderRadius: {
    type: String,
    required: true,
    default: '8px',
    trim: true
  },
  spacing: {
    type: String,
    required: true,
    default: 'normal',
    enum: ['compact', 'normal', 'spacious']
  },
  customCSS: {
    type: String,
    default: '',
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

export const ThemeSettings = mongoose.model<IThemeSettings>('ThemeSettings', themeSettingsSchema);
