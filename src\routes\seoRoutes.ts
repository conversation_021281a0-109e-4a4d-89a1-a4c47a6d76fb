import { Router } from 'express';
import { SEOController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';

const router = Router();

// GET /api/admin/seo (admin only)
router.get(
  '/',
  authenticate,
  authorize('admin'),
  SEOController.getSEOSettings
);

// PUT /api/admin/seo (admin only)
router.put(
  '/',
  authenticate,
  authorize('admin'),
  SEOController.updateSEOSettings
);

export default router;
