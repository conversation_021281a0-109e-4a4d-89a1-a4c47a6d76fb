import mongoose, { Schema, Document, Types } from 'mongoose';

export interface ILoyaltyPoints extends Document {
  _id: string;
  user: Types.ObjectId;
  points: number;
  totalEarned: number;
  totalRedeemed: number;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  createdAt: Date;
  updatedAt: Date;
}

export interface ILoyaltyTransaction extends Document {
  _id: string;
  user: Types.ObjectId;
  type: 'earned' | 'redeemed';
  points: number;
  description: string;
  orderId?: Types.ObjectId;
  rewardId?: Types.ObjectId;
  createdAt: Date;
}

export interface ILoyaltyReward extends Document {
  _id: string;
  name: string;
  description: string;
  pointsCost: number;
  value: number;
  type: 'discount' | 'freeService' | 'freeProduct' | 'cashback';
  isActive: boolean;
  expiryDays: number;
  createdAt: Date;
  updatedAt: Date;
}

const loyaltyPointsSchema = new Schema<ILoyaltyPoints>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  points: {
    type: Number,
    default: 0,
    min: 0
  },
  totalEarned: {
    type: Number,
    default: 0,
    min: 0
  },
  totalRedeemed: {
    type: Number,
    default: 0,
    min: 0
  },
  tier: {
    type: String,
    enum: ['bronze', 'silver', 'gold', 'platinum'],
    default: 'bronze'
  }
}, {
  timestamps: true
});

const loyaltyTransactionSchema = new Schema<ILoyaltyTransaction>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['earned', 'redeemed'],
    required: true
  },
  points: {
    type: Number,
    required: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  orderId: {
    type: Schema.Types.ObjectId,
    ref: 'Order'
  },
  rewardId: {
    type: Schema.Types.ObjectId,
    ref: 'LoyaltyReward'
  }
}, {
  timestamps: true
});

const loyaltyRewardSchema = new Schema<ILoyaltyReward>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500
  },
  pointsCost: {
    type: Number,
    required: true,
    min: 1
  },
  value: {
    type: Number,
    required: true,
    min: 0
  },
  type: {
    type: String,
    enum: ['discount', 'freeService', 'freeProduct', 'cashback'],
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  expiryDays: {
    type: Number,
    default: 30,
    min: 1
  }
}, {
  timestamps: true
});

// Update tier based on total earned points
loyaltyPointsSchema.pre('save', function(next) {
  if (this.totalEarned >= 10000) {
    this.tier = 'platinum';
  } else if (this.totalEarned >= 5000) {
    this.tier = 'gold';
  } else if (this.totalEarned >= 1000) {
    this.tier = 'silver';
  } else {
    this.tier = 'bronze';
  }
  next();
});

// Index for better query performance
loyaltyPointsSchema.index({ user: 1 });
loyaltyTransactionSchema.index({ user: 1 });
loyaltyTransactionSchema.index({ createdAt: -1 });
loyaltyRewardSchema.index({ isActive: 1 });
loyaltyRewardSchema.index({ pointsCost: 1 });

export const LoyaltyPoints = mongoose.model<ILoyaltyPoints>('LoyaltyPoints', loyaltyPointsSchema);
export const LoyaltyTransaction = mongoose.model<ILoyaltyTransaction>('LoyaltyTransaction', loyaltyTransactionSchema);
export const LoyaltyReward = mongoose.model<ILoyaltyReward>('LoyaltyReward', loyaltyRewardSchema);
