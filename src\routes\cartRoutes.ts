import { Router } from 'express';
import { CartController } from '../controllers';
import { authenticate } from '../middleware/auth';
import { validate } from '../middleware/validation';
import {
  addToCartValidation,
  updateCartItemValidation,
  mongoIdValidation
} from '../utils/validation';

const router = Router();

// GET /api/cart
router.get(
  '/',
  authenticate,
  CartController.getCart
);

// POST /api/cart/items
router.post(
  '/items',
  authenticate,
  validate(addToCartValidation),
  CartController.addToCart
);

// PUT /api/cart/items/:itemId
router.put(
  '/items/:itemId',
  authenticate,
  validate(updateCartItemValidation),
  CartController.updateCartItem
);

// DELETE /api/cart/items/:itemId
router.delete(
  '/items/:itemId',
  authenticate,
  validate(mongoIdValidation('itemId')),
  CartController.removeFromCart
);

// DELETE /api/cart
router.delete(
  '/',
  authenticate,
  CartController.clearCart
);

export default router;
