import { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { brandingService } from '../services'
import { BUSINESS_INFO, FALLBACK_IMAGES } from '../utils/constants'

// Default branding configuration (fallback)
const DEFAULT_BRANDING = {
  // Business Information
  businessName: BUSINESS_INFO.name,
  tagline: BUSINESS_INFO.tagline,
  description: 'Professional locs and natural hair care services. Specializing in micro locs, traditional locs, and natural hair maintenance.',
  
  // Contact Information
  phone: BUSINESS_INFO.phone,
  email: BUSINESS_INFO.email,
  address: BUSINESS_INFO.address,
  social: BUSINESS_INFO.social,
  hours: BUSINESS_INFO.hours,
  
  // Brand Colors
  colors: {
    primary: '#008000',      // Green
    secondary: '#f3d016',    // Gold/Yellow
    accent: '#006600',       // Dark Green
    background: '#ffffff',   // White
    text: '#000000',         // Black
    textSecondary: '#666666' // Gray
  },
  
  // Typography
  fonts: {
    primary: 'Inter, system-ui, sans-serif',
    secondary: 'Inter, system-ui, sans-serif',
    heading: 'Inter, system-ui, sans-serif'
  },
  
  // Images and Media
  images: {
    logo: FALLBACK_IMAGES.logo,
    hero: FALLBACK_IMAGES.hero,
    favicon: FALLBACK_IMAGES.logo,
    placeholder: FALLBACK_IMAGES.placeholder
  },
  
  // Content
  content: {
    heroTitle: 'Beautiful Locs, Beautiful You',
    heroSubtitle: 'Professional loc services and natural hair care by Tina',
    aboutTitle: 'About Goldie Locs',
    aboutText: 'With years of experience in natural hair care and loc maintenance, Tina provides personalized services to help you achieve and maintain beautiful, healthy locs.',
    servicesTitle: 'Our Services',
    servicesSubtitle: 'Professional hair care services tailored to your needs'
  },
  
  // SEO and Meta
  seo: {
    title: 'Goldie Locs By Tina - Professional Loc Services',
    description: 'Professional locs and natural hair care services in Atlanta, GA. Specializing in micro locs, traditional locs, and natural hair maintenance.',
    keywords: 'locs, dreadlocks, natural hair, hair care, Atlanta, micro locs, loc maintenance'
  },
  
  // Features and Settings
  features: {
    onlineBooking: true,
    ecommerce: true,
    loyaltyProgram: true,
    giftCards: true,
    reviews: true,
    blog: false
  }
}

const BrandingContext = createContext({
  branding: DEFAULT_BRANDING,
  isLoading: false,
  error: null,
  refreshBranding: () => {},
  updateBranding: () => {}
})

export const BrandingProvider = ({ children }) => {
  const [branding, setBranding] = useState(DEFAULT_BRANDING)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load branding data from API
  const loadBranding = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await brandingService.getCompleteBranding()
      
      if (response.success && response.data) {
        const { branding: brandingData, business, theme, site } = response.data
        
        // Merge API data with defaults, keeping defaults as fallback
        const mergedBranding = {
          ...DEFAULT_BRANDING,
          
          // Business info from API
          ...(business && {
            businessName: business.name || DEFAULT_BRANDING.businessName,
            tagline: business.tagline || DEFAULT_BRANDING.tagline,
            description: business.description || DEFAULT_BRANDING.description,
            phone: business.phone || DEFAULT_BRANDING.phone,
            email: business.email || DEFAULT_BRANDING.email,
            address: business.address || DEFAULT_BRANDING.address,
            social: { ...DEFAULT_BRANDING.social, ...business.social },
            hours: { ...DEFAULT_BRANDING.hours, ...business.hours }
          }),
          
          // Theme colors from API
          ...(theme && {
            colors: { ...DEFAULT_BRANDING.colors, ...theme.colors },
            fonts: { ...DEFAULT_BRANDING.fonts, ...theme.fonts }
          }),
          
          // Branding assets from API
          ...(brandingData && {
            images: {
              ...DEFAULT_BRANDING.images,
              logo: brandingData.logo || DEFAULT_BRANDING.images.logo,
              hero: brandingData.heroImage || DEFAULT_BRANDING.images.hero,
              favicon: brandingData.favicon || DEFAULT_BRANDING.images.favicon
            },
            content: {
              ...DEFAULT_BRANDING.content,
              heroTitle: brandingData.heroTitle || DEFAULT_BRANDING.content.heroTitle,
              heroSubtitle: brandingData.heroSubtitle || DEFAULT_BRANDING.content.heroSubtitle,
              aboutTitle: brandingData.aboutTitle || DEFAULT_BRANDING.content.aboutTitle,
              aboutText: brandingData.aboutText || DEFAULT_BRANDING.content.aboutText
            }
          }),
          
          // Site settings from API
          ...(site && {
            seo: { ...DEFAULT_BRANDING.seo, ...site.seo },
            features: { ...DEFAULT_BRANDING.features, ...site.features }
          })
        }
        
        setBranding(mergedBranding)
      } else {
        // API failed, use defaults
        console.warn('Failed to load branding from API, using defaults')
        setBranding(DEFAULT_BRANDING)
      }
    } catch (error) {
      console.error('Error loading branding:', error)
      setError('Failed to load branding configuration')
      // Use defaults on error
      setBranding(DEFAULT_BRANDING)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Load branding on mount
  useEffect(() => {
    loadBranding()
  }, [loadBranding])

  // Refresh branding data
  const refreshBranding = useCallback(() => {
    loadBranding()
  }, [loadBranding])

  // Update branding (for admin use)
  const updateBranding = useCallback((newBranding) => {
    setBranding(prev => ({ ...prev, ...newBranding }))
  }, [])

  const value = {
    branding,
    isLoading,
    error,
    refreshBranding,
    updateBranding
  }

  return (
    <BrandingContext.Provider value={value}>
      {children}
    </BrandingContext.Provider>
  )
}

// Custom hook to use branding context
export const useBranding = () => {
  const context = useContext(BrandingContext)
  if (!context) {
    throw new Error('useBranding must be used within a BrandingProvider')
  }
  return context
}

export default BrandingContext
