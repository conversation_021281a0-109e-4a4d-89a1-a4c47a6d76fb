import apiService from './api.js'

/**
 * User service for handling user profile and preferences
 */
class UserService {
  /**
   * Get user profile
   */
  async getProfile() {
    try {
      const response = await apiService.get('/users/profile')
      return response
    } catch (error) {
      console.error('Get profile error:', error)
      throw error
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData) {
    try {
      const response = await apiService.put('/users/profile', profileData)
      return response
    } catch (error) {
      console.error('Update profile error:', error)
      throw error
    }
  }

  /**
   * Upload profile picture
   */
  async uploadProfilePicture(file) {
    try {
      const response = await apiService.upload('/users/profile/picture', file)
      return response
    } catch (error) {
      console.error('Upload profile picture error:', error)
      throw error
    }
  }

  /**
   * Get user favorites
   */
  async getFavorites() {
    try {
      const response = await apiService.get('/users/favorites')
      return response
    } catch (error) {
      console.error('Get favorites error:', error)
      throw error
    }
  }

  /**
   * Add to favorites
   */
  async addToFavorites(productId) {
    try {
      const response = await apiService.post('/users/favorites', {
        productId
      })
      return response
    } catch (error) {
      console.error('Add to favorites error:', error)
      throw error
    }
  }

  /**
   * Remove from favorites
   */
  async removeFromFavorites(productId) {
    try {
      const response = await apiService.delete(`/users/favorites/${productId}`)
      return response
    } catch (error) {
      console.error('Remove from favorites error:', error)
      throw error
    }
  }

  /**
   * Get notification preferences
   */
  async getNotificationPreferences() {
    try {
      const response = await apiService.get('/users/notification-preferences')
      return response
    } catch (error) {
      console.error('Get notification preferences error:', error)
      throw error
    }
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(preferences) {
    try {
      const response = await apiService.put('/users/notification-preferences', preferences)
      return response
    } catch (error) {
      console.error('Update notification preferences error:', error)
      throw error
    }
  }

  /**
   * Get user addresses
   */
  async getAddresses() {
    try {
      const response = await apiService.get('/users/addresses')
      return response
    } catch (error) {
      console.error('Get addresses error:', error)
      throw error
    }
  }

  /**
   * Add new address
   */
  async addAddress(addressData) {
    try {
      const response = await apiService.post('/users/addresses', addressData)
      return response
    } catch (error) {
      console.error('Add address error:', error)
      throw error
    }
  }

  /**
   * Update address
   */
  async updateAddress(addressId, addressData) {
    try {
      const response = await apiService.put(`/users/addresses/${addressId}`, addressData)
      return response
    } catch (error) {
      console.error('Update address error:', error)
      throw error
    }
  }

  /**
   * Delete address
   */
  async deleteAddress(addressId) {
    try {
      const response = await apiService.delete(`/users/addresses/${addressId}`)
      return response
    } catch (error) {
      console.error('Delete address error:', error)
      throw error
    }
  }

  /**
   * Get user loyalty points
   */
  async getLoyaltyPoints() {
    try {
      const response = await apiService.get('/users/loyalty')
      return response
    } catch (error) {
      console.error('Get loyalty points error:', error)
      throw error
    }
  }

  /**
   * Get loyalty transactions
   */
  async getLoyaltyTransactions(params = {}) {
    try {
      const response = await apiService.get('/users/loyalty/transactions', params)
      return response
    } catch (error) {
      console.error('Get loyalty transactions error:', error)
      throw error
    }
  }

  /**
   * Redeem loyalty reward
   */
  async redeemLoyaltyReward(rewardId) {
    try {
      const response = await apiService.post('/users/loyalty/redeem', {
        rewardId
      })
      return response
    } catch (error) {
      console.error('Redeem loyalty reward error:', error)
      throw error
    }
  }

  /**
   * Get user referral data
   */
  async getReferralData() {
    try {
      const response = await apiService.get('/users/referral')
      return response
    } catch (error) {
      console.error('Get referral data error:', error)
      throw error
    }
  }

  /**
   * Get user gift cards
   */
  async getGiftCards() {
    try {
      const response = await apiService.get('/gift-cards/user')
      return response
    } catch (error) {
      console.error('Get gift cards error:', error)
      throw error
    }
  }

  /**
   * Check gift card balance
   */
  async checkGiftCardBalance(code) {
    try {
      const response = await apiService.get(`/gift-cards/${code}/balance`)
      return response
    } catch (error) {
      console.error('Check gift card balance error:', error)
      throw error
    }
  }

  /**
   * Delete user account
   */
  async deleteAccount(password) {
    try {
      const response = await apiService.delete('/users/account', {
        body: JSON.stringify({ password })
      })
      return response
    } catch (error) {
      console.error('Delete account error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const userService = new UserService()
export default userService
