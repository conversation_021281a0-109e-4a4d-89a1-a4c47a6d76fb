import { useState } from 'react'
import { FiShoppingCart, FiStar, FiFilter, FiSearch } from 'react-icons/fi'

const Shop = ({ onNavigate, onSelectProduct }) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState('name')

  const categories = [
    { id: 'all', name: 'All Products' },
    { id: 'shampoo', name: 'Shampoos' },
    { id: 'conditioner', name: 'Conditioners' },
    { id: 'oil', name: 'Oils & Serums' },
    { id: 'styling', name: 'Styling Products' },
    { id: 'tools', name: 'Tools & Accessories' }
  ]

  const products = [
    {
      id: 1,
      name: "Loc Maintenance Shampoo",
      category: "shampoo",
      price: 24.99,
      rating: 4.8,
      reviews: 156,
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600",
      description: "Gentle cleansing shampoo specifically formulated for locs"
    },
    {
      id: 2,
      name: "Natural Hair Oil Blend",
      category: "oil",
      price: 18.99,
      rating: 4.9,
      reviews: 203,
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600",
      description: "Nourishing oil blend for healthy scalp and hair growth"
    },
    {
      id: 3,
      name: "Loc Twisting Gel",
      category: "styling",
      price: 16.99,
      rating: 4.7,
      reviews: 89,
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600",
      description: "Strong hold gel for loc maintenance and styling"
    },
    {
      id: 4,
      name: "Deep Conditioning Mask",
      category: "conditioner",
      price: 28.99,
      rating: 4.9,
      reviews: 134,
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600",
      description: "Intensive moisture treatment for dry and damaged hair"
    },
    {
      id: 5,
      name: "Loc Maintenance Tool Kit",
      category: "tools",
      price: 45.99,
      rating: 4.6,
      reviews: 67,
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600",
      description: "Complete set of tools for loc maintenance at home"
    },
    {
      id: 6,
      name: "Scalp Moisturizing Serum",
      category: "oil",
      price: 22.99,
      rating: 4.8,
      reviews: 112,
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600",
      description: "Lightweight serum for scalp hydration and comfort"
    },
    {
      id: 7,
      name: "Clarifying Shampoo",
      category: "shampoo",
      price: 21.99,
      rating: 4.5,
      reviews: 78,
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600",
      description: "Deep cleansing shampoo for removing buildup"
    },
    {
      id: 8,
      name: "Leave-In Conditioner",
      category: "conditioner",
      price: 19.99,
      rating: 4.7,
      reviews: 145,
      image: "https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600",
      description: "Daily moisture and protection for all hair types"
    }
  ]

  const filteredProducts = products
    .filter(product =>
      (selectedCategory === 'all' || product.category === selectedCategory) &&
      product.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price
        case 'price-high':
          return b.price - a.price
        case 'rating':
          return b.rating - a.rating
        default:
          return a.name.localeCompare(b.name)
      }
    })

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Hair Care Shop
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Premium hair care products specially curated for locs and natural hair maintenance.
          </p>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-2xl p-6 mb-8 shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                onFocus={(e) => {
                  e.target.style.outline = 'none'
                  e.target.style.borderColor = '#f3d016'
                  e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db'
                  e.target.style.boxShadow = 'none'
                }}
              />
            </div>

            {/* Category Filter */}
            <div className="relative">
              <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 appearance-none"
                onFocus={(e) => {
                  e.target.style.outline = 'none'
                  e.target.style.borderColor = '#f3d016'
                  e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db'
                  e.target.style.boxShadow = 'none'
                }}
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = '#f3d016'
                e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            >
              <option value="name">Sort by Name</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Highest Rated</option>
            </select>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <div key={product.id} className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
              <button
                onClick={() => {
                  onSelectProduct(product.id)
                  onNavigate('product')
                }}
                className="w-full"
              >
                <div className="aspect-square bg-gray-200">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>
              </button>
              <div className="p-6">
                <button
                  onClick={() => {
                    onSelectProduct(product.id)
                    onNavigate('product')
                  }}
                  className="w-full text-left"
                >
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 transition-colors duration-200"
                      onMouseEnter={(e) => e.target.style.color = '#f3d016'}
                      onMouseLeave={(e) => e.target.style.color = ''}
                  >
                    {product.name}
                  </h3>
                </button>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {product.description}
                </p>

                <div className="flex items-center mb-3">
                  <div className="flex mr-2" style={{ color: '#FFFF00' }}>
                    {[...Array(5)].map((_, i) => (
                      <FiStar
                        key={i}
                        className={`w-4 h-4 ${
                          i < Math.floor(product.rating) ? 'fill-current' : ''
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    {product.rating} ({product.reviews})
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-xl font-bold text-gray-900">
                    ${product.price}
                  </span>
                  <button
                    className="text-white p-2 rounded-lg transition-colors duration-200"
                    style={{ backgroundColor: '#f3d016' }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = '#d4b014'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#f3d016'}
                  >
                    <FiShoppingCart className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <FiSearch className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria</p>
          </div>
        )}

        {/* Newsletter Signup */}
        <div className="mt-16 rounded-2xl p-8 lg:p-12 text-center" style={{ backgroundColor: '#f3d016' }}>
          <h2 className="text-3xl font-bold text-white mb-4">
            Stay Updated on New Products
          </h2>
          <p className="text-yellow-100 mb-6 max-w-2xl mx-auto">
            Be the first to know about new arrivals, exclusive deals, and hair care tips.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = '#f3d016'
                e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
            <button
              className="bg-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
              style={{ color: '#f3d016' }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#F0FFF0'}
              onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
            >
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Shop
