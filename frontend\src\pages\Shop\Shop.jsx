import { useState, useEffect, useCallback, useRef } from 'react'
import { FiShoppingCart, FiStar, FiFilter, FiSearch, FiAlertCircle } from 'react-icons/fi'
import { productService, cartService } from '../../services'
import { FALLBACK_IMAGES } from '../../utils/constants'
import { useApiDataFetching } from '../../hooks/useDataFetching'
import { useBranding } from '../../contexts/BrandingContext'
import Loading from '../../components/Loading'

const Shop = ({ onNavigate, onSelectProduct }) => {
  const { branding } = useBranding()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState('name')
  const [addingToCart, setAddingToCart] = useState(null)

  // Use ref to prevent loading state from causing infinite loops
  const loadingRef = useRef(false)

  // Memoized function to load initial data - excludes loading from dependencies
  const loadInitialData = useCallback(async () => {
    if (loadingRef.current) return

    try {
      loadingRef.current = true

      // Load categories and products in parallel
      const [categoriesResponse, productsResponse] = await Promise.all([
        productService.getCategories(),
        productService.getProducts()
      ])

      const results = { categories: [], products: [] }

      if (categoriesResponse.success) {
        results.categories = [
          { id: 'all', name: 'All Products' },
          ...categoriesResponse.data
        ]
      }

      if (productsResponse.success) {
        results.products = productsResponse.data
      }

      return results
    } catch (error) {
      console.error('Error loading shop data:', error)
      throw error
    } finally {
      loadingRef.current = false
    }
  }, []) // No dependencies that would cause recreation

  // Use the custom hook for initial data loading
  const {
    data: initialData,
    loading: isLoading,
    error,
    refetch: refetchInitialData
  } = useApiDataFetching(
    '/products/initial',
    {},
    loadInitialData,
    []
  )

  // Extract data from the hook result
  const categories = initialData?.categories || []
  const initialProducts = initialData?.products || []

  // Memoized function to load filtered products - excludes loading from dependencies
  const loadFilteredProducts = useCallback(async () => {
    const params = {}

    if (searchTerm) {
      params.search = searchTerm
    }

    if (selectedCategory !== 'all') {
      params.category = selectedCategory
    }

    if (sortBy !== 'name') {
      params.sort = sortBy
    }

    const response = await productService.getProducts(params)
    if (response.success) {
      return response.data
    }
    throw new Error('Failed to load filtered products')
  }, [searchTerm, selectedCategory, sortBy]) // Only include actual dependencies

  // Use the custom hook for filtered products
  const {
    data: filteredProductsData,
    loading: isFilterLoading,
    refetch: refetchFilteredProducts
  } = useApiDataFetching(
    '/products/filtered',
    { searchTerm, selectedCategory, sortBy },
    loadFilteredProducts,
    [searchTerm, selectedCategory, sortBy]
  )

  // Use filtered products if available, otherwise use initial products
  // Ensure products is always an array to prevent filter errors
  const products = Array.isArray(filteredProductsData) ? filteredProductsData :
                   Array.isArray(initialProducts) ? initialProducts : []

  // Debounced effect for search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm || selectedCategory !== 'all' || sortBy !== 'name') {
        refetchFilteredProducts()
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchTerm, selectedCategory, sortBy, refetchFilteredProducts])

  // Handle add to cart
  const handleAddToCart = async (product) => {
    try {
      setAddingToCart(product.id)

      const cartItem = {
        productId: product.id,
        quantity: 1
      }

      const response = await cartService.addToCart(cartItem)

      if (response.success) {
        // Show success notification
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'success',
            message: `${product.name} added to cart!`
          }
        }))
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'Failed to add item to cart. Please try again.'
        }
      }))
    } finally {
      setAddingToCart(null)
    }
  }

  // Client-side filtering for immediate response (server-side filtering happens in useEffect)
  const filteredProducts = Array.isArray(products) ? products.filter(product => {
    // Ensure product exists and has required properties
    if (!product || typeof product !== 'object') return false

    const matchesSearch = !searchTerm ||
      (product.name && product.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = selectedCategory === 'all' ||
      product.category === selectedCategory ||
      product.categoryId === selectedCategory

    return matchesSearch && matchesCategory
  }) : []

  // Show loading state
  if (isLoading) {
    return <Loading />
  }

  // Show error state with retry option
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <FiAlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Products</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => refetchInitialData()}
              className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {branding.content.shopTitle}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {branding.content.shopSubtitle}
          </p>
          <p className="text-lg text-gray-500 max-w-2xl mx-auto mt-4">
            {branding.content.shopDescription}
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
            <FiAlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}

        {/* Filters and Search */}
        <div className="bg-white rounded-2xl p-6 mb-8 shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                onFocus={(e) => {
                  e.target.style.outline = 'none'
                  e.target.style.borderColor = branding.colors.secondary
                  e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db'
                  e.target.style.boxShadow = 'none'
                }}
              />
            </div>

            {/* Category Filter */}
            <div className="relative">
              <FiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200 appearance-none"
                onFocus={(e) => {
                  e.target.style.outline = 'none'
                  e.target.style.borderColor = '#f3d016'
                  e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#d1d5db'
                  e.target.style.boxShadow = 'none'
                }}
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = '#f3d016'
                e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            >
              <option value="name">Sort by Name</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Highest Rated</option>
            </select>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <div key={product.id} className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
              <button
                onClick={() => {
                  onSelectProduct(product.id)
                  onNavigate('product')
                }}
                className="w-full"
              >
                <div className="aspect-square bg-gray-200">
                  <img
                    src={product.image || product.images?.[0] || FALLBACK_IMAGES.product}
                    alt={product.name}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      e.target.src = FALLBACK_IMAGES.product
                    }}
                  />
                </div>
              </button>
              <div className="p-6">
                <button
                  onClick={() => {
                    onSelectProduct(product.id)
                    onNavigate('product')
                  }}
                  className="w-full text-left"
                >
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 transition-colors duration-200"
                      onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
                      onMouseLeave={(e) => e.target.style.color = ''}
                  >
                    {product.name}
                  </h3>
                </button>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {product.description}
                </p>

                {(product.rating || product.reviewCount) && (
                  <div className="flex items-center mb-3">
                    <div className="flex mr-2" style={{ color: '#FFFF00' }}>
                      {[...Array(5)].map((_, i) => (
                        <FiStar
                          key={i}
                          className={`w-4 h-4 ${
                            i < Math.floor(product.rating || 0) ? 'fill-current' : ''
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">
                      {product.rating?.toFixed(1) || '0.0'} ({product.reviewCount || product.reviews || 0})
                    </span>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-xl font-bold text-gray-900">
                    ${product.price?.toFixed(2) || '0.00'}
                  </span>
                  <button
                    onClick={() => handleAddToCart(product)}
                    disabled={addingToCart === product.id}
                    className="text-white p-2 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{ backgroundColor: branding.colors.secondary }}
                    onMouseEnter={(e) => !e.target.disabled && (e.target.style.backgroundColor = branding.colors.accent)}
                    onMouseLeave={(e) => !e.target.disabled && (e.target.style.backgroundColor = branding.colors.secondary)}
                  >
                    {addingToCart === product.id ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    ) : (
                      <FiShoppingCart className="w-5 h-5" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <FiSearch className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria</p>
          </div>
        )}

        {/* Newsletter Signup */}
        <div className="mt-16 rounded-2xl p-8 lg:p-12 text-center" style={{ backgroundColor: '#f3d016' }}>
          <h2 className="text-3xl font-bold text-white mb-4">
            Stay Updated on New Products
          </h2>
          <p className="text-yellow-100 mb-6 max-w-2xl mx-auto">
            Be the first to know about new arrivals, exclusive deals, and hair care tips.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = '#f3d016'
                e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
            <button
              className="bg-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
              style={{ color: '#f3d016' }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#F0FFF0'}
              onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
            >
              Subscribe
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Shop
