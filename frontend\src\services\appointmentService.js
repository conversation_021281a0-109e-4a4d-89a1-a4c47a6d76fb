import apiService from './api.js'

/**
 * Appointment service for handling appointment-related API calls
 */
class AppointmentService {
  /**
   * Get available time slots
   */
  async getAvailability(params = {}) {
    try {
      const response = await apiService.get('/appointments/availability', params)
      return response
    } catch (error) {
      console.error('Get availability error:', error)
      throw error
    }
  }

  /**
   * Create new appointment
   */
  async createAppointment(appointmentData) {
    try {
      const response = await apiService.post('/appointments', appointmentData)
      return response
    } catch (error) {
      console.error('Create appointment error:', error)
      throw error
    }
  }

  /**
   * Get user appointments
   */
  async getUserAppointments(params = {}) {
    try {
      const response = await apiService.get('/appointments/user', params)
      return response
    } catch (error) {
      console.error('Get user appointments error:', error)
      throw error
    }
  }

  /**
   * Get appointment by ID
   */
  async getAppointment(appointmentId) {
    try {
      const response = await apiService.get(`/appointments/${appointmentId}`)
      return response
    } catch (error) {
      console.error('Get appointment error:', error)
      throw error
    }
  }

  /**
   * Update appointment
   */
  async updateAppointment(appointmentId, appointmentData) {
    try {
      const response = await apiService.put(`/appointments/${appointmentId}`, appointmentData)
      return response
    } catch (error) {
      console.error('Update appointment error:', error)
      throw error
    }
  }

  /**
   * Cancel appointment
   */
  async cancelAppointment(appointmentId) {
    try {
      const response = await apiService.delete(`/appointments/${appointmentId}`)
      return response
    } catch (error) {
      console.error('Cancel appointment error:', error)
      throw error
    }
  }

  /**
   * Reschedule appointment
   */
  async rescheduleAppointment(appointmentId, newDateTime) {
    try {
      const response = await apiService.put(`/appointments/${appointmentId}`, {
        dateTime: newDateTime
      })
      return response
    } catch (error) {
      console.error('Reschedule appointment error:', error)
      throw error
    }
  }

  /**
   * Send appointment reminder
   */
  async sendReminder(appointmentId) {
    try {
      const response = await apiService.post(`/appointments/${appointmentId}/remind`)
      return response
    } catch (error) {
      console.error('Send reminder error:', error)
      throw error
    }
  }

  // Waitlist methods

  /**
   * Add to waitlist
   */
  async addToWaitlist(waitlistData) {
    try {
      const response = await apiService.post('/appointments/waitlist', waitlistData)
      return response
    } catch (error) {
      console.error('Add to waitlist error:', error)
      throw error
    }
  }

  // Admin methods

  /**
   * Get all appointments (Admin)
   */
  async getAdminAppointments(params = {}) {
    try {
      const response = await apiService.get('/admin/appointments', params)
      return response
    } catch (error) {
      console.error('Get admin appointments error:', error)
      throw error
    }
  }

  /**
   * Update appointment status (Admin)
   */
  async updateAppointmentStatus(appointmentId, status) {
    try {
      const response = await apiService.put(`/admin/appointments/${appointmentId}/status`, {
        status
      })
      return response
    } catch (error) {
      console.error('Update appointment status error:', error)
      throw error
    }
  }

  /**
   * Get waitlist (Admin)
   */
  async getWaitlist(params = {}) {
    try {
      const response = await apiService.get('/admin/waitlist', params)
      return response
    } catch (error) {
      console.error('Get waitlist error:', error)
      throw error
    }
  }

  /**
   * Update waitlist status (Admin)
   */
  async updateWaitlistStatus(waitlistId, status) {
    try {
      const response = await apiService.put(`/admin/waitlist/${waitlistId}`, {
        status
      })
      return response
    } catch (error) {
      console.error('Update waitlist status error:', error)
      throw error
    }
  }

  /**
   * Notify waitlist (Admin)
   */
  async notifyWaitlist(waitlistId) {
    try {
      const response = await apiService.post('/admin/waitlist/notify', {
        waitlistId
      })
      return response
    } catch (error) {
      console.error('Notify waitlist error:', error)
      throw error
    }
  }

  /**
   * Schedule automatic reminders (Admin)
   */
  async scheduleReminders(reminderData) {
    try {
      const response = await apiService.post('/admin/appointments/reminders/schedule', reminderData)
      return response
    } catch (error) {
      console.error('Schedule reminders error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const appointmentService = new AppointmentService()
export default appointmentService
