import { Request, Response } from 'express';
import { User, Product } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class UserController {
  static async getProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const user = await User.findById(req.user._id)
        .populate('favorites', 'name price images rating');

      if (!user) {
        sendNotFound(res, 'User not found');
        return;
      }

      sendSuccess(res, 'Profile retrieved successfully', user);
    } catch (error) {
      console.error('Get profile error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { name, phone, notificationPreferences } = req.body;
      const updateData: any = {};

      if (name) updateData.name = name;
      if (phone) updateData.phone = phone;
      if (notificationPreferences) updateData.notificationPreferences = notificationPreferences;

      const user = await User.findByIdAndUpdate(
        req.user._id,
        updateData,
        { new: true, runValidators: true }
      ).populate('favorites', 'name price images rating');

      if (!user) {
        sendNotFound(res, 'User not found');
        return;
      }

      sendSuccess(res, 'Profile updated successfully', user);
    } catch (error) {
      console.error('Update profile error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getFavorites(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const user = await User.findById(req.user._id)
        .populate('favorites', 'name price images rating category stock isActive');

      if (!user) {
        sendNotFound(res, 'User not found');
        return;
      }

      // Filter out inactive products
      const activeFavorites = user.favorites.filter((product: any) => product.isActive);

      sendSuccess(res, 'Favorites retrieved successfully', activeFavorites);
    } catch (error) {
      console.error('Get favorites error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async addToFavorites(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { productId } = req.body;

      // Verify product exists
      const product = await Product.findById(productId);
      if (!product || !product.isActive) {
        sendNotFound(res, 'Product not found or inactive');
        return;
      }

      const user = await User.findById(req.user._id);
      if (!user) {
        sendNotFound(res, 'User not found');
        return;
      }

      // Check if already in favorites
      if (user.favorites.includes(productId)) {
        sendError(res, 'Product already in favorites');
        return;
      }

      user.favorites.push(productId);
      await user.save();

      await user.populate('favorites', 'name price images rating category');

      sendSuccess(res, 'Product added to favorites successfully', user.favorites);
    } catch (error) {
      console.error('Add to favorites error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async removeFromFavorites(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { productId } = req.params;

      const user = await User.findById(req.user._id);
      if (!user) {
        sendNotFound(res, 'User not found');
        return;
      }

      user.favorites = user.favorites.filter(
        (fav: any) => fav.toString() !== productId
      );
      await user.save();

      await user.populate('favorites', 'name price images rating category');

      sendSuccess(res, 'Product removed from favorites successfully', user.favorites);
    } catch (error) {
      console.error('Remove from favorites error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateNotificationPreferences(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { email, sms, push } = req.body;

      const user = await User.findByIdAndUpdate(
        req.user._id,
        {
          notificationPreferences: {
            email: email !== undefined ? email : req.user.notificationPreferences.email,
            sms: sms !== undefined ? sms : req.user.notificationPreferences.sms,
            push: push !== undefined ? push : req.user.notificationPreferences.push
          }
        },
        { new: true, runValidators: true }
      );

      if (!user) {
        sendNotFound(res, 'User not found');
        return;
      }

      sendSuccess(res, 'Notification preferences updated successfully', user.notificationPreferences);
    } catch (error) {
      console.error('Update notification preferences error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async changePassword(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { currentPassword, newPassword, confirmPassword } = req.body;

      if (!currentPassword || !newPassword || !confirmPassword) {
        sendError(res, 'All password fields are required');
        return;
      }

      if (newPassword !== confirmPassword) {
        sendError(res, 'New passwords do not match');
        return;
      }

      if (newPassword.length < 6) {
        sendError(res, 'New password must be at least 6 characters long');
        return;
      }

      // Get user with password field
      const user = await User.findById(req.user._id).select('+password');
      if (!user) {
        sendNotFound(res, 'User not found');
        return;
      }

      // Verify current password
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        sendError(res, 'Current password is incorrect', undefined, 400);
        return;
      }

      // Update password
      user.password = newPassword;
      await user.save();

      sendSuccess(res, 'Password changed successfully');
    } catch (error) {
      console.error('Change password error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
