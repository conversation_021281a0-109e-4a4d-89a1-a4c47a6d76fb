import { Request, Response } from 'express';
import { Staff } from '../models';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';

export class StaffController {
  static async getAllStaff(req: Request, res: Response): Promise<void> {
    try {
      const { active, page = 1, limit = 20 } = req.query;
      const filter: any = {};

      if (active !== undefined) {
        filter.isActive = active === 'true';
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [staff, total] = await Promise.all([
        Staff.find(filter)
          .populate('services', 'name price duration')
          .sort({ name: 1 })
          .skip(skip)
          .limit(limitNum),
        Staff.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Staff retrieved successfully', {
        staff,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get all staff error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getStaffById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const staff = await Staff.findById(id)
        .populate('services', 'name price duration category');

      if (!staff) {
        sendNotFound(res, 'Staff member not found');
        return;
      }

      sendSuccess(res, 'Staff member retrieved successfully', staff);
    } catch (error) {
      console.error('Get staff by ID error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createStaff(req: Request, res: Response): Promise<void> {
    try {
      const staffData = req.body;

      const staff = await Staff.create(staffData);
      await staff.populate('services', 'name price duration');

      sendCreated(res, 'Staff member created successfully', staff);
    } catch (error) {
      console.error('Create staff error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateStaff(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const staff = await Staff.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).populate('services', 'name price duration');

      if (!staff) {
        sendNotFound(res, 'Staff member not found');
        return;
      }

      sendSuccess(res, 'Staff member updated successfully', staff);
    } catch (error) {
      console.error('Update staff error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteStaff(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const staff = await Staff.findByIdAndDelete(id);

      if (!staff) {
        sendNotFound(res, 'Staff member not found');
        return;
      }

      sendSuccess(res, 'Staff member deleted successfully');
    } catch (error) {
      console.error('Delete staff error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getStaffAvailability(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { date } = req.query;

      const staff = await Staff.findById(id);

      if (!staff) {
        sendNotFound(res, 'Staff member not found');
        return;
      }

      let availability = staff.availability;

      // If date is provided, filter by day of week
      if (date) {
        const requestedDate = new Date(date as string);
        const dayOfWeek = requestedDate.toLocaleDateString('en-US', { weekday: 'long' });
        availability = staff.availability.filter(avail => avail.day === dayOfWeek);
      }

      sendSuccess(res, 'Staff availability retrieved successfully', {
        staffId: staff._id,
        name: staff.name,
        availability
      });
    } catch (error) {
      console.error('Get staff availability error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateStaffAvailability(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { availability } = req.body;

      const staff = await Staff.findByIdAndUpdate(
        id,
        { availability },
        { new: true, runValidators: true }
      );

      if (!staff) {
        sendNotFound(res, 'Staff member not found');
        return;
      }

      sendSuccess(res, 'Staff availability updated successfully', staff.availability);
    } catch (error) {
      console.error('Update staff availability error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
