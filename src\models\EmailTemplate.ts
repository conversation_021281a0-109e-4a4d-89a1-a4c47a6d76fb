import mongoose, { Schema, Document } from 'mongoose';

export interface IEmailTemplate extends Document {
  _id: string;
  type: string;
  subject: string;
  content: string;
  variables: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const emailTemplateSchema = new Schema<IEmailTemplate>({
  type: {
    type: String,
    required: true,
    unique: true,
    enum: [
      'appointment-confirmation',
      'appointment-reminder',
      'appointment-cancelled',
      'order-confirmation',
      'order-shipped',
      'order-delivered',
      'password-reset',
      'welcome',
      'newsletter',
      'promotion'
    ]
  },
  subject: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  content: {
    type: String,
    required: true
  },
  variables: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better query performance
emailTemplateSchema.index({ type: 1 });
emailTemplateSchema.index({ isActive: 1 });

export const EmailTemplate = mongoose.model<IEmailTemplate>('EmailTemplate', emailTemplateSchema);
