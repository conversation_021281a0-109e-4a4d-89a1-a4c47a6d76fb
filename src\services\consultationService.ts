import { User } from '../models/User';
import { AppointmentService } from './appointmentService';
import { AuthService } from './authService';
import crypto from 'crypto';

interface ConsultationData {
  name?: string; // For backward compatibility
  firstName?: string;
  lastName?: string;
  email: string;
  phone?: string;
  service: string;
  date: string;
  time: string;
  message?: string;
  isLoggedIn?: boolean;
  userId?: string;
}

interface ConsultationResult {
  success: boolean;
  message: string;
  appointment?: any;
  user?: any;
  token?: string;
  refreshToken?: string;
  isNewUser?: boolean;
  generatedPassword?: string;
}

export class ConsultationService {
  /**
   * Smart consultation booking that handles:
   * 1. Guest users (auto-creates account with generated password)
   * 2. Existing users (uses their data)
   * 3. New users (creates account from form data)
   * 4. Logged-in users (uses authenticated user data)
   */
  static async bookConsultation(data: ConsultationData): Promise<ConsultationResult> {
    try {
      let user: any;
      let isNewUser = false;
      let generatedPassword: string | undefined;
      let token: string | undefined;
      let refreshToken: string | undefined;

      // Case 1: User is already logged in
      if (data.isLoggedIn && data.userId) {
        user = await User.findById(data.userId);
        if (!user) {
          throw new Error('Authenticated user not found');
        }
      } else {
        // Case 2: Check if user exists by email
        const existingUser = await User.findOne({ email: data.email.toLowerCase() });
        
        if (existingUser) {
          // Existing user - use their account
          user = existingUser;
        } else {
          // Case 3: New user - create account
          isNewUser = true;
          
          // Generate a secure password for guest users
          generatedPassword = this.generateSecurePassword();

          // Get firstName and lastName from data or split name
          let firstName: string;
          let lastName: string;

          if (data.firstName && data.lastName) {
            firstName = data.firstName.trim();
            lastName = data.lastName.trim();
          } else if (data.name) {
            // Fallback: split name into firstName and lastName
            const nameParts = data.name.trim().split(' ');
            firstName = nameParts[0] || 'Guest';
            lastName = nameParts.slice(1).join(' ') || 'User';
          } else {
            firstName = 'Guest';
            lastName = 'User';
          }
          
          // Create new user account
          const fullName = `${firstName} ${lastName}`.trim();
          const registrationResult = await AuthService.register({
            firstName,
            lastName,
            name: fullName,
            email: data.email,
            phone: data.phone,
            password: generatedPassword
          });
          
          user = registrationResult.user;
          token = registrationResult.token;
          refreshToken = registrationResult.refreshToken;
        }
      }

      // Create the appointment
      const customerName = data.name || `${data.firstName || user.firstName} ${data.lastName || user.lastName}`.trim() || user.name;
      const appointmentData = {
        user: user._id,
        service: data.service,
        date: new Date(data.date),
        time: data.time,
        customerInfo: {
          name: customerName,
          email: data.email,
          phone: data.phone || user.phone || ''
        },
        message: data.message
      };

      const appointment = await AppointmentService.createAppointment(appointmentData);

      // Prepare response message
      let message = 'Consultation booked successfully!';
      if (isNewUser) {
        message += ' An account has been created for you. Please check your email for login details.';
      }

      return {
        success: true,
        message,
        appointment,
        user: {
          _id: user._id,
          name: user.name,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          role: user.role,
          isVerified: user.isVerified
        },
        token,
        refreshToken,
        isNewUser,
        generatedPassword: isNewUser ? generatedPassword : undefined
      };

    } catch (error) {
      console.error('Consultation booking error:', error);
      throw error;
    }
  }

  /**
   * Generate a secure password for guest users
   */
  private static generateSecurePassword(): string {
    // Generate a secure 12-character password with mix of letters, numbers, and symbols
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      password += charset[randomIndex];
    }
    
    return password;
  }

  /**
   * Get consultation availability (same as appointment availability)
   */
  static async getAvailability(date: string, serviceId?: string): Promise<string[]> {
    return AppointmentService.getAvailableTimeSlots(date, serviceId);
  }
}
