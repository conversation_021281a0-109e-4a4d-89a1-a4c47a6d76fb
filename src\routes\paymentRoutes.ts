import { Router } from 'express';
import { PaymentController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';

const router = Router();

// GET /api/admin/payment-settings (admin only)
router.get(
  '/payment-settings',
  authenticate,
  authorize('admin'),
  PaymentController.getPaymentSettings
);

// PUT /api/admin/payment-settings (admin only)
router.put(
  '/payment-settings',
  authenticate,
  authorize('admin'),
  PaymentController.updatePaymentSettings
);

export default router;
