/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Ensure all interactive elements have cursor pointer */
button,
[role="button"],
[onclick],
.cursor-pointer,
input[type="button"],
input[type="submit"],
input[type="reset"],
select,
a[href],
label[for],
[tabindex]:not([tabindex="-1"]) {
  cursor: pointer;
}

/* Specific cursor styles for form elements */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="number"],
textarea {
  cursor: text;
}

/* Disabled elements should not have pointer cursor */
button:disabled,
input:disabled,
select:disabled,
textarea:disabled,
[disabled] {
  cursor: not-allowed !important;
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}