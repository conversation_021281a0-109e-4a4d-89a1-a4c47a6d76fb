/* Only add cursor pointer styles without disrupting existing layout */

/* Ensure interactive elements have cursor pointer - more specific selectors */
button:not(.no-cursor),
[role="button"]:not(.no-cursor),
.cursor-pointer {
  cursor: pointer;
}

/* Form elements cursor styles */
input[type="text"]:not(.no-cursor),
input[type="email"]:not(.no-cursor),
input[type="password"]:not(.no-cursor),
input[type="tel"]:not(.no-cursor),
input[type="url"]:not(.no-cursor),
input[type="search"]:not(.no-cursor),
input[type="number"]:not(.no-cursor),
textarea:not(.no-cursor) {
  cursor: text;
}

/* Disabled elements */
button:disabled,
input:disabled,
select:disabled,
textarea:disabled,
[disabled] {
  cursor: not-allowed !important;
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}