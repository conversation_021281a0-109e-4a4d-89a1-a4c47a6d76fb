import { Request, Response, NextFunction } from 'express';
import { validationResult, Validation<PERSON>hain } from 'express-validator';
import { sendError } from '../utils/response';

export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    sendError(res, 'Validation failed', errorMessages.join(', '), 400);
    return;
  }
  
  next();
};

export const validate = (validations: ValidationChain[]) => {
  return [
    ...validations,
    handleValidationErrors
  ];
};
