import apiService from './api.js'

/**
 * Order service for handling order operations
 */
class OrderService {
  /**
   * Create new order
   */
  async createOrder(orderData) {
    try {
      const response = await apiService.post('/orders', orderData)
      return response
    } catch (error) {
      console.error('Create order error:', error)
      throw error
    }
  }

  /**
   * Get user orders
   */
  async getUserOrders(params = {}) {
    try {
      const response = await apiService.get('/orders', params)
      return response
    } catch (error) {
      console.error('Get user orders error:', error)
      throw error
    }
  }

  /**
   * Get order by ID
   */
  async getOrder(orderId) {
    try {
      const response = await apiService.get(`/orders/${orderId}`)
      return response
    } catch (error) {
      console.error('Get order error:', error)
      throw error
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(orderId, reason = '') {
    try {
      const response = await apiService.put(`/orders/${orderId}/cancel`, {
        reason
      })
      return response
    } catch (error) {
      console.error('Cancel order error:', error)
      throw error
    }
  }

  /**
   * Request order refund
   */
  async requestRefund(orderId, refundData) {
    try {
      const response = await apiService.post(`/orders/${orderId}/refund`, refundData)
      return response
    } catch (error) {
      console.error('Request refund error:', error)
      throw error
    }
  }

  /**
   * Track order
   */
  async trackOrder(orderId) {
    try {
      const response = await apiService.get(`/orders/${orderId}/tracking`)
      return response
    } catch (error) {
      console.error('Track order error:', error)
      throw error
    }
  }

  /**
   * Get order invoice
   */
  async getOrderInvoice(orderId) {
    try {
      const response = await apiService.get(`/orders/${orderId}/invoice`)
      return response
    } catch (error) {
      console.error('Get order invoice error:', error)
      throw error
    }
  }

  /**
   * Reorder (create new order from existing order)
   */
  async reorder(orderId) {
    try {
      const response = await apiService.post(`/orders/${orderId}/reorder`)
      return response
    } catch (error) {
      console.error('Reorder error:', error)
      throw error
    }
  }

  /**
   * Get order history with filters
   */
  async getOrderHistory(filters = {}) {
    try {
      const response = await apiService.get('/orders/history', filters)
      return response
    } catch (error) {
      console.error('Get order history error:', error)
      throw error
    }
  }

  // Admin methods

  /**
   * Get all orders (Admin)
   */
  async getAdminOrders(params = {}) {
    try {
      const response = await apiService.get('/admin/orders', params)
      return response
    } catch (error) {
      console.error('Get admin orders error:', error)
      throw error
    }
  }

  /**
   * Update order status (Admin)
   */
  async updateOrderStatus(orderId, status, notes = '') {
    try {
      const response = await apiService.put(`/orders/${orderId}/status`, {
        status,
        notes
      })
      return response
    } catch (error) {
      console.error('Update order status error:', error)
      throw error
    }
  }

  /**
   * Process refund (Admin)
   */
  async processRefund(orderId, refundData) {
    try {
      const response = await apiService.post(`/admin/orders/${orderId}/refund`, refundData)
      return response
    } catch (error) {
      console.error('Process refund error:', error)
      throw error
    }
  }

  /**
   * Update shipping info (Admin)
   */
  async updateShipping(orderId, shippingData) {
    try {
      const response = await apiService.put(`/admin/orders/${orderId}/shipping`, shippingData)
      return response
    } catch (error) {
      console.error('Update shipping error:', error)
      throw error
    }
  }

  /**
   * Add order notes (Admin)
   */
  async addOrderNotes(orderId, notes) {
    try {
      const response = await apiService.post(`/admin/orders/${orderId}/notes`, {
        notes
      })
      return response
    } catch (error) {
      console.error('Add order notes error:', error)
      throw error
    }
  }

  /**
   * Get order analytics (Admin)
   */
  async getOrderAnalytics(params = {}) {
    try {
      const response = await apiService.get('/admin/orders/analytics', params)
      return response
    } catch (error) {
      console.error('Get order analytics error:', error)
      throw error
    }
  }

  /**
   * Export orders (Admin)
   */
  async exportOrders(params = {}) {
    try {
      const response = await apiService.get('/admin/export/orders', params)
      return response
    } catch (error) {
      console.error('Export orders error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const orderService = new OrderService()
export default orderService
