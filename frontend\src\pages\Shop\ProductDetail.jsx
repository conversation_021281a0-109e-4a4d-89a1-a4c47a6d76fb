import { useState } from 'react'
import { FiShoppingCart, FiStar, FiHeart, FiArrowLeft, FiCheck, FiTruck } from 'react-icons/fi'

const ProductDetail = ({ productId, onNavigate }) => {
  const [quantity, setQuantity] = useState(1)
  const [selectedImage, setSelectedImage] = useState(0)

  // Mock product data - in a real app, this would come from an API
  const product = {
    id: productId,
    name: "Loc Maintenance Shampoo",
    price: 24.99,
    rating: 4.8,
    reviews: 156,
    description: "Our premium Loc Maintenance Shampoo is specially formulated to gently cleanse your locs without stripping natural oils. This sulfate-free formula helps maintain the integrity of your locs while promoting healthy scalp conditions.",
    images: [
      "https://images.unsplash.com/photo-**********-8c89e6adf883?w=500&h=500&fit=crop",
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500&h=500&fit=crop",
      "https://images.unsplash.com/photo-**********-efd6c1ff04f6?w=500&h=500&fit=crop",
      "https://images.unsplash.com/photo-1608248543803-ba4f8c70ae0b?w=500&h=500&fit=crop"
    ],
    features: [
      "Sulfate-free formula",
      "Natural ingredients",
      "Promotes healthy scalp",
      "Gentle on locs",
      "pH balanced",
      "Cruelty-free"
    ],
    ingredients: "Water, Cocamidopropyl Betaine, Sodium Cocoyl Isethionate, Glycerin, Aloe Barbadensis Leaf Juice, Coconut Oil, Jojoba Oil, Tea Tree Oil, Rosemary Extract",
    howToUse: [
      "Wet hair thoroughly with warm water",
      "Apply a small amount to scalp and roots",
      "Gently massage with fingertips",
      "Rinse thoroughly with warm water",
      "Follow with conditioner if needed"
    ],
    inStock: true,
    category: "Shampoo"
  }

  const relatedProducts = [
    {
      id: 2,
      name: "Natural Hair Oil Blend",
      price: 18.99,
      image: "https://images.unsplash.com/photo-1608248543803-ba4f8c70ae0b?w=200&h=200&fit=crop"
    },
    {
      id: 4,
      name: "Deep Conditioning Mask",
      price: 28.99,
      image: "https://images.unsplash.com/photo-**********-efd6c1ff04f6?w=200&h=200&fit=crop"
    },
    {
      id: 8,
      name: "Leave-In Conditioner",
      price: 19.99,
      image: "https://images.unsplash.com/photo-**********-8c89e6adf883?w=200&h=200&fit=crop"
    }
  ]

  const handleAddToCart = () => {
    // Handle add to cart logic
    alert(`Added ${quantity} ${product.name} to cart!`)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <button
            onClick={() => onNavigate('shop')}
            className="hover:text-amber-600 transition-colors duration-200"
          >
            Shop
          </button>
          <span>/</span>
          <span className="text-gray-900">{product.name}</span>
        </div>

        {/* Back Button */}
        <button
          onClick={() => onNavigate('shop')}
          className="inline-flex items-center text-amber-600 hover:text-amber-700 mb-8 transition-colors duration-200"
        >
          <FiArrowLeft className="w-4 h-4 mr-2" />
          Back to Shop
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Product Images */}
          <div>
            <div className="aspect-square bg-gray-200 rounded-2xl overflow-hidden mb-4">
              <img
                src={product.images[selectedImage]}
                alt={product.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`aspect-square bg-gray-200 rounded-lg overflow-hidden border-2 transition-colors duration-200 ${
                    selectedImage === index ? 'border-amber-600' : 'border-transparent'
                  }`}
                >
                  <img
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              {product.name}
            </h1>

            <div className="flex items-center mb-6">
              <div className="flex text-amber-400 mr-3">
                {[...Array(5)].map((_, i) => (
                  <FiStar
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(product.rating) ? 'fill-current' : ''
                    }`}
                  />
                ))}
              </div>
              <span className="text-gray-600">
                {product.rating} ({product.reviews} reviews)
              </span>
            </div>

            <div className="text-3xl font-bold text-gray-900 mb-6">
              ${product.price}
            </div>

            <p className="text-gray-600 mb-8 leading-relaxed">
              {product.description}
            </p>

            {/* Features */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Features</h3>
              <div className="grid grid-cols-2 gap-2">
                {product.features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <FiCheck className="w-4 h-4 text-green-500 mr-2" />
                    <span className="text-gray-600 text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Quantity and Add to Cart */}
            <div className="flex items-center space-x-4 mb-8">
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-3 py-2 text-gray-600 hover:text-gray-900"
                >
                  -
                </button>
                <span className="px-4 py-2 border-x border-gray-300">{quantity}</span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-3 py-2 text-gray-600 hover:text-gray-900"
                >
                  +
                </button>
              </div>
              <button
                onClick={handleAddToCart}
                className="flex-1 bg-amber-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-amber-700 transition-colors duration-200 flex items-center justify-center"
              >
                <FiShoppingCart className="w-5 h-5 mr-2" />
                Add to Cart
              </button>
              <button className="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                <FiHeart className="w-5 h-5 text-gray-600" />
              </button>
            </div>

            {/* Shipping Info */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-8">
              <div className="flex items-center text-green-700">
                <FiTruck className="w-5 h-5 mr-2" />
                <span className="font-medium">Free shipping on orders over $50</span>
              </div>
            </div>

            {/* Stock Status */}
            <div className="flex items-center text-green-600 mb-8">
              <FiCheck className="w-5 h-5 mr-2" />
              <span className="font-medium">In Stock - Ready to Ship</span>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="bg-white rounded-2xl p-8 mb-16 shadow-lg">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">How to Use</h3>
              <ol className="space-y-2">
                {product.howToUse.map((step, index) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-6 h-6 bg-amber-100 text-amber-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                      {index + 1}
                    </span>
                    <span className="text-gray-600">{step}</span>
                  </li>
                ))}
              </ol>
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Ingredients</h3>
              <p className="text-gray-600 leading-relaxed">{product.ingredients}</p>
            </div>
          </div>
        </div>

        {/* Related Products */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-8">You Might Also Like</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {relatedProducts.map((relatedProduct) => (
              <button
                key={relatedProduct.id}
                onClick={() => {
                  onNavigate('product')
                }}
                className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 text-left"
              >
                <div className="aspect-square bg-gray-200">
                  <img
                    src={relatedProduct.image}
                    alt={relatedProduct.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {relatedProduct.name}
                  </h3>
                  <span className="text-xl font-bold text-gray-900">
                    ${relatedProduct.price}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProductDetail
