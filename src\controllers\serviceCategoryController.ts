import { Request, Response } from 'express';
import { Service } from '../models';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';

export class ServiceCategoryController {
  static async getServiceCategories(req: Request, res: Response): Promise<void> {
    try {
      // Get unique categories from services
      const categories = await Service.aggregate([
        { $match: { isActive: true } },
        { $group: { _id: '$category', count: { $sum: 1 } } },
        { $project: { name: '$_id', count: 1, _id: 0 } },
        { $sort: { name: 1 } }
      ]);

      sendSuccess(res, 'Service categories retrieved successfully', categories);
    } catch (error) {
      console.error('Get service categories error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createServiceCategory(req: Request, res: Response): Promise<void> {
    try {
      const { name, description } = req.body;

      // Check if category already exists
      const existingService = await Service.findOne({ category: name });
      if (existingService) {
        sendError(res, 'Service category already exists');
        return;
      }

      // Create a placeholder service for the category (or you could create a separate Category model)
      const categoryData = {
        name: `${name} Category`,
        description: description || `Services in the ${name} category`,
        category: name,
        price: 0,
        duration: 0,
        isActive: false // This is just a placeholder
      };

      sendCreated(res, 'Service category created successfully', { name, description });
    } catch (error) {
      console.error('Create service category error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateServiceCategory(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { name, description } = req.body;

      // Update all services in this category
      const result = await Service.updateMany(
        { category: id },
        { category: name }
      );

      sendSuccess(res, 'Service category updated successfully', {
        oldCategory: id,
        newCategory: name,
        servicesUpdated: result.modifiedCount
      });
    } catch (error) {
      console.error('Update service category error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteServiceCategory(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      // Check if any services use this category
      const servicesCount = await Service.countDocuments({ category: id });
      
      if (servicesCount > 0) {
        sendError(res, `Cannot delete category. ${servicesCount} services are using this category.`);
        return;
      }

      sendSuccess(res, 'Service category deleted successfully');
    } catch (error) {
      console.error('Delete service category error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
