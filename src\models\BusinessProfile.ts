import mongoose, { Schema, Document } from 'mongoose';

export interface IBusinessProfile extends Document {
  _id: string;
  name: string;
  email: string;
  role: string;
  avatar: string;
  businessSince: string;
  certifications: string[];
  yearsExperience: number;
  businessHours: Array<{
    day: string;
    open: string;
    close: string;
    isClosed: boolean;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

const businessProfileSchema = new Schema<IBusinessProfile>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  role: {
    type: String,
    required: true,
    trim: true
  },
  avatar: {
    type: String,
    default: ''
  },
  businessSince: {
    type: String,
    required: true
  },
  certifications: [{
    type: String,
    trim: true
  }],
  yearsExperience: {
    type: Number,
    required: true,
    min: 0
  },
  businessHours: [{
    day: {
      type: String,
      required: true,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    },
    open: {
      type: String,
      required: function(this: any) { return !this.isClosed; },
      match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    close: {
      type: String,
      required: function(this: any) { return !this.isClosed; },
      match: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
    },
    isClosed: {
      type: Boolean,
      default: false
    }
  }]
}, {
  timestamps: true
});

export const BusinessProfile = mongoose.model<IBusinessProfile>('BusinessProfile', businessProfileSchema);
