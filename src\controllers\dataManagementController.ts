import { Request, Response } from 'express';
import { User, Order, Appointment, Product, Service } from '../models';
import { sendSuccess, sendError } from '../utils/response';
import { CacheService } from '../utils/cache';
import fs from 'fs';
import path from 'path';
import { Parser } from 'json2csv';

export class DataManagementController {
  // Export Data
  static async exportData(req: Request, res: Response): Promise<void> {
    try {
      const { type } = req.params;
      const { format = 'json' } = req.query;

      let data: any[] = [];
      let filename = '';

      switch (type) {
        case 'customers':
          data = await User.find({ role: 'user' }).select('-password').lean();
          filename = `customers_${Date.now()}`;
          break;
        case 'orders':
          data = await Order.find()
            .populate('user', 'name email')
            .populate('items.product', 'name')
            .lean();
          filename = `orders_${Date.now()}`;
          break;
        case 'appointments':
          data = await Appointment.find()
            .populate('user', 'name email')
            .populate('service', 'name')
            .lean();
          filename = `appointments_${Date.now()}`;
          break;
        case 'products':
          data = await Product.find().lean();
          filename = `products_${Date.now()}`;
          break;
        case 'services':
          data = await Service.find().lean();
          filename = `services_${Date.now()}`;
          break;
        default:
          sendError(res, 'Invalid export type');
          return;
      }

      if (format === 'csv') {
        try {
          const parser = new Parser();
          const csv = parser.parse(data);
          
          res.setHeader('Content-Type', 'text/csv');
          res.setHeader('Content-Disposition', `attachment; filename="${filename}.csv"`);
          res.send(csv);
        } catch (error) {
          sendError(res, 'Failed to generate CSV');
        }
      } else {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}.json"`);
        res.json(data);
      }
    } catch (error) {
      console.error('Export data error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Import Data
  static async importData(req: Request, res: Response): Promise<void> {
    try {
      const { type } = req.params;
      
      if (!req.file) {
        sendError(res, 'No file provided');
        return;
      }

      const fileContent = fs.readFileSync(req.file.path, 'utf8');
      let data: any[];

      try {
        data = JSON.parse(fileContent);
      } catch (error) {
        sendError(res, 'Invalid JSON file');
        return;
      }

      if (!Array.isArray(data)) {
        sendError(res, 'Data must be an array');
        return;
      }

      let imported = 0;
      let errors = 0;

      switch (type) {
        case 'products':
          for (const item of data) {
            try {
              await Product.create(item);
              imported++;
            } catch (error) {
              errors++;
              console.error('Product import error:', error);
            }
          }
          break;
        case 'services':
          for (const item of data) {
            try {
              await Service.create(item);
              imported++;
            } catch (error) {
              errors++;
              console.error('Service import error:', error);
            }
          }
          break;
        default:
          sendError(res, 'Invalid import type');
          return;
      }

      // Clean up uploaded file
      fs.unlinkSync(req.file.path);

      sendSuccess(res, 'Data imported successfully', {
        imported,
        errors,
        total: data.length
      });
    } catch (error) {
      console.error('Import data error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Backup Database
  static async createBackup(req: Request, res: Response): Promise<void> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupData = {
        timestamp,
        users: await User.find().select('-password').lean(),
        products: await Product.find().lean(),
        services: await Service.find().lean(),
        orders: await Order.find().lean(),
        appointments: await Appointment.find().lean()
      };

      const backupDir = path.join(process.cwd(), 'backups');
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      const filename = `backup_${timestamp}.json`;
      const filepath = path.join(backupDir, filename);

      fs.writeFileSync(filepath, JSON.stringify(backupData, null, 2));

      sendSuccess(res, 'Backup created successfully', {
        filename,
        size: fs.statSync(filepath).size,
        url: `/backups/${filename}`
      });
    } catch (error) {
      console.error('Create backup error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Restore Database
  static async restoreBackup(req: Request, res: Response): Promise<void> {
    try {
      if (!req.file) {
        sendError(res, 'No backup file provided');
        return;
      }

      const fileContent = fs.readFileSync(req.file.path, 'utf8');
      let backupData: any;

      try {
        backupData = JSON.parse(fileContent);
      } catch (error) {
        sendError(res, 'Invalid backup file');
        return;
      }

      // Validate backup structure
      if (!backupData.timestamp || !backupData.users) {
        sendError(res, 'Invalid backup file structure');
        return;
      }

      // Production-ready backup restoration with proper safeguards
      const results = {
        users: 0,
        products: 0,
        services: 0,
        orders: 0,
        appointments: 0
      };

      // WARNING: This operation will replace existing data
      // In production, implement proper backup validation and user confirmation

      // Restore users (excluding existing admin users)
      if (backupData.users) {
        for (const userData of backupData.users) {
          try {
            // Skip if user already exists
            const existingUser = await User.findOne({ email: userData.email });
            if (!existingUser) {
              await User.create(userData);
              results.users++;
            }
          } catch (error) {
            console.error('User restore error:', error);
          }
        }
      }

      // Restore products
      if (backupData.products) {
        for (const productData of backupData.products) {
          try {
            const existingProduct = await Product.findOne({ name: productData.name });
            if (!existingProduct) {
              await Product.create(productData);
              results.products++;
            }
          } catch (error) {
            console.error('Product restore error:', error);
          }
        }
      }

      // Restore services
      if (backupData.services) {
        for (const serviceData of backupData.services) {
          try {
            const existingService = await Service.findOne({ name: serviceData.name });
            if (!existingService) {
              await Service.create(serviceData);
              results.services++;
            }
          } catch (error) {
            console.error('Service restore error:', error);
          }
        }
      }

      // Restore orders (only if users exist)
      if (backupData.orders) {
        for (const orderData of backupData.orders) {
          try {
            const userExists = await User.findById(orderData.user);
            if (userExists) {
              await Order.create(orderData);
              results.orders++;
            }
          } catch (error) {
            console.error('Order restore error:', error);
          }
        }
      }

      // Restore appointments (only if users and services exist)
      if (backupData.appointments) {
        for (const appointmentData of backupData.appointments) {
          try {
            const userExists = await User.findById(appointmentData.user);
            const serviceExists = await Service.findById(appointmentData.service);
            if (userExists && serviceExists) {
              await Appointment.create(appointmentData);
              results.appointments++;
            }
          } catch (error) {
            console.error('Appointment restore error:', error);
          }
        }
      }

      // Clean up uploaded file
      fs.unlinkSync(req.file.path);

      sendSuccess(res, 'Backup restored successfully', results);
    } catch (error) {
      console.error('Restore backup error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Cache Management
  static async clearCache(req: Request, res: Response): Promise<void> {
    try {
      const { type } = req.body;

      switch (type) {
        case 'all':
          const allResult = CacheService.clearAllCaches();
          if (allResult.success) {
            sendSuccess(res, 'All caches cleared successfully');
          } else {
            sendError(res, allResult.error || 'Failed to clear all caches');
          }
          break;

        case 'images':
          const imageResult = CacheService.clearImageCache();
          if (imageResult.success) {
            sendSuccess(res, 'Image cache cleared successfully');
          } else {
            sendError(res, imageResult.error || 'Failed to clear image cache');
          }
          break;

        case 'data':
          const dataResult = CacheService.clearDataCache();
          if (dataResult.success) {
            sendSuccess(res, 'Data cache cleared successfully');
          } else {
            sendError(res, dataResult.error || 'Failed to clear data cache');
          }
          break;

        default:
          sendError(res, 'Invalid cache type. Valid types: all, images, data');
          return;
      }
    } catch (error) {
      console.error('Clear cache error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Cache Statistics
  static async getCacheStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = CacheService.getCacheStats();
      sendSuccess(res, 'Cache statistics retrieved successfully', stats);
    } catch (error) {
      console.error('Get cache stats error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Cleanup Expired Cache
  static async cleanupCache(req: Request, res: Response): Promise<void> {
    try {
      const result = CacheService.cleanupExpiredCache();
      sendSuccess(res, 'Cache cleanup completed successfully', result);
    } catch (error) {
      console.error('Cache cleanup error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
