import mongoose, { Schema, Document } from 'mongoose';

export interface IPaymentSettings extends Document {
  _id: string;
  acceptedMethods: string[];
  taxRate: number;
  shippingOptions: Array<{
    name: string;
    price: number;
    estimatedDays: string;
  }>;
  freeShippingThreshold: number;
  createdAt: Date;
  updatedAt: Date;
}

const paymentSettingsSchema = new Schema<IPaymentSettings>({
  acceptedMethods: {
    type: [String],
    default: ['Cash', 'Credit Card', 'Debit Card', 'PayPal'],
    enum: ['Cash', 'Credit Card', 'Debit Card', 'PayPal', 'Venmo', 'CashApp', 'Stripe']
  },
  taxRate: {
    type: Number,
    default: 0.08,
    min: 0,
    max: 1
  },
  shippingOptions: [{
    name: {
      type: String,
      required: true
    },
    price: {
      type: Number,
      required: true,
      min: 0
    },
    estimatedDays: {
      type: String,
      required: true
    }
  }],
  freeShippingThreshold: {
    type: Number,
    default: 50,
    min: 0
  }
}, {
  timestamps: true
});

export const PaymentSettings = mongoose.model<IPaymentSettings>('PaymentSettings', paymentSettingsSchema);
