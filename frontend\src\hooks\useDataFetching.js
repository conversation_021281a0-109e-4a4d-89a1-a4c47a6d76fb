import { useState, useEffect, useCallback, useRef } from 'react'
import { useRequestDeduplication } from '../utils/requestDeduplicator'

/**
 * Custom hook for data fetching that prevents infinite loops and duplicate API calls
 * 
 * Key Benefits:
 * - Prevents infinite loops: Functions are memoized and only recreated when dependencies change
 * - Prevents duplicate API calls: loadingRef prevents concurrent requests
 * - Better performance: Reduces unnecessary re-renders and API calls
 * - Consistent pattern: Same solution applied across all components
 * 
 * @param {Function} fetchFunction - The async function to fetch data
 * @param {Array} dependencies - Dependencies that should trigger a refetch
 * @param {Object} options - Configuration options
 * @returns {Object} - { data, loading, error, refetch }
 */
export function useDataFetching(fetchFunction, dependencies = [], options = {}) {
  const {
    immediate = true,
    deduplicate = true,
    deduplicationKey = null
  } = options

  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(immediate)
  const [error, setError] = useState('')
  
  // Use ref to prevent loading state from causing infinite loops
  const loadingRef = useRef(false)
  const { execute, generateKey } = useRequestDeduplication()

  // Memoized fetch function that excludes loading from dependencies
  const fetchData = useCallback(async () => {
    // Prevent duplicate calls
    if (loadingRef.current) {
      console.log('🚫 Preventing duplicate API call')
      return
    }

    try {
      loadingRef.current = true
      setLoading(true)
      setError('')

      let result
      if (deduplicate && deduplicationKey) {
        // Use request deduplication
        const key = typeof deduplicationKey === 'string' 
          ? deduplicationKey 
          : generateKey(deduplicationKey.endpoint, deduplicationKey.params)
        
        result = await execute(key, fetchFunction)
      } else {
        // Direct function call
        result = await fetchFunction()
      }

      setData(result)
    } catch (err) {
      console.error('Data fetching error:', err)
      setError(err.message || 'Failed to fetch data')
    } finally {
      loadingRef.current = false
      setLoading(false)
    }
  }, dependencies) // Only include actual dependencies, NOT loading state

  // Effect that runs when dependencies change
  useEffect(() => {
    if (immediate) {
      fetchData()
    }
  }, [fetchData]) // Include the memoized function in dependencies

  // Manual refetch function
  const refetch = useCallback(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    loading,
    error,
    refetch,
    isLoading: loadingRef.current // Alternative loading state that doesn't cause re-renders
  }
}

/**
 * Simplified hook for basic data fetching without deduplication
 */
export function useSimpleDataFetching(fetchFunction, dependencies = []) {
  return useDataFetching(fetchFunction, dependencies, { deduplicate: false })
}

/**
 * Hook specifically for API endpoints with automatic deduplication
 */
export function useApiDataFetching(endpoint, params = {}, fetchFunction, dependencies = []) {
  const deduplicationKey = { endpoint, params }
  
  return useDataFetching(
    fetchFunction, 
    dependencies, 
    { 
      deduplicate: true, 
      deduplicationKey 
    }
  )
}
