import { Router } from 'express';
import { ReviewController } from '../controllers';
import { authenticate } from '../middleware/auth';
import { validate } from '../middleware/validation';
import {
  createReviewValidation,
  mongoIdValidation,
  paginationValidation
} from '../utils/validation';

const router = Router();

// GET /api/products/:id/reviews
router.get(
  '/:id/reviews',
  validate([...mongoIdValidation(), ...paginationValidation]),
  ReviewController.getProductReviews
);

// POST /api/products/:id/reviews
router.post(
  '/:id/reviews',
  authenticate,
  validate(createReviewValidation),
  ReviewController.createReview
);

// PUT /api/reviews/:id
router.put(
  '/:id',
  authenticate,
  validate(mongoIdValidation()),
  ReviewController.updateReview
);

// DELETE /api/reviews/:id
router.delete(
  '/:id',
  authenticate,
  validate(mongoIdValidation()),
  ReviewController.deleteReview
);

export default router;
