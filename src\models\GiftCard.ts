import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IGiftCard extends Document {
  _id: string;
  code: string;
  amount: number;
  balance: number;
  recipientName: string;
  recipientEmail: string;
  message: string;
  senderName: string;
  purchasedBy: Types.ObjectId;
  isActive: boolean;
  expiryDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

const giftCardSchema = new Schema<IGiftCard>({
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true
  },
  amount: {
    type: Number,
    required: true,
    min: 10,
    max: 1000
  },
  balance: {
    type: Number,
    required: true,
    min: 0
  },
  recipientName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  recipientEmail: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  message: {
    type: String,
    trim: true,
    maxlength: 500,
    default: ''
  },
  senderName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  purchasedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  expiryDate: {
    type: Date,
    required: true
  }
}, {
  timestamps: true
});

// Index for better query performance
giftCardSchema.index({ code: 1 });
giftCardSchema.index({ recipientEmail: 1 });
giftCardSchema.index({ purchasedBy: 1 });
giftCardSchema.index({ isActive: 1 });
giftCardSchema.index({ expiryDate: 1 });

// Generate gift card code before saving
giftCardSchema.pre('save', function(next) {
  if (!this.code) {
    this.code = 'GC' + Date.now().toString() + Math.random().toString(36).substring(2, 8).toUpperCase();
  }
  if (!this.balance) {
    this.balance = this.amount;
  }
  next();
});

export const GiftCard = mongoose.model<IGiftCard>('GiftCard', giftCardSchema);
