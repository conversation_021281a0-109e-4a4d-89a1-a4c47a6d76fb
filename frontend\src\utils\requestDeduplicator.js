/**
 * Request deduplication utility to prevent duplicate API calls
 * This helps avoid the double-call issue caused by React development patterns
 */

class RequestDeduplicator {
  constructor() {
    this.pendingRequests = new Map()
    this.CACHE_DURATION = 1000 // 1 second cache to prevent rapid duplicates
  }

  /**
   * Execute a request with deduplication
   * If the same request is already pending, return the existing promise
   */
  async execute(key, requestFn) {
    const now = Date.now()
   
    // Check if we have a pending request for this key
    const existing = this.pendingRequests.get(key)
   
    if (existing) {
      // If the request is still fresh (within cache duration), return existing promise
      if (now - existing.timestamp < this.CACHE_DURATION) {
        console.log(`🔄 Deduplicating request: ${key}`)
        return existing.promise
      } else {
        // Clean up expired request
        this.pendingRequests.delete(key)
      }
    }

    // Create new request
    console.log(`📤 Executing new request: ${key}`)
    const promise = requestFn().finally(() => {
      // Clean up after request completes
      this.pendingRequests.delete(key)
    })

    // Store the pending request
    this.pendingRequests.set(key, {
      promise,
      timestamp: now
    })

    return promise
  }

  /**
   * Generate a cache key for API requests
   */
  generateKey(endpoint, params = {}) {
    const paramString = params ? JSON.stringify(params) : ''
    return `${endpoint}:${paramString}`
  }

  /**
   * Clear all pending requests (useful for cleanup)
   */
  clear() {
    this.pendingRequests.clear()
  }

  /**
   * Get the number of pending requests (for debugging)
   */
  getPendingCount() {
    return this.pendingRequests.size
  }
}

// Export a singleton instance
export const requestDeduplicator = new RequestDeduplicator()

/**
 * Hook for using request deduplication in React components
 */
export function useRequestDeduplication() {
  return {
    execute: requestDeduplicator.execute.bind(requestDeduplicator),
    generateKey: requestDeduplicator.generateKey.bind(requestDeduplicator),
    clear: requestDeduplicator.clear.bind(requestDeduplicator),
    getPendingCount: requestDeduplicator.getPendingCount.bind(requestDeduplicator),
  }
}
