import { Request, Response } from 'express';
import { BusinessProfile } from '../models';
import { sendSuccess, sendError } from '../utils/response';

export class BusinessController {
  static async getBusinessProfile(req: Request, res: Response): Promise<void> {
    try {
      let profile = await BusinessProfile.findOne();
      
      if (!profile) {
        // Create default profile if none exists
        profile = await BusinessProfile.create({
          name: 'Business Owner',
          email: '<EMAIL>',
          role: 'Hair Specialist',
          businessSince: '2020',
          yearsExperience: 5,
          businessHours: [
            { day: 'Monday', open: '09:00', close: '18:00', isClosed: false },
            { day: 'Tuesday', open: '09:00', close: '18:00', isClosed: false },
            { day: 'Wednesday', open: '09:00', close: '18:00', isClosed: false },
            { day: 'Thursday', open: '09:00', close: '18:00', isClosed: false },
            { day: 'Friday', open: '09:00', close: '18:00', isClosed: false },
            { day: 'Saturday', open: '10:00', close: '16:00', isClosed: false },
            { day: 'Sunday', open: '', close: '', isClosed: true }
          ]
        });
      }

      sendSuccess(res, 'Business profile retrieved successfully', profile);
    } catch (error) {
      console.error('Get business profile error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateBusinessProfile(req: Request, res: Response): Promise<void> {
    try {
      const updateData = req.body;

      let profile = await BusinessProfile.findOne();
      
      if (!profile) {
        profile = await BusinessProfile.create(updateData);
      } else {
        Object.assign(profile, updateData);
        await profile.save();
      }

      sendSuccess(res, 'Business profile updated successfully', profile);
    } catch (error) {
      console.error('Update business profile error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getBusinessHours(req: Request, res: Response): Promise<void> {
    try {
      const profile = await BusinessProfile.findOne();
      
      if (!profile) {
        sendError(res, 'Business profile not found');
        return;
      }

      sendSuccess(res, 'Business hours retrieved successfully', profile.businessHours);
    } catch (error) {
      console.error('Get business hours error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateBusinessHours(req: Request, res: Response): Promise<void> {
    try {
      const businessHours = req.body;

      let profile = await BusinessProfile.findOne();
      
      if (!profile) {
        sendError(res, 'Business profile not found');
        return;
      }

      profile.businessHours = businessHours;
      await profile.save();

      sendSuccess(res, 'Business hours updated successfully', profile.businessHours);
    } catch (error) {
      console.error('Update business hours error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
