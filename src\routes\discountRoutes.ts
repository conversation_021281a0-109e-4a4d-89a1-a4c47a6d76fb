import { Router } from 'express';
import { DiscountController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation, paginationValidation } from '../utils/validation';

const router = Router();

// POST /api/discount-codes/validate
router.post(
  '/validate',
  DiscountController.validateDiscountCode
);

// Admin routes
// GET /api/admin/discount-codes (admin only)
router.get(
  '/',
  authenticate,
  authorize('admin'),
  validate(paginationValidation),
  DiscountController.getDiscountCodes
);

// POST /api/admin/discount-codes (admin only)
router.post(
  '/',
  authenticate,
  authorize('admin'),
  DiscountController.createDiscountCode
);

// PUT /api/admin/discount-codes/:id (admin only)
router.put(
  '/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  DiscountController.updateDiscountCode
);

// DELETE /api/admin/discount-codes/:id (admin only)
router.delete(
  '/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  DiscountController.deleteDiscountCode
);

export default router;
