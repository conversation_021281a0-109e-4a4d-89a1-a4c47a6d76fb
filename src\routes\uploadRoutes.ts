import { Router } from 'express';
import { UploadController } from '../controllers';
import { authenticate } from '../middleware/auth';

const router = Router();

// POST /api/upload/image
router.post(
  '/image',
  authenticate,
  UploadController.uploadMiddleware,
  UploadController.uploadImage
);

// POST /api/upload/images
router.post(
  '/images',
  authenticate,
  UploadController.uploadMultipleImages
);

// GET /api/upload/image/:filename
router.get(
  '/image/:filename',
  UploadController.getImageInfo
);

// DELETE /api/upload/image/:filename
router.delete(
  '/image/:filename',
  authenticate,
  UploadController.deleteImage
);

export default router;
