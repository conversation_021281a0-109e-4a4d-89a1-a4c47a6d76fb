import { Router } from 'express';
import { GiftCardController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { paginationValidation } from '../utils/validation';

const router = Router();

// POST /api/gift-cards
router.post(
  '/',
  authenticate,
  GiftCardController.createGiftCard
);

// GET /api/gift-cards/:code/balance
router.get(
  '/:code/balance',
  GiftCardController.getGiftCardBalance
);

// GET /api/gift-cards/user
router.get(
  '/user',
  authenticate,
  validate(paginationValidation),
  GiftCardController.getUserGiftCards
);

// Admin routes
// GET /api/admin/gift-cards (admin only)
router.get(
  '/admin/all',
  authenticate,
  authorize('admin'),
  validate(paginationValidation),
  GiftCardController.getAllGiftCards
);

export default router;
