import { User } from '../models/User';
import { config } from '../config';

/**
 * Initialize server with default admin user
 * Creates admin user if it doesn't exist
 */
export const initializeServer = async (): Promise<void> => {
  try {
    console.log('🔧 Initializing server...');
    
    // Check if admin user exists
    const adminExists = await User.findOne({ email: config.ADMIN.EMAIL });
    
    if (!adminExists) {
      console.log('👤 Creating default admin user...');
      
      // Split the email to get name parts
      const emailParts = config.ADMIN.EMAIL.split('@')[0];
      const nameParts = emailParts.split(/[._-]/);
      
      const firstName = nameParts[0] || 'Admin';
      const lastName = nameParts[1] || 'User';
      const fullName = `${firstName} ${lastName}`;
      
      // Create admin user
      await User.create({
        name: fullName,
        firstName: firstName.charAt(0).toUpperCase() + firstName.slice(1),
        lastName: lastName.charAt(0).toUpperCase() + lastName.slice(1),
        email: config.ADMIN.EMAIL,
        phone: '+1234567890',
        password: config.ADMIN.PASSWORD,
        role: 'admin',
        isVerified: true
      });
      
      console.log(`✅ Default admin user created: ${config.ADMIN.EMAIL}`);
    } else {
      console.log(`✅ Admin user already exists: ${config.ADMIN.EMAIL}`);
    }
    
    console.log('🚀 Server initialization completed');
  } catch (error) {
    console.error('❌ Error during server initialization:', error);
    throw error;
  }
};
