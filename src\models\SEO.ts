import mongoose, { Schema, Document } from 'mongoose';

export interface ISEO extends Document {
  _id: string;
  title: string;
  description: string;
  keywords: string;
  ogImage: string;
  structuredData: any;
  createdAt: Date;
  updatedAt: Date;
}

const seoSchema = new Schema<ISEO>({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 60
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 160
  },
  keywords: {
    type: String,
    required: true,
    trim: true
  },
  ogImage: {
    type: String,
    trim: true,
    default: ''
  },
  structuredData: {
    type: Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

export const SEO = mongoose.model<ISEO>('SEO', seoSchema);
