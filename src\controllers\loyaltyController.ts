import { Request, Response } from 'express';
import { LoyaltyPoints, LoyaltyTransaction, LoyaltyReward } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class LoyaltyController {
  static async getUserLoyalty(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      let loyaltyPoints = await LoyaltyPoints.findOne({ user: req.user._id });

      if (!loyaltyPoints) {
        loyaltyPoints = await LoyaltyPoints.create({ user: req.user._id });
      }

      // Get recent transactions
      const recentTransactions = await LoyaltyTransaction.find({ user: req.user._id })
        .sort({ createdAt: -1 })
        .limit(10);

      // Get available rewards
      const availableRewards = await LoyaltyReward.find({ 
        isActive: true,
        pointsCost: { $lte: loyaltyPoints.points }
      }).sort({ pointsCost: 1 });

      sendSuccess(res, 'User loyalty data retrieved successfully', {
        points: loyaltyPoints.points,
        totalEarned: loyaltyPoints.totalEarned,
        totalRedeemed: loyaltyPoints.totalRedeemed,
        tier: loyaltyPoints.tier,
        recentTransactions,
        availableRewards
      });
    } catch (error) {
      console.error('Get user loyalty error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async redeemReward(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { rewardId } = req.body;

      const reward = await LoyaltyReward.findById(rewardId);
      if (!reward || !reward.isActive) {
        sendNotFound(res, 'Reward not found or inactive');
        return;
      }

      const loyaltyPoints = await LoyaltyPoints.findOne({ user: req.user._id });
      if (!loyaltyPoints) {
        sendError(res, 'Loyalty account not found');
        return;
      }

      if (loyaltyPoints.points < reward.pointsCost) {
        sendError(res, 'Insufficient points to redeem this reward');
        return;
      }

      // Deduct points
      loyaltyPoints.points -= reward.pointsCost;
      loyaltyPoints.totalRedeemed += reward.pointsCost;
      await loyaltyPoints.save();

      // Record transaction
      await LoyaltyTransaction.create({
        user: req.user._id,
        type: 'redeemed',
        points: -reward.pointsCost,
        description: `Redeemed: ${reward.name}`,
        rewardId: reward._id
      });

      sendSuccess(res, 'Reward redeemed successfully', {
        reward,
        remainingPoints: loyaltyPoints.points,
        tier: loyaltyPoints.tier
      });
    } catch (error) {
      console.error('Redeem reward error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async addPoints(userId: string, points: number, description: string, orderId?: string): Promise<void> {
    try {
      let loyaltyPoints = await LoyaltyPoints.findOne({ user: userId });

      if (!loyaltyPoints) {
        loyaltyPoints = await LoyaltyPoints.create({ user: userId });
      }

      loyaltyPoints.points += points;
      loyaltyPoints.totalEarned += points;
      await loyaltyPoints.save();

      // Record transaction
      await LoyaltyTransaction.create({
        user: userId,
        type: 'earned',
        points,
        description,
        orderId
      });
    } catch (error) {
      console.error('Add points error:', error);
    }
  }

  static async getLoyaltyRewards(req: Request, res: Response): Promise<void> {
    try {
      const { active } = req.query;
      const filter: any = {};

      if (active !== undefined) {
        filter.isActive = active === 'true';
      }

      const rewards = await LoyaltyReward.find(filter).sort({ pointsCost: 1 });

      sendSuccess(res, 'Loyalty rewards retrieved successfully', rewards);
    } catch (error) {
      console.error('Get loyalty rewards error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createLoyaltyReward(req: Request, res: Response): Promise<void> {
    try {
      const rewardData = req.body;

      const reward = await LoyaltyReward.create(rewardData);

      sendSuccess(res, 'Loyalty reward created successfully', reward, 201);
    } catch (error) {
      console.error('Create loyalty reward error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateLoyaltyReward(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const reward = await LoyaltyReward.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!reward) {
        sendNotFound(res, 'Loyalty reward not found');
        return;
      }

      sendSuccess(res, 'Loyalty reward updated successfully', reward);
    } catch (error) {
      console.error('Update loyalty reward error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteLoyaltyReward(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const reward = await LoyaltyReward.findByIdAndDelete(id);

      if (!reward) {
        sendNotFound(res, 'Loyalty reward not found');
        return;
      }

      sendSuccess(res, 'Loyalty reward deleted successfully');
    } catch (error) {
      console.error('Delete loyalty reward error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getLoyaltyTransactions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { page = 1, limit = 20, type } = req.query;
      const filter: any = { user: req.user._id };

      if (type) {
        filter.type = type;
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [transactions, total] = await Promise.all([
        LoyaltyTransaction.find(filter)
          .populate('orderId', 'orderNumber totalAmount')
          .populate('rewardId', 'name type')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        LoyaltyTransaction.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Loyalty transactions retrieved successfully', {
        transactions,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get loyalty transactions error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
