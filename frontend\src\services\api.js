import { API_CONFIG } from '../utils/constants.js'

/**
 * Base API service with authentication, error handling, and retry logic
 */
class ApiService {
  constructor() {
    this.baseURL = API_CONFIG.BASE_URL
    this.timeout = API_CONFIG.TIMEOUT
    this.retryAttempts = API_CONFIG.RETRY_ATTEMPTS
  }

  /**
   * Get authentication token from storage
   */
  getAuthToken() {
    // Check localStorage first (for "remember me")
    const rememberedToken = localStorage.getItem('authToken')
    if (rememberedToken) {
      console.log('Retrieved token from localStorage:', rememberedToken.substring(0, 20) + '...')
      return rememberedToken
    }

    // Check sessionStorage (for current session)
    const sessionToken = sessionStorage.getItem('authToken')
    if (sessionToken) {
      console.log('Retrieved token from sessionStorage:', sessionToken.substring(0, 20) + '...')
      return sessionToken
    }

    console.log('No auth token found in storage')
    return null
  }

  /**
   * Set authentication token in storage
   */
  setAuthToken(token, rememberMe = false) {
    console.log('Storing auth token:', token.substring(0, 20) + '...', 'rememberMe:', rememberMe)
    if (rememberMe) {
      localStorage.setItem('authToken', token)
      sessionStorage.removeItem('authToken')
      console.log('Token stored in localStorage')
    } else {
      sessionStorage.setItem('authToken', token)
      localStorage.removeItem('authToken')
      console.log('Token stored in sessionStorage')
    }
  }

  /**
   * Remove authentication token from storage
   */
  removeAuthToken() {
    localStorage.removeItem('authToken')
    sessionStorage.removeItem('authToken')
  }

  /**
   * Get default headers for API requests
   */
  getHeaders(includeAuth = true) {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }

    if (includeAuth) {
      const token = this.getAuthToken()
      if (token) {
        headers.Authorization = `Bearer ${token}`
        console.log('Including auth token in request:', token.substring(0, 20) + '...')
      } else {
        console.log('No auth token found for request')
      }
    }

    return headers
  }

  /**
   * Handle API response
   */
  async handleResponse(response) {
    const contentType = response.headers.get('content-type')

    let data
    if (contentType && contentType.includes('application/json')) {
      data = await response.json()
    } else {
      data = await response.text()
    }

    // Check for authentication errors in response data
    if (data && typeof data === 'object') {
      // Handle "Access token is required" message
      if (data.success === false &&
          (data.message === 'Access token is required' ||
           data.message === 'Authentication required' ||
           data.message === 'Invalid token' ||
           data.message === 'Token expired')) {
        console.log('Authentication error detected:', data.message)
        this.handleAuthError(data.message)

        const error = new Error(data.message)
        error.status = 401
        error.data = data
        throw error
      }
    }

    if (!response.ok) {
      // Handle HTTP 401 status
      if (response.status === 401) {
        console.log('HTTP 401 error detected')
        this.handleAuthError('Authentication required')
      }

      const error = new Error(data.message || `HTTP error! status: ${response.status}`)
      error.status = response.status
      error.data = data
      throw error
    }

    return data
  }

  /**
   * Handle authentication errors and redirect to login
   */
  handleAuthError(message) {
    console.log('Handling auth error:', message)

    // Remove invalid token
    this.removeAuthToken()

    // Emit auth error event for components to handle
    window.dispatchEvent(new CustomEvent('auth-error', {
      detail: { message }
    }))

    // Redirect to login page
    this.redirectToLogin()
  }

  /**
   * Redirect to login page
   */
  redirectToLogin() {
    // Check if we're already on login page to avoid infinite redirects
    const currentHash = window.location.hash.slice(1)
    if (currentHash === 'login' || currentHash === 'signup') {
      console.log('Already on auth page, skipping redirect')
      return
    }

    console.log('Redirecting to login page')

    // Store current page for redirect after login
    if (currentHash && currentHash !== 'home') {
      sessionStorage.setItem('redirectAfterLogin', currentHash)
    }

    // Redirect to login
    window.location.hash = 'login'

    // Show notification
    window.dispatchEvent(new CustomEvent('show-notification', {
      detail: {
        type: 'error',
        message: 'Please log in to continue'
      }
    }))
  }

  /**
   * Make API request with retry logic
   */
  async request(endpoint, options = {}, attempt = 1) {
    const url = `${this.baseURL}${endpoint}`
    
    const config = {
      method: 'GET',
      headers: this.getHeaders(options.includeAuth !== false),
      ...options
    }

    // Add timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)
    config.signal = controller.signal

    try {
      const response = await fetch(url, config)
      clearTimeout(timeoutId)
      return await this.handleResponse(response)
    } catch (error) {
      clearTimeout(timeoutId)
      
      // Retry logic for network errors
      if (attempt < this.retryAttempts && 
          (error.name === 'AbortError' || error.name === 'TypeError')) {
        console.warn(`Request failed, retrying... (${attempt}/${this.retryAttempts})`)
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
        return this.request(endpoint, options, attempt + 1)
      }

      throw error
    }
  }

  /**
   * GET request
   */
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const url = queryString ? `${endpoint}?${queryString}` : endpoint
    return this.request(url)
  }

  /**
   * POST request
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * PUT request
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  /**
   * DELETE request
   */
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE'
    })
  }

  /**
   * Upload file
   */
  async upload(endpoint, file, additionalData = {}) {
    const formData = new FormData()
    formData.append('file', file)
    
    // Add additional data to form
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key])
    })

    return this.request(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`
        // Don't set Content-Type for FormData, let browser set it
      },
      body: formData
    })
  }

  /**
   * Health check
   */
  async healthCheck() {
    return this.request('/health', { includeAuth: false })
  }
}

// Create and export singleton instance
const apiService = new ApiService()
export default apiService
