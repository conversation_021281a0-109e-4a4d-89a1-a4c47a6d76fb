import { API_CONFIG } from '../utils/constants.js'

/**
 * Base API service with authentication, error handling, and retry logic
 */
class ApiService {
  constructor() {
    this.baseURL = API_CONFIG.BASE_URL
    this.timeout = API_CONFIG.TIMEOUT
    this.retryAttempts = API_CONFIG.RETRY_ATTEMPTS
  }

  /**
   * Get authentication token from storage
   */
  getAuthToken() {
    // Check localStorage first (for "remember me")
    const rememberedToken = localStorage.getItem('authToken')
    if (rememberedToken) {
      return rememberedToken
    }
    
    // Check sessionStorage (for current session)
    const sessionToken = sessionStorage.getItem('authToken')
    return sessionToken
  }

  /**
   * Set authentication token in storage
   */
  setAuthToken(token, rememberMe = false) {
    if (rememberMe) {
      localStorage.setItem('authToken', token)
      sessionStorage.removeItem('authToken')
    } else {
      sessionStorage.setItem('authToken', token)
      localStorage.removeItem('authToken')
    }
  }

  /**
   * Remove authentication token from storage
   */
  removeAuthToken() {
    localStorage.removeItem('authToken')
    sessionStorage.removeItem('authToken')
  }

  /**
   * Get default headers for API requests
   */
  getHeaders(includeAuth = true) {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    }

    if (includeAuth) {
      const token = this.getAuthToken()
      if (token) {
        headers.Authorization = `Bearer ${token}`
      }
    }

    return headers
  }

  /**
   * Handle API response
   */
  async handleResponse(response) {
    const contentType = response.headers.get('content-type')
    
    let data
    if (contentType && contentType.includes('application/json')) {
      data = await response.json()
    } else {
      data = await response.text()
    }

    if (!response.ok) {
      // Handle authentication errors
      if (response.status === 401) {
        this.removeAuthToken()
        // Redirect to login or emit auth error event
        window.dispatchEvent(new CustomEvent('auth-error', { 
          detail: { message: 'Authentication required' } 
        }))
      }

      const error = new Error(data.message || `HTTP error! status: ${response.status}`)
      error.status = response.status
      error.data = data
      throw error
    }

    return data
  }

  /**
   * Make API request with retry logic
   */
  async request(endpoint, options = {}, attempt = 1) {
    const url = `${this.baseURL}${endpoint}`
    
    const config = {
      method: 'GET',
      headers: this.getHeaders(options.includeAuth !== false),
      ...options
    }

    // Add timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)
    config.signal = controller.signal

    try {
      const response = await fetch(url, config)
      clearTimeout(timeoutId)
      return await this.handleResponse(response)
    } catch (error) {
      clearTimeout(timeoutId)
      
      // Retry logic for network errors
      if (attempt < this.retryAttempts && 
          (error.name === 'AbortError' || error.name === 'TypeError')) {
        console.warn(`Request failed, retrying... (${attempt}/${this.retryAttempts})`)
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
        return this.request(endpoint, options, attempt + 1)
      }

      throw error
    }
  }

  /**
   * GET request
   */
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const url = queryString ? `${endpoint}?${queryString}` : endpoint
    return this.request(url)
  }

  /**
   * POST request
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * PUT request
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  /**
   * DELETE request
   */
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE'
    })
  }

  /**
   * Upload file
   */
  async upload(endpoint, file, additionalData = {}) {
    const formData = new FormData()
    formData.append('file', file)
    
    // Add additional data to form
    Object.keys(additionalData).forEach(key => {
      formData.append(key, additionalData[key])
    })

    return this.request(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`
        // Don't set Content-Type for FormData, let browser set it
      },
      body: formData
    })
  }

  /**
   * Health check
   */
  async healthCheck() {
    return this.request('/health', { includeAuth: false })
  }
}

// Create and export singleton instance
const apiService = new ApiService()
export default apiService
