import api from './api'

class ConsultationService {
  /**
   * Get available consultation time slots
   */
  async getAvailability(date, serviceId = null) {
    try {
      const params = new URLSearchParams({ date })
      if (serviceId) {
        params.append('service', serviceId)
      }
      
      const response = await api.get(`/consultation/availability?${params}`)
      return response
    } catch (error) {
      console.error('Error getting consultation availability:', error)
      throw error
    }
  }

  /**
   * Book consultation for guest users (main endpoint)
   * This handles all scenarios: guest, existing user, new user
   */
  async bookConsultation(consultationData) {
    try {
      const response = await api.post('/consultation/book', consultationData)
      
      // If a new user was created and tokens were returned, save them
      if (response.data?.token) {
        localStorage.setItem('token', response.data.token)
        if (response.data.refreshToken) {
          localStorage.setItem('refreshToken', response.data.refreshToken)
        }
        
        // Dispatch login event for other components
        window.dispatchEvent(new CustomEvent('user-login', {
          detail: {
            user: response.data.user,
            token: response.data.token
          }
        }))
      }
      
      return response
    } catch (error) {
      console.error('Error booking consultation:', error)
      throw error
    }
  }

  /**
   * Book consultation for authenticated users
   */
  async bookAuthenticatedConsultation(consultationData) {
    try {
      const response = await api.post('/consultation/book-authenticated', consultationData)
      return response
    } catch (error) {
      console.error('Error booking authenticated consultation:', error)
      throw error
    }
  }

  /**
   * Smart consultation booking that automatically detects if user is logged in
   */
  async smartBookConsultation(consultationData) {
    try {
      const token = localStorage.getItem('token')
      
      if (token) {
        // User is logged in, use authenticated endpoint
        return await this.bookAuthenticatedConsultation(consultationData)
      } else {
        // User is not logged in, use guest endpoint
        return await this.bookConsultation(consultationData)
      }
    } catch (error) {
      console.error('Error with smart consultation booking:', error)
      throw error
    }
  }
}

export const consultationService = new ConsultationService()
