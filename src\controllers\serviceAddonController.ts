import { Request, Response } from 'express';
import { ServiceAddon, Service } from '../models';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';

export class ServiceAddonController {
  static async getServiceAddons(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params; // Service ID
      const { active } = req.query;

      // Verify service exists
      const service = await Service.findById(id);
      if (!service) {
        sendNotFound(res, 'Service not found');
        return;
      }

      const filter: any = { service: id };
      if (active !== undefined) {
        filter.isActive = active === 'true';
      }

      const addons = await ServiceAddon.find(filter).sort({ name: 1 });

      sendSuccess(res, 'Service add-ons retrieved successfully', addons);
    } catch (error) {
      console.error('Get service add-ons error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createServiceAddon(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params; // Service ID
      const addonData = { ...req.body, service: id };

      // Verify service exists
      const service = await Service.findById(id);
      if (!service) {
        sendNotFound(res, 'Service not found');
        return;
      }

      const addon = await ServiceAddon.create(addonData);

      sendCreated(res, 'Service add-on created successfully', addon);
    } catch (error) {
      console.error('Create service add-on error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateServiceAddon(req: Request, res: Response): Promise<void> {
    try {
      const { addonId } = req.params;
      const updateData = req.body;

      const addon = await ServiceAddon.findByIdAndUpdate(
        addonId,
        updateData,
        { new: true, runValidators: true }
      );

      if (!addon) {
        sendNotFound(res, 'Service add-on not found');
        return;
      }

      sendSuccess(res, 'Service add-on updated successfully', addon);
    } catch (error) {
      console.error('Update service add-on error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteServiceAddon(req: Request, res: Response): Promise<void> {
    try {
      const { addonId } = req.params;

      const addon = await ServiceAddon.findByIdAndDelete(addonId);

      if (!addon) {
        sendNotFound(res, 'Service add-on not found');
        return;
      }

      sendSuccess(res, 'Service add-on deleted successfully');
    } catch (error) {
      console.error('Delete service add-on error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
