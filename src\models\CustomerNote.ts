import mongoose, { Schema, Document, Types } from 'mongoose';

export interface ICustomerNote extends Document {
  _id: string;
  customer: Types.ObjectId;
  content: string;
  createdBy: Types.ObjectId;
  isPrivate: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const customerNoteSchema = new Schema<ICustomerNote>({
  customer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: true,
    trim: true,
    maxlength: 2000
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isPrivate: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better query performance
customerNoteSchema.index({ customer: 1 });
customerNoteSchema.index({ createdBy: 1 });
customerNoteSchema.index({ createdAt: -1 });

export const CustomerNote = mongoose.model<ICustomerNote>('CustomerNote', customerNoteSchema);
