import { Router } from 'express';
import { UserController } from '../controllers';
import { authenticate } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation } from '../utils/validation';

const router = Router();

// GET /api/users/profile
router.get(
  '/profile',
  authenticate,
  UserController.getProfile
);

// PUT /api/users/profile
router.put(
  '/profile',
  authenticate,
  UserController.updateProfile
);

// GET /api/users/favorites
router.get(
  '/favorites',
  authenticate,
  UserController.getFavorites
);

// POST /api/users/favorites
router.post(
  '/favorites',
  authenticate,
  UserController.addToFavorites
);

// DELETE /api/users/favorites/:productId
router.delete(
  '/favorites/:productId',
  authenticate,
  validate(mongoIdValidation('productId')),
  UserController.removeFromFavorites
);

// PUT /api/users/notification-preferences
router.put(
  '/notification-preferences',
  authenticate,
  UserController.updateNotificationPreferences
);

// PUT /api/users/password
router.put(
  '/password',
  authenticate,
  UserController.changePassword
);

export default router;
