import apiService from './api.js'

/**
 * Product service for handling product-related API calls
 */
class ProductService {
  /**
   * Get all products with optional filtering and pagination
   */
  async getProducts(params = {}) {
    try {
      const response = await apiService.get('/products', params)
      return response
    } catch (error) {
      console.error('Get products error:', error)
      throw error
    }
  }

  /**
   * Get product by ID
   */
  async getProduct(productId) {
    try {
      const response = await apiService.get(`/products/${productId}`)
      return response
    } catch (error) {
      console.error('Get product error:', error)
      throw error
    }
  }

  /**
   * Get product categories
   */
  async getCategories() {
    try {
      const response = await apiService.get('/products/categories')
      return response
    } catch (error) {
      console.error('Get categories error:', error)
      throw error
    }
  }

  /**
   * Search products
   */
  async searchProducts(query, filters = {}) {
    try {
      const params = {
        search: query,
        ...filters
      }
      const response = await apiService.get('/products', params)
      return response
    } catch (error) {
      console.error('Search products error:', error)
      throw error
    }
  }

  /**
   * Get product reviews
   */
  async getProductReviews(productId, params = {}) {
    try {
      const response = await apiService.get(`/products/${productId}/reviews`, params)
      return response
    } catch (error) {
      console.error('Get product reviews error:', error)
      throw error
    }
  }

  /**
   * Submit product review
   */
  async submitReview(productId, reviewData) {
    try {
      const response = await apiService.post(`/products/${productId}/reviews`, reviewData)
      return response
    } catch (error) {
      console.error('Submit review error:', error)
      throw error
    }
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts() {
    try {
      const response = await apiService.get('/products', { featured: true })
      return response
    } catch (error) {
      console.error('Get featured products error:', error)
      throw error
    }
  }

  /**
   * Get related products
   */
  async getRelatedProducts(productId) {
    try {
      const response = await apiService.get(`/products/${productId}/related`)
      return response
    } catch (error) {
      console.error('Get related products error:', error)
      throw error
    }
  }

  // Admin methods (require admin authentication)

  /**
   * Create new product (Admin)
   */
  async createProduct(productData) {
    try {
      const response = await apiService.post('/admin/products', productData)
      return response
    } catch (error) {
      console.error('Create product error:', error)
      throw error
    }
  }

  /**
   * Update product (Admin)
   */
  async updateProduct(productId, productData) {
    try {
      const response = await apiService.put(`/admin/products/${productId}`, productData)
      return response
    } catch (error) {
      console.error('Update product error:', error)
      throw error
    }
  }

  /**
   * Delete product (Admin)
   */
  async deleteProduct(productId) {
    try {
      const response = await apiService.delete(`/admin/products/${productId}`)
      return response
    } catch (error) {
      console.error('Delete product error:', error)
      throw error
    }
  }

  /**
   * Get admin products list (Admin)
   */
  async getAdminProducts(params = {}) {
    try {
      const response = await apiService.get('/admin/products', params)
      return response
    } catch (error) {
      console.error('Get admin products error:', error)
      throw error
    }
  }

  /**
   * Update product inventory (Admin)
   */
  async updateInventory(productId, inventoryData) {
    try {
      const response = await apiService.put(`/admin/products/${productId}/inventory`, inventoryData)
      return response
    } catch (error) {
      console.error('Update inventory error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const productService = new ProductService()
export default productService
