import { Request, Response } from 'express';
import { DiscountCode } from '../models';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';

export class DiscountController {
  static async getDiscountCodes(req: Request, res: Response): Promise<void> {
    try {
      const { page = 1, limit = 20, active } = req.query;
      const filter: any = {};

      if (active !== undefined) {
        filter.isActive = active === 'true';
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [discountCodes, total] = await Promise.all([
        DiscountCode.find(filter)
          .populate('products', 'name price')
          .populate('services', 'name price')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        DiscountCode.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Discount codes retrieved successfully', {
        discountCodes,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get discount codes error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createDiscountCode(req: Request, res: Response): Promise<void> {
    try {
      const discountData = req.body;

      // Validate percentage discount
      if (discountData.type === 'percentage' && discountData.value > 100) {
        sendError(res, 'Percentage discount cannot exceed 100%');
        return;
      }

      const discountCode = await DiscountCode.create(discountData);
      await discountCode.populate('products', 'name price');
      await discountCode.populate('services', 'name price');

      sendCreated(res, 'Discount code created successfully', discountCode);
    } catch (error) {
      console.error('Create discount code error:', error);
      if ((error as any).code === 11000) {
        sendError(res, 'Discount code already exists');
      } else {
        sendError(res, (error as Error).message);
      }
    }
  }

  static async updateDiscountCode(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Validate percentage discount
      if (updateData.type === 'percentage' && updateData.value > 100) {
        sendError(res, 'Percentage discount cannot exceed 100%');
        return;
      }

      const discountCode = await DiscountCode.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).populate('products', 'name price')
       .populate('services', 'name price');

      if (!discountCode) {
        sendNotFound(res, 'Discount code not found');
        return;
      }

      sendSuccess(res, 'Discount code updated successfully', discountCode);
    } catch (error) {
      console.error('Update discount code error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteDiscountCode(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const discountCode = await DiscountCode.findByIdAndDelete(id);

      if (!discountCode) {
        sendNotFound(res, 'Discount code not found');
        return;
      }

      sendSuccess(res, 'Discount code deleted successfully');
    } catch (error) {
      console.error('Delete discount code error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async validateDiscountCode(req: Request, res: Response): Promise<void> {
    try {
      const { code } = req.body;

      if (!code) {
        sendError(res, 'Discount code is required');
        return;
      }

      const discountCode = await DiscountCode.findOne({ 
        code: code.toUpperCase(),
        isActive: true 
      }).populate('products', 'name price')
       .populate('services', 'name price');

      if (!discountCode) {
        sendError(res, 'Invalid discount code', undefined, 404);
        return;
      }

      // Check if expired
      if (discountCode.expiryDate < new Date()) {
        sendError(res, 'Discount code has expired', undefined, 400);
        return;
      }

      // Check usage limit
      if (discountCode.maxUses && discountCode.currentUses >= discountCode.maxUses) {
        sendError(res, 'Discount code usage limit reached', undefined, 400);
        return;
      }

      sendSuccess(res, 'Discount code is valid', {
        code: discountCode.code,
        type: discountCode.type,
        value: discountCode.value,
        minPurchase: discountCode.minPurchase,
        products: discountCode.products,
        services: discountCode.services
      });
    } catch (error) {
      console.error('Validate discount code error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async applyDiscountCode(discountCode: string, orderTotal: number): Promise<number> {
    try {
      const discount = await DiscountCode.findOne({ 
        code: discountCode.toUpperCase(),
        isActive: true 
      });

      if (!discount || discount.expiryDate < new Date()) {
        return 0;
      }

      if (orderTotal < discount.minPurchase) {
        return 0;
      }

      if (discount.maxUses && discount.currentUses >= discount.maxUses) {
        return 0;
      }

      let discountAmount = 0;
      if (discount.type === 'percentage') {
        discountAmount = (orderTotal * discount.value) / 100;
      } else {
        discountAmount = discount.value;
      }

      // Increment usage count
      discount.currentUses += 1;
      await discount.save();

      return Math.min(discountAmount, orderTotal);
    } catch (error) {
      console.error('Apply discount code error:', error);
      return 0;
    }
  }
}
