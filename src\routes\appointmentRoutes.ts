import { Router } from 'express';
import { AppointmentController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import {
  createAppointmentValidation,
  updateAppointmentValidation,
  mongoIdValidation,
  paginationValidation
} from '../utils/validation';

const router = Router();

// GET /api/appointments/availability
router.get(
  '/availability',
  AppointmentController.getAvailability
);

// POST /api/appointments
router.post(
  '/',
  authenticate,
  validate(createAppointmentValidation),
  AppointmentController.createAppointment
);

// GET /api/appointments/user
router.get(
  '/user',
  authenticate,
  AppointmentController.getUserAppointments
);

// PUT /api/appointments/:id
router.put(
  '/:id',
  authenticate,
  validate(updateAppointmentValidation),
  AppointmentController.updateAppointment
);

// DELETE /api/appointments/:id
router.delete(
  '/:id',
  authenticate,
  validate(mongoIdValidation()),
  AppointmentController.cancelAppointment
);

// Admin routes
// GET /api/appointments (admin only)
router.get(
  '/',
  authenticate,
  authorize('admin'),
  validate(paginationValidation),
  AppointmentController.getAllAppointments
);

export default router;
