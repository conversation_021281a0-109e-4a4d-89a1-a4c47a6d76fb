import { useState, useRef, useEffect } from 'react'
import { FiImage, FiAlertCircle } from 'react-icons/fi'

const OptimizedImage = ({ 
  src, 
  alt, 
  className = '', 
  fallbackSrc = null,
  placeholder = true,
  lazy = true,
  onLoad = null,
  onError = null,
  ...props 
}) => {
  const [imageState, setImageState] = useState('loading')
  const [currentSrc, setCurrentSrc] = useState(lazy ? null : src)
  const imgRef = useRef(null)
  const observerRef = useRef(null)

  // Lazy loading with Intersection Observer
  useEffect(() => {
    if (!lazy || !imgRef.current) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setCurrentSrc(src)
            observerRef.current?.unobserve(entry.target)
          }
        })
      },
      { threshold: 0.1 }
    )

    observerRef.current.observe(imgRef.current)

    return () => {
      observerRef.current?.disconnect()
    }
  }, [src, lazy])

  const handleLoad = (e) => {
    setImageState('loaded')
    onLoad?.(e)
  }

  const handleError = (e) => {
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc)
      setImageState('loading')
    } else {
      setImageState('error')
    }
    onError?.(e)
  }

  const renderPlaceholder = () => (
    <div className={`bg-gray-200 flex items-center justify-center ${className}`} {...props}>
      <FiImage className="w-8 h-8 text-gray-400" />
    </div>
  )

  const renderError = () => (
    <div className={`bg-gray-100 flex flex-col items-center justify-center ${className}`} {...props}>
      <FiAlertCircle className="w-8 h-8 text-gray-400 mb-2" />
      <span className="text-xs text-gray-500 text-center px-2">
        Failed to load image
      </span>
    </div>
  )

  if (imageState === 'error') {
    return renderError()
  }

  if (!currentSrc && lazy) {
    return (
      <div ref={imgRef} className={className} {...props}>
        {placeholder && renderPlaceholder()}
      </div>
    )
  }

  return (
    <div className="relative">
      {imageState === 'loading' && placeholder && (
        <div className={`absolute inset-0 ${className}`}>
          {renderPlaceholder()}
        </div>
      )}
      <img
        ref={imgRef}
        src={currentSrc}
        alt={alt}
        className={`${className} ${imageState === 'loading' ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onLoad={handleLoad}
        onError={handleError}
        loading={lazy ? 'lazy' : 'eager'}
        {...props}
      />
    </div>
  )
}

export default OptimizedImage
