import { useState, useEffect, useCallback, useRef } from 'react'
import {
  FiUsers,
  FiCalendar,
  FiShoppingBag,
  FiDollarSign,
  FiSettings,
  FiBarChart,
  FiPackage,
  FiUserCheck,
  FiEdit3,
  FiTrash2,
  FiPlus,
  FiEye,
  FiX,
  FiEdit,
  FiImage,
  FiType,
  FiSave,
  FiUpload,
  FiHome,
  FiUser,
  FiLogOut,
  FiMenu
} from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'
import { useRouter } from '../../hooks/useRouter'
import { adminService } from '../../services'
import Loading from '../../components/Loading'

const AdminDashboard = ({ onNavigate, onLogout }) => {
  const { branding } = useBranding()
  const { parseCurrentRoute, navigateToSubRoute } = useRouter()

  // Initialize activeTab from URL if available
  const [activeTab, setActiveTab] = useState(() => {
    const { mainRoute, subRoute } = parseCurrentRoute()
    return (mainRoute === 'admin-dashboard' && subRoute) ? subRoute : 'overview'
  })

  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [viewingItem, setViewingItem] = useState(null)
  const [toast, setToast] = useState(null)
  const [confirmDialog, setConfirmDialog] = useState(null)
  const [modalType, setModalType] = useState('') // 'add', 'edit', 'view'
  const [activeBrandingSection, setActiveBrandingSection] = useState('global')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [sectionLoading, setSectionLoading] = useState({
    dashboard: false,
    appointments: false,
    customers: false,
    orders: false,
    products: false
  })
  const loadingRef = useRef(false)

  // Data state with initial mock data
  const [dashboardStats, setDashboardStats] = useState(null)
  const [appointments, setAppointments] = useState([])
  const [customers, setCustomers] = useState([])
  const [orders, setOrders] = useState([
    {
      id: 'ORD-001',
      customer: 'Jennifer Lee',
      email: '<EMAIL>',
      phone: '(*************',
      items: [
        { name: 'Loc Maintenance Shampoo', quantity: 1, price: 24.99 },
        { name: 'Natural Hair Oil', quantity: 1, price: 18.99 }
      ],
      total: 43.98,
      status: 'pending',
      date: '2024-02-12',
      shippingAddress: '123 Main St, City, State 12345',
      paymentMethod: 'Credit Card'
    },
    {
      id: 'ORD-002',
      customer: 'Lisa Wilson',
      email: '<EMAIL>',
      phone: '(*************',
      items: [
        { name: 'Deep Conditioning Mask', quantity: 1, price: 28.99 }
      ],
      total: 28.99,
      status: 'shipped',
      date: '2024-02-11',
      shippingAddress: '456 Oak Ave, City, State 12345',
      paymentMethod: 'PayPal',
      trackingNumber: 'TRK123456789'
    },
    {
      id: 'ORD-003',
      customer: 'Maria Garcia',
      email: '<EMAIL>',
      phone: '(*************',
      items: [
        { name: 'Loc Twisting Gel', quantity: 2, price: 16.99 },
        { name: 'Scalp Serum', quantity: 1, price: 22.99 }
      ],
      total: 56.97,
      status: 'delivered',
      date: '2024-02-09',
      shippingAddress: '789 Pine St, City, State 12345',
      paymentMethod: 'Credit Card',
      trackingNumber: 'TRK987654321'
    }
  ])
  const [products, setProducts] = useState([
    {
      id: 1,
      name: 'Loc Maintenance Shampoo',
      price: 24.99,
      stock: 45,
      sold: 23,
      status: 'active',
      category: 'shampoo',
      description: 'Gentle cleansing shampoo for locs'
    },
    {
      id: 2,
      name: 'Natural Hair Oil Blend',
      price: 18.99,
      stock: 12,
      sold: 67,
      status: 'low-stock',
      category: 'oil',
      description: 'Nourishing oil blend for healthy hair'
    },
    {
      id: 3,
      name: 'Deep Conditioning Mask',
      price: 28.99,
      stock: 30,
      sold: 45,
      status: 'active',
      category: 'conditioner',
      description: 'Intensive moisture treatment'
    },
    {
      id: 4,
      name: 'Loc Twisting Gel',
      price: 16.99,
      stock: 8,
      sold: 89,
      status: 'low-stock',
      category: 'styling',
      description: 'Strong hold gel for styling'
    }
  ])
  const [adminData, setAdminData] = useState({
    name: 'Tina Williams',
    email: '<EMAIL>',
    role: 'Owner & Master Stylist',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    businessSince: '2014',
    certifications: ['Master Loctician', 'Natural Hair Specialist'],
    yearsExperience: 10
  })

  // Load branding content from localStorage or use defaults
  const getInitialBrandingContent = () => {
    const saved = localStorage.getItem('goldielocs_branding_content')
    if (saved) {
      try {
        return JSON.parse(saved)
      } catch (error) {
        console.error('Error parsing saved branding content:', error)
      }
    }
    return {
    // Site-wide/Global content
    global: {
      siteName: 'Goldie Locs',
      tagline: 'By Tina',
      logo: 'https://via.placeholder.com/150x50/f3d016/000000?text=Goldie+Locs',
      phone: '(*************',
      email: '<EMAIL>',
      address: '123 Beauty Street, Atlanta, GA 30309',
      instagram: '@goldielocsbytina',
      facebook: 'GoldieLocsByTina',
      twitter: '@goldielocs',
      youtube: 'GoldieLocsTV'
    },

    // Home page content
    home: {
      heroTitle: 'Beautiful',
      heroTitleAccent: 'Locs & Natural',
      heroTitleEnd: 'Hair Care',
      heroSubtitle: 'Transform your hair with professional locs installation and maintenance. Over 10 years of experience in creating stunning, healthy locs.',
      heroButtonPrimary: 'Book Consultation',
      heroButtonSecondary: 'View Our Work',
      heroImage: 'https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600',

      // Features section
      featuresTitle: 'Why Choose Goldie Locs?',
      featuresSubtitle: 'Professional expertise meets personalized care',
      feature1Title: 'Expert Loctician',
      feature1Description: 'Over 10 years of experience in locs installation and maintenance',
      feature1Icon: 'FiStar',
      feature2Title: 'Natural Products',
      feature2Description: 'Premium, natural hair care products for healthy locs',
      feature2Icon: 'FiHeart',
      feature3Title: 'Personalized Care',
      feature3Description: 'Customized treatments based on your hair type and goals',
      feature3Icon: 'FiUser',



      // CTA section
      ctaTitle: 'Ready to Transform Your Hair?',
      ctaSubtitle: 'Book your consultation today and start your locs journey',
      ctaButtonText: 'Schedule Consultation',
      ctaBackgroundImage: 'https://images.pexels.com/photos/3993449/pexels-photo-3993449.jpeg'
    },

    // Services page content
    services: {
      pageTitle: 'Professional Locs & Natural Hair Services',
      pageSubtitle: 'Expert care for your natural hair journey',

      // Service categories
      locsInstallation: {
        title: 'Locs Installation',
        description: 'Professional installation of micro locs, traditional locs, and sister locs',
        price: 'Starting at $200',
        duration: '4-8 hours',
        image: 'https://images.pexels.com/photos/3993449/pexels-photo-3993449.jpeg'
      },
      locsMaintenance: {
        title: 'Locs Maintenance',
        description: 'Regular maintenance, root touch-ups, and styling',
        price: 'Starting at $80',
        duration: '2-3 hours',
        image: 'https://images.pexels.com/photos/3993449/pexels-photo-3993449.jpeg'
      },
      naturalHairCare: {
        title: 'Natural Hair Care',
        description: 'Protective styling, treatments, and natural hair maintenance',
        price: 'Starting at $60',
        duration: '1-2 hours',
        image: 'https://images.pexels.com/photos/3993449/pexels-photo-3993449.jpeg'
      },

      // Why choose section
      whyChooseTitle: 'Why Choose Our Services?',
      whyChooseSubtitle: 'Professional expertise you can trust',
      benefit1: 'Certified and experienced loctician',
      benefit2: 'Premium natural hair products',
      benefit3: 'Personalized consultation and care',
      benefit4: 'Flexible scheduling and appointments',

      // CTA section
      ctaTitle: 'Book Your Service Today',
      ctaSubtitle: 'Transform your hair with professional care',
      ctaButtonText: 'Schedule Appointment'
    },

    // Shop page content
    shop: {
      pageTitle: 'Premium Hair Care Products',
      pageSubtitle: 'Natural products for healthy locs and natural hair',

      // Categories
      categories: ['All Products', 'Locs Care', 'Natural Hair', 'Styling', 'Treatments'],

      // Newsletter section
      newsletterTitle: 'Stay Updated',
      newsletterSubtitle: 'Get hair care tips and product updates',
      newsletterButtonText: 'Subscribe',
      newsletterPlaceholder: 'Enter your email address'
    },

    // Consultation page content
    consultation: {
      pageTitle: 'Book Your Consultation',
      pageSubtitle: 'Let\'s discuss your hair goals and create a personalized plan',

      // Form labels
      nameLabel: 'Full Name',
      emailLabel: 'Email Address',
      phoneLabel: 'Phone Number',
      serviceLabel: 'Service Interest',
      dateLabel: 'Preferred Date',
      timeLabel: 'Preferred Time',
      messageLabel: 'Additional Notes',
      submitButtonText: 'Book Consultation',

      // Consultation info
      infoTitle: 'What to Expect',
      infoSubtitle: 'Your consultation includes:',
      info1: '30-minute one-on-one consultation',
      info2: 'Hair assessment and analysis',
      info3: 'Personalized care plan',
      info4: 'Product recommendations',

      // Success message
      successTitle: 'Consultation Booked!',
      successMessage: 'We\'ll contact you within 24 hours to confirm your appointment.'
    },

    // Login page content
    login: {
      pageTitle: 'Welcome Back',
      pageSubtitle: 'Sign in to your account',
      emailLabel: 'Email Address',
      passwordLabel: 'Password',
      rememberLabel: 'Remember me',
      forgotPasswordText: 'Forgot your password?',
      signInButtonText: 'Sign In',
      signUpText: 'Don\'t have an account?',
      signUpLinkText: 'Sign up here',

      // Footer links
      footerText: 'Need help?',
      contactLinkText: 'Contact Support',
      privacyLinkText: 'Privacy Policy',
      termsLinkText: 'Terms of Service'
    },

    // Footer content
    footer: {
      description: 'Professional locs and natural hair care services. Specializing in micro locs, traditional locs, and natural hair maintenance.',
      quickLinksTitle: 'Quick Links',
      servicesTitle: 'Services',
      contactTitle: 'Contact Info',
      followTitle: 'Follow Us',
      copyrightText: '© 2024 Goldie Locs. All rights reserved.',

      // Quick links
      quickLinks: ['Home', 'Services', 'Shop', 'Consultation', 'Login'],

      // Services links
      serviceLinks: ['Locs Installation', 'Locs Maintenance', 'Natural Hair Care', 'Consultations']
    },

    // Sign Up page content
    signup: {
      pageTitle: 'Create Your Account',
      pageSubtitle: 'Join our community and start your locs journey',
      nameLabel: 'Full Name',
      emailLabel: 'Email Address',
      passwordLabel: 'Password',
      confirmPasswordLabel: 'Confirm Password',
      phoneLabel: 'Phone Number (Optional)',
      agreeTermsText: 'I agree to the',
      termsLinkText: 'Terms of Service',
      andText: 'and',
      privacyLinkText: 'Privacy Policy',
      signUpButtonText: 'Create Account',
      loginText: 'Already have an account?',
      loginLinkText: 'Sign in here',

      // Benefits section
      benefitsTitle: 'Why Join Goldie Locs?',
      benefit1: 'Book appointments online 24/7',
      benefit2: 'Track your locs journey and progress',
      benefit3: 'Get personalized hair care recommendations',
      benefit4: 'Access exclusive member discounts'
    },

    // Cart page content
    cart: {
      pageTitle: 'Shopping Cart',
      emptyCartTitle: 'Your cart is empty',
      emptyCartMessage: 'Add some products to get started',
      continueShoppingText: 'Continue Shopping',
      itemText: 'Item',
      quantityText: 'Quantity',
      priceText: 'Price',
      totalText: 'Total',
      subtotalText: 'Subtotal',
      shippingText: 'Shipping',
      taxText: 'Tax',
      grandTotalText: 'Grand Total',
      checkoutButtonText: 'Proceed to Checkout',
      updateCartText: 'Update Cart',
      removeText: 'Remove',

      // Shipping info
      freeShippingThreshold: 'Free shipping on orders over $50',
      shippingCalculated: 'Shipping calculated at checkout'
    },

    // Product Detail page content
    productDetail: {
      addToCartText: 'Add to Cart',
      quantityText: 'Quantity',
      descriptionTitle: 'Description',
      ingredientsTitle: 'Ingredients',
      howToUseTitle: 'How to Use',
      reviewsTitle: 'Customer Reviews',
      relatedProductsTitle: 'Related Products',
      inStockText: 'In Stock',
      outOfStockText: 'Out of Stock',
      lowStockText: 'Only {count} left in stock',

      // Product tabs
      overviewTab: 'Overview',
      ingredientsTab: 'Ingredients',
      reviewsTab: 'Reviews',

      // Review section
      writeReviewText: 'Write a Review',
      noReviewsText: 'No reviews yet. Be the first to review this product!',
      ratingText: 'Rating',
      reviewText: 'Review'
    },

    // User Dashboard content
    userDashboard: {
      welcomeTitle: 'Welcome back, {name}!',
      welcomeSubtitle: 'Manage your appointments and track your locs journey',

      // Quick stats
      nextAppointmentText: 'Next Appointment',
      totalAppointmentsText: 'Total Appointments',
      memberSinceText: 'Member Since',
      loyaltyPointsText: 'Loyalty Points',

      // Sections
      appointmentsTitle: 'My Appointments',
      ordersTitle: 'Order History',
      profileTitle: 'Profile Settings',
      favoritesTitle: 'Favorite Products',

      // Buttons
      bookAppointmentText: 'Book New Appointment',
      viewAllOrdersText: 'View All Orders',
      updateProfileText: 'Update Profile',

      // Empty states
      noAppointmentsText: 'No upcoming appointments',
      noOrdersText: 'No orders yet',
      noFavoritesText: 'No favorite products yet'
    }
    }
  }

  // Branding content state - organized by page
  const [brandingContent, setBrandingContent] = useState(getInitialBrandingContent)

  // Save branding content to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('goldielocs_branding_content', JSON.stringify(brandingContent))
  }, [brandingContent])

  // API Loading Functions
  const loadDashboardStats = useCallback(async () => {
    if (loadingRef.current) return

    try {
      setSectionLoading(prev => ({ ...prev, dashboard: true }))
      const response = await adminService.getDashboardStats()
      if (response.success) {
        setDashboardStats(response.data)
      }
    } catch (error) {
      console.error('Error loading dashboard stats:', error)
      setError('Failed to load dashboard statistics')
    } finally {
      setSectionLoading(prev => ({ ...prev, dashboard: false }))
    }
  }, [])

  const loadAppointments = useCallback(async () => {
    if (loadingRef.current) return

    try {
      setSectionLoading(prev => ({ ...prev, appointments: true }))
      const response = await adminService.getAppointments({ page: 1, limit: 20 })
      if (response.success) {
        setAppointments(response.data.appointments || [])
      }
    } catch (error) {
      console.error('Error loading appointments:', error)
      setError('Failed to load appointments')
    } finally {
      setSectionLoading(prev => ({ ...prev, appointments: false }))
    }
  }, [])

  const loadCustomers = useCallback(async () => {
    if (loadingRef.current) return

    try {
      setSectionLoading(prev => ({ ...prev, customers: true }))
      const response = await adminService.getCustomers({ page: 1, limit: 20 })
      if (response.success) {
        setCustomers(response.data.customers || [])
      }
    } catch (error) {
      console.error('Error loading customers:', error)
      setError('Failed to load customers')
    } finally {
      setSectionLoading(prev => ({ ...prev, customers: false }))
    }
  }, [])

  const loadOrders = useCallback(async () => {
    if (loadingRef.current) return

    try {
      setSectionLoading(prev => ({ ...prev, orders: true }))
      const response = await adminService.getOrders({ page: 1, limit: 20 })
      if (response.success) {
        setOrders(response.data.orders || [])
      }
    } catch (error) {
      console.error('Error loading orders:', error)
      setError('Failed to load orders')
    } finally {
      setSectionLoading(prev => ({ ...prev, orders: false }))
    }
  }, [])

  const loadProducts = useCallback(async () => {
    if (loadingRef.current) return

    try {
      setSectionLoading(prev => ({ ...prev, products: true }))
      const response = await adminService.getProducts({ page: 1, limit: 20 })
      if (response.success) {
        setProducts(response.data.products || [])
      }
    } catch (error) {
      console.error('Error loading products:', error)
      setError('Failed to load products')
    } finally {
      setSectionLoading(prev => ({ ...prev, products: false }))
    }
  }, [])

  // Load data based on active tab
  const loadDataForTab = useCallback((tabId) => {
    switch (tabId) {
      case 'overview':
        loadDashboardStats()
        break
      case 'appointments':
        loadAppointments()
        break
      case 'customers':
        loadCustomers()
        break
      case 'orders':
        loadOrders()
        break
      case 'products':
        loadProducts()
        break
      default:
        break
    }
  }, [loadDashboardStats, loadAppointments, loadCustomers, loadOrders, loadProducts])

  // Update URL when tab changes and load required data
  const handleTabChange = (tabId) => {
    setActiveTab(tabId)
    // Don't replace history - allow proper back navigation
    navigateToSubRoute('admin-dashboard', tabId, { replace: false, scrollToTop: false })

    // Load data based on the selected tab
    loadDataForTab(tabId)
  }

  // Memoized data fetching function
  const fetchDashboardData = useCallback(async () => {
    if (loadingRef.current) return // Prevent duplicate calls

    try {
      loadingRef.current = true
      setIsLoading(true)
      setError('')

      // Simulate API calls - replace with actual API calls
      await new Promise(resolve => setTimeout(resolve, 500))

      // In a real app, you would fetch data here:
      // const [appointmentsRes, ordersRes, productsRes] = await Promise.all([
      //   appointmentService.getAll(),
      //   orderService.getAll(),
      //   productService.getAll()
      // ])

      // Load initial data for overview tab
      await loadDashboardStats()

    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      setError('Failed to load dashboard data')
    } finally {
      setIsLoading(false)
      loadingRef.current = false
    }
  }, [loadDashboardStats])

  // Load initial data on component mount
  useEffect(() => {
    const initializeDashboard = async () => {
      setIsLoading(true)
      setError('')

      try {
        // Set initial URL if no sub-route is present
        const { mainRoute, subRoute } = parseCurrentRoute()
        if (mainRoute === 'admin-dashboard' && !subRoute) {
          navigateToSubRoute('admin-dashboard', 'overview', { replace: true, scrollToTop: false })
          // Load data for overview tab
          loadDataForTab('overview')
        } else if (subRoute) {
          // Load data for the current tab
          loadDataForTab(subRoute)
        }
      } catch (error) {
        console.error('Error initializing dashboard:', error)
        setError('Failed to load dashboard. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    initializeDashboard()
  }, [parseCurrentRoute, navigateToSubRoute, loadDataForTab])

  // Load data when tab changes via browser navigation
  useEffect(() => {
    const { mainRoute, subRoute } = parseCurrentRoute()
    if (mainRoute === 'admin-dashboard') {
      const newTab = subRoute || 'overview'
      if (newTab !== activeTab) {
        setActiveTab(newTab)
        loadDataForTab(newTab)
      }
    }
  }, [parseCurrentRoute, activeTab, loadDataForTab])

  // Close mobile menu when screen size changes to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) { // lg breakpoint
        setIsMobileMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Mock analytics data - Demo Business Metrics
  const analytics = {
    totalCustomers: 156,
    monthlyRevenue: 12450,
    appointmentsToday: 8,
    pendingOrders: 23,
    monthlyGrowth: 15.3,
    averageBookingValue: 125,
    customerRetentionRate: 89,
    monthlyAppointments: 98,
    topService: 'Micro Locs Installation',
    busyDay: 'Saturday'
  }

  // Initialize appointments with mock data
  setAppointments([
    {
      id: 1,
      customer: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      service: 'Micro Locs Installation',
      time: '10:00 AM',
      endTime: '4:00 PM',
      status: 'confirmed',
      duration: '6 hours',
      price: 300,
      notes: 'First-time client, discussed maintenance schedule',
      isNewClient: true
    },
    {
      id: 2,
      customer: 'Maria Garcia',
      email: '<EMAIL>',
      phone: '(*************',
      service: 'Loc Maintenance',
      time: '2:00 PM',
      endTime: '4:00 PM',
      status: 'in-progress',
      duration: '2 hours',
      price: 80,
      notes: 'Regular maintenance, 3-month follow-up',
      isNewClient: false
    },
    {
      id: 3,
      customer: 'Ashley Brown',
      email: '<EMAIL>',
      phone: '(*************',
      service: 'Consultation',
      time: '4:00 PM',
      endTime: '5:00 PM',
      status: 'confirmed',
      duration: '1 hour',
      price: 50,
      notes: 'Interested in transitioning to locs',
      isNewClient: true
    },
    {
      id: 4,
      customer: 'Jennifer Lee',
      email: '<EMAIL>',
      phone: '(*************',
      service: 'Loc Styling & Updo',
      time: '5:30 PM',
      endTime: '7:00 PM',
      status: 'confirmed',
      duration: '1.5 hours',
      price: 60,
      notes: 'Wedding styling for tomorrow',
      isNewClient: false
    }
  ])

  // Mock recent orders
  const recentOrders = [
    {
      id: 'ORD-001',
      customer: 'Jennifer Lee',
      items: 2,
      total: 43.98,
      status: 'pending',
      date: '2024-02-12'
    },
    {
      id: 'ORD-002',
      customer: 'Lisa Wilson',
      items: 1,
      total: 28.99,
      status: 'shipped',
      date: '2024-02-11'
    }
  ]

  // Mock customers data
  const recentCustomers = [
    {
      id: 1,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      joinDate: '2024-02-10',
      totalSpent: 430,
      appointments: 3
    },
    {
      id: 2,
      name: 'Maria Garcia',
      email: '<EMAIL>',
      joinDate: '2024-02-08',
      totalSpent: 160,
      appointments: 2
    }
  ]

  // Products data is initialized in the state above

  // Orders data is initialized in the state above

  // Helper functions for notifications
  const showToast = (message, type = 'success') => {
    setToast({ message, type })
    setTimeout(() => setToast(null), 3000)
  }

  const showConfirmDialog = (title, message, onConfirm) => {
    setConfirmDialog({ title, message, onConfirm })
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'bg-blue-100 text-blue-800'
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800'
      case 'completed':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'pending':
        return 'bg-orange-100 text-orange-800'
      case 'shipped':
        return 'bg-blue-100 text-blue-800'
      case 'delivered':
        return 'bg-yellow-100 text-yellow-800'
      case 'active':
        return 'bg-yellow-100 text-yellow-800'
      case 'low-stock':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: FiBarChart },
    { id: 'appointments', name: 'Appointments', icon: FiCalendar },
    { id: 'customers', name: 'Customers', icon: FiUsers },
    { id: 'orders', name: 'Orders', icon: FiShoppingBag },
    { id: 'products', name: 'Products', icon: FiPackage },
    { id: 'branding', name: 'Branding', icon: FiEdit },
    { id: 'settings', name: 'Settings', icon: FiSettings }
  ]

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="relative rounded-2xl p-8 text-white overflow-hidden transform hover:scale-[1.01] transition-all duration-300 shadow-xl border border-white/20"
           style={{ background: `linear-gradient(135deg, ${branding.colors.secondary}, ${branding.colors.accent})` }}>
        <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-2">Welcome back, {adminData.name}!</h2>
          <p className="text-white/80 text-lg">Here's your business overview for today</p>
        </div>
      </div>

      {/* Loading State for Dashboard Stats */}
      {sectionLoading.dashboard ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 animate-pulse">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-24"></div>
                  <div className="h-8 bg-gray-300 rounded w-16"></div>
                </div>
                <div className="w-12 h-12 bg-gray-300 rounded-xl"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        /* Analytics Cards */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 font-medium">Total Customers</p>
                <p className="text-3xl font-bold text-gray-900">
                  {dashboardStats?.totalCustomers || 0}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  +{dashboardStats?.newCustomersThisMonth || 0} this month
                </p>
              </div>
              <div className="p-3 rounded-xl shadow-lg"
                   style={{ background: `linear-gradient(135deg, ${branding.colors.primary}20, ${branding.colors.secondary}20)` }}>
                <FiUsers className="w-6 h-6" style={{ color: branding.colors.secondary }} />
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 font-medium">Monthly Revenue</p>
                <p className="text-3xl font-bold text-gray-900">
                  ${dashboardStats?.monthlyRevenue?.toLocaleString() || '0'}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  +{dashboardStats?.revenueGrowth || 0}% vs last month
                </p>
              </div>
              <div className="p-3 rounded-xl shadow-lg"
                   style={{ background: `linear-gradient(135deg, ${branding.colors.accent}20, #f3d01620)` }}>
                <FiDollarSign className="w-6 h-6" style={{ color: '#f3d016' }} />
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 font-medium">Today's Appointments</p>
                <p className="text-3xl font-bold text-gray-900">
                  {dashboardStats?.appointmentsToday || 0}
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  {dashboardStats?.upcomingAppointments || 0} upcoming
                </p>
              </div>
              <div className="p-3 rounded-xl shadow-lg"
                   style={{ background: `linear-gradient(135deg, #8b5cf620, #a855f720)` }}>
                <FiCalendar className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 font-medium">Pending Orders</p>
                <p className="text-3xl font-bold text-gray-900">
                  {dashboardStats?.pendingOrders || 0}
                </p>
                <p className="text-xs text-orange-600 mt-1">
                  {dashboardStats?.totalOrders || 0} total orders
                </p>
              </div>
              <div className="p-3 rounded-xl shadow-lg"
                   style={{ background: `linear-gradient(135deg, #ea580c20, #f97316 20)` }}>
                <FiShoppingBag className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Today's Appointments */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-bold text-gray-900">Today's Appointments</h3>
          <button
            onClick={() => handleTabChange('appointments')}
            className="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 transform hover:scale-105 cursor-pointer"
            style={{
              backgroundColor: `${branding.colors.secondary}15`,
              color: branding.colors.secondary,
              border: `1px solid ${branding.colors.secondary}30`
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = branding.colors.secondary
              e.target.style.color = 'white'
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = `${branding.colors.secondary}15`
              e.target.style.color = branding.colors.secondary
            }}
          >
            View All
          </button>
        </div>

        {sectionLoading.appointments ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-100 rounded-xl animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-32"></div>
                    <div className="h-3 bg-gray-300 rounded w-24"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-16"></div>
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        ) : appointments.length > 0 ? (
          <div className="space-y-4">
            {appointments.slice(0, 3).map((appointment, index) => (
              <div key={appointment._id || index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200 transform hover:scale-[1.01]">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center shadow-md"
                       style={{ background: `linear-gradient(135deg, ${branding.colors.secondary}20, ${branding.colors.accent}20)` }}>
                    <FiUserCheck className="w-6 h-6" style={{ color: branding.colors.secondary }} />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">{appointment.customerName || appointment.name || 'Unknown Customer'}</p>
                    <p className="text-sm text-gray-600">{appointment.service || appointment.serviceType || 'Service'}</p>
                    <p className="text-xs text-gray-500">{appointment.email}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">
                    {appointment.time || new Date(appointment.date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </p>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status || 'pending')}`}>
                    {appointment.status || 'Pending'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No appointments scheduled for today</p>
          </div>
        )}
      </div>

      {/* Recent Orders */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-bold text-gray-900">Recent Orders</h3>
          <button
            onClick={() => handleTabChange('orders')}
            className="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 transform hover:scale-105 cursor-pointer"
            style={{
              backgroundColor: `${branding.colors.secondary}15`,
              color: branding.colors.secondary,
              border: `1px solid ${branding.colors.secondary}30`
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = branding.colors.secondary
              e.target.style.color = 'white'
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = `${branding.colors.secondary}15`
              e.target.style.color = branding.colors.secondary
            }}
          >
            View All
          </button>
        </div>

        {sectionLoading.orders ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-100 rounded-xl animate-pulse">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-32"></div>
                  <div className="h-3 bg-gray-300 rounded w-40"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-16"></div>
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        ) : orders.length > 0 ? (
          <div className="space-y-4">
            {orders.slice(0, 3).map((order, index) => (
              <div key={order._id || index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200 transform hover:scale-[1.01]">
                <div>
                  <p className="font-semibold text-gray-900">Order #{order.orderNumber || order._id?.slice(-6) || 'N/A'}</p>
                  <p className="text-sm text-gray-600">
                    {order.customerName || order.customer?.name || 'Unknown Customer'} • {order.items?.length || 0} items
                  </p>
                  <p className="text-xs text-gray-500">
                    {order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'Date unknown'}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">${order.total?.toFixed(2) || '0.00'}</p>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status || 'pending')}`}>
                    {order.status || 'Pending'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiShoppingBag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No recent orders found</p>
          </div>
        )}
      </div>
    </div>
  )

  const renderAppointments = () => (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Appointments Management</h2>
          <p className="text-gray-600 mt-1">Manage all customer appointments and bookings</p>
        </div>
        <button
          onClick={() => {
            setEditingItem(null)
            setModalType('add')
            setShowAddModal(true)
          }}
          className="px-6 py-3 rounded-xl text-white font-medium transition-all duration-200 flex items-center shadow-lg transform hover:scale-105 cursor-pointer"
          style={{ background: `linear-gradient(135deg, ${branding.colors.secondary}, ${branding.colors.accent})` }}
        >
          <FiPlus className="w-5 h-5 mr-2" />
          Add Appointment
        </button>
      </div>

      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        <div className="p-6 border-b border-gray-200/50">
          <div className="flex flex-col sm:flex-row gap-4">
            <input
              type="text"
              placeholder="Search appointments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 px-4 py-3 border border-gray-300 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50"
              style={{
                focusRingColor: branding.colors.secondary,
                focusBorderColor: branding.colors.secondary
              }}
            />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-opacity-50"
              style={{
                focusRingColor: branding.colors.secondary,
                focusBorderColor: branding.colors.secondary
              }}
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="in-progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        {sectionLoading.appointments ? (
          <div className="p-6">
            <div className="space-y-4">
              {[...Array(5)].map((_, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-100 rounded-xl animate-pulse">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-300 rounded w-32"></div>
                      <div className="h-3 bg-gray-300 rounded w-24"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-16"></div>
                    <div className="h-6 bg-gray-300 rounded w-20"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : appointments.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Customer</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Contact</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Service</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Date & Time</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {appointments
                  .filter(appointment => {
                    const matchesSearch = searchTerm === '' ||
                      (appointment.customerName || appointment.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                      (appointment.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                      (appointment.service || appointment.serviceType || '').toLowerCase().includes(searchTerm.toLowerCase())

                    const matchesStatus = statusFilter === 'all' ||
                      (appointment.status || 'pending').toLowerCase() === statusFilter.toLowerCase()

                    return matchesSearch && matchesStatus
                  })
                  .map((appointment, index) => (
                    <tr key={appointment._id || index} className="hover:bg-gray-50 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-full flex items-center justify-center mr-3"
                               style={{ background: `linear-gradient(135deg, ${branding.colors.secondary}20, ${branding.colors.accent}20)` }}>
                            <FiUser className="w-5 h-5" style={{ color: branding.colors.secondary }} />
                          </div>
                          <div>
                            <div className="font-semibold text-gray-900">
                              {appointment.customerName || appointment.name || 'Unknown Customer'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{appointment.email}</div>
                        <div className="text-sm text-gray-500">{appointment.phone}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {appointment.service || appointment.serviceType || 'Service'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {appointment.date ? new Date(appointment.date).toLocaleDateString() : 'Date not set'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {appointment.time || 'Time not set'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status || 'pending')}`}>
                          {appointment.status || 'Pending'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setViewingItem(appointment)
                              setModalType('view')
                            }}
                            className="p-2 rounded-lg transition-colors duration-200 cursor-pointer"
                            style={{ backgroundColor: `${branding.colors.accent}15`, color: branding.colors.accent }}
                            title="View Appointment"
                          >
                            <FiEye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => {
                              setEditingItem(appointment)
                              setModalType('edit')
                              setShowAddModal(true)
                            }}
                            className="p-2 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors duration-200 cursor-pointer"
                            title="Edit Appointment"
                          >
                            <FiEdit3 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => {
                              showConfirmDialog(
                                'Delete Appointment',
                                `Are you sure you want to delete the appointment for ${appointment.customerName || appointment.name}? This action cannot be undone.`,
                                async () => {
                                  try {
                                    const response = await adminService.deleteAppointment(appointment._id)
                                    if (response.success) {
                                      setAppointments(prev => prev.filter(a => a._id !== appointment._id))
                                      showToast('Appointment deleted successfully', 'success')
                                    } else {
                                      showToast('Failed to delete appointment', 'error')
                                    }
                                  } catch (error) {
                                    console.error('Error deleting appointment:', error)
                                    showToast('Failed to delete appointment', 'error')
                                  }
                                }
                              )
                            }}
                            className="p-2 rounded-lg bg-red-100 text-red-600 hover:bg-red-200 transition-colors duration-200 cursor-pointer"
                            title="Delete Appointment"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <FiCalendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No appointments found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your search or filter criteria'
                : 'Get started by adding your first appointment'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <button
                onClick={() => {
                  setEditingItem(null)
                  setModalType('add')
                  setShowAddModal(true)
                }}
                className="px-6 py-3 rounded-xl text-white font-medium transition-all duration-200 transform hover:scale-105 cursor-pointer"
                style={{ background: `linear-gradient(135deg, ${branding.colors.secondary}, ${branding.colors.accent})` }}
              >
                <FiPlus className="w-5 h-5 mr-2 inline" />
                Add First Appointment
              </button>
            )}
          </div>
        )}
      </div>

      {/* Appointment Details Modal */}
      {editingItem && modalType && activeTab === 'appointments' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {modalType === 'view' ? 'Appointment Details' : 'Edit Appointment'}
              </h3>
              <button
                onClick={() => {
                  setEditingItem(null)
                  setModalType('')
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <FiX className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Customer Information</h4>
                  <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                    <p><strong>Name:</strong> {editingItem.customer}</p>
                    <p><strong>Email:</strong> {editingItem.email}</p>
                    <p><strong>Phone:</strong> {editingItem.phone}</p>
                    <p><strong>Client Type:</strong> {editingItem.isNewClient ? 'New Client' : 'Returning Client'}</p>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Appointment Details</h4>
                  <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                    <p><strong>Service:</strong> {editingItem.service}</p>
                    <p><strong>Date:</strong> Today</p>
                    <p><strong>Time:</strong> {editingItem.time} - {editingItem.endTime}</p>
                    <p><strong>Duration:</strong> {editingItem.duration}</p>
                    <p><strong>Price:</strong> ${editingItem.price}</p>
                    <p><strong>Status:</strong>
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(editingItem.status)}`}>
                        {editingItem.status}
                      </span>
                    </p>
                  </div>
                </div>

                {editingItem.notes && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Notes</h4>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <p>{editingItem.notes}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setEditingItem(null)
                  setModalType('')
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                {modalType === 'view' ? 'Close' : 'Cancel'}
              </button>
              {modalType === 'edit' && (
                <button
                  onClick={() => {
                    showToast('Appointment updated successfully!', 'success')
                    setEditingItem(null)
                    setModalType('')
                  }}
                  className="px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700"
                >
                  Update Appointment
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )

  const renderCustomers = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Customer Management</h2>
        <button
          onClick={() => {
            setEditingItem(null)
            setModalType('add')
            setShowAddModal(true)
          }}
          className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700 transition-colors duration-200 flex items-center"
        >
          <FiPlus className="w-4 h-4 mr-2" />
          Add Customer
        </button>
      </div>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <input
            type="text"
            placeholder="Search customers..."
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
          />
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Join Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Appointments</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Spent</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentCustomers.map((customer) => (
                <tr key={customer.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="font-medium text-gray-900">{customer.name}</div>
                      <div className="text-sm text-gray-500">{customer.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {customer.joinDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {customer.appointments}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${customer.totalSpent}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setEditingItem(customer)
                          setModalType('view')
                        }}
                        className="text-amber-600 hover:text-amber-700"
                        title="View Customer"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          setEditingItem(customer)
                          setModalType('edit')
                        }}
                        className="text-blue-600 hover:text-blue-700"
                        title="Edit Customer"
                      >
                        <FiEdit3 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Customer Details Modal */}
      {editingItem && modalType && activeTab === 'customers' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {modalType === 'view' ? 'Customer Details' : 'Edit Customer'}
              </h3>
              <button
                onClick={() => {
                  setEditingItem(null)
                  setModalType('')
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <FiX className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Customer Information</h4>
                <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                  <p><strong>Name:</strong> {editingItem.name}</p>
                  <p><strong>Email:</strong> {editingItem.email}</p>
                  <p><strong>Join Date:</strong> {editingItem.joinDate}</p>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Statistics</h4>
                <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                  <p><strong>Total Appointments:</strong> {editingItem.appointments}</p>
                  <p><strong>Total Spent:</strong> ${editingItem.totalSpent}</p>
                  <p><strong>Average per Visit:</strong> ${(editingItem.totalSpent / editingItem.appointments).toFixed(2)}</p>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setEditingItem(null)
                  setModalType('')
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                {modalType === 'view' ? 'Close' : 'Cancel'}
              </button>
              {modalType === 'edit' && (
                <button
                  onClick={() => {
                    showToast('Customer updated successfully!', 'success')
                    setEditingItem(null)
                    setModalType('')
                  }}
                  className="px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700"
                >
                  Update Customer
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )

  const renderOrders = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Order Management</h2>
        <div className="flex space-x-3">
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center cursor-pointer">
            <FiPlus className="w-4 h-4 mr-2" />
            Export Orders
          </button>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <input
              type="text"
              placeholder="Search orders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {orders
                .filter(order =>
                  (statusFilter === 'all' || order.status === statusFilter) &&
                  (order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                   order.customer.toLowerCase().includes(searchTerm.toLowerCase()))
                )
                .map((order) => (
                <tr key={order.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-900">{order.id}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="font-medium text-gray-900">{order.customer}</div>
                      <div className="text-sm text-gray-500">{order.email}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {order.items.map((item, index) => (
                        <div key={index}>
                          {item.quantity}x {item.name}
                        </div>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${order.total}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <select
                      value={order.status}
                      onChange={(e) => {
                        const newOrders = orders.map(o =>
                          o.id === order.id ? { ...o, status: e.target.value } : o
                        )
                        setOrders(newOrders)
                        showToast(`Order ${order.id} status updated to ${e.target.value}`, 'success')
                      }}
                      className={`px-2 py-1 rounded-full text-xs font-medium border-0 ${getStatusColor(order.status)}`}
                    >
                      <option value="pending">Pending</option>
                      <option value="shipped">Shipped</option>
                      <option value="delivered">Delivered</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {order.date}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setEditingItem(order)
                          setModalType('view')
                        }}
                        className="transition-colors duration-200"
                        style={{ color: '#f3d016' }}
                        onMouseEnter={(e) => e.target.style.color = '#d4b014'}
                        onMouseLeave={(e) => e.target.style.color = '#f3d016'}
                        title="View Details"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          setEditingItem(order)
                          setModalType('edit')
                        }}
                        className="text-blue-600 hover:text-blue-700"
                        title="Edit Order"
                      >
                        <FiEdit3 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          showConfirmDialog(
                            'Delete Order',
                            `Are you sure you want to delete order ${order.id}? This action cannot be undone.`,
                            () => {
                              setOrders(orders.filter(o => o.id !== order.id))
                              showToast('Order deleted successfully', 'success')
                            }
                          )
                        }}
                        className="text-red-600 hover:text-red-700"
                        title="Delete Order"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Order Details Modal */}
      {editingItem && modalType && activeTab === 'orders' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {modalType === 'view' ? 'Order Details' : 'Edit Order'} - {editingItem.id}
              </h3>
              <button
                onClick={() => {
                  setEditingItem(null)
                  setModalType('')
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <FiX className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Customer Information</h4>
                  <p><strong>Name:</strong> {editingItem.customer}</p>
                  <p><strong>Email:</strong> {editingItem.email}</p>
                  <p><strong>Phone:</strong> {editingItem.phone}</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Order Information</h4>
                  <p><strong>Order ID:</strong> {editingItem.id}</p>
                  <p><strong>Date:</strong> {editingItem.date}</p>
                  <p><strong>Status:</strong>
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(editingItem.status)}`}>
                      {editingItem.status}
                    </span>
                  </p>
                  {editingItem.trackingNumber && (
                    <p><strong>Tracking:</strong> {editingItem.trackingNumber}</p>
                  )}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Items Ordered</h4>
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Item</th>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Qty</th>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Price</th>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {editingItem.items.map((item, index) => (
                        <tr key={index} className="border-t">
                          <td className="px-4 py-2">{item.name}</td>
                          <td className="px-4 py-2">{item.quantity}</td>
                          <td className="px-4 py-2">${item.price}</td>
                          <td className="px-4 py-2">${(item.quantity * item.price).toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <div className="text-right mt-2">
                  <strong>Total: ${editingItem.total}</strong>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Shipping Address</h4>
                <p>{editingItem.shippingAddress}</p>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Payment Method</h4>
                <p>{editingItem.paymentMethod}</p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setEditingItem(null)
                  setModalType('')
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                {modalType === 'view' ? 'Close' : 'Cancel'}
              </button>
              {modalType === 'edit' && (
                <button
                  onClick={() => {
                    showToast('Order updated successfully!', 'success')
                    setEditingItem(null)
                    setModalType('')
                  }}
                  className="px-4 py-2 text-white rounded-lg transition-colors duration-200"
                  style={{ backgroundColor: '#f3d016' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = '#d4b014'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = '#f3d016'}
                >
                  Update Order
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )

  const renderProducts = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Product Management</h2>
        <button
          onClick={() => {
            setEditingItem(null)
            setModalType('add')
            setShowAddModal(true)
          }}
          className="text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center"
          style={{ backgroundColor: '#f3d016' }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#d4b014'}
          onMouseLeave={(e) => e.target.style.backgroundColor = '#f3d016'}
        >
          <FiPlus className="w-4 h-4 mr-2" />
          Add Product
        </button>
      </div>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = '#f3d016'
                e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = '#f3d016'
                e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="low-stock">Low Stock</option>
              <option value="out-of-stock">Out of Stock</option>
            </select>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sold</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products
                .filter(product =>
                  (statusFilter === 'all' || product.status === statusFilter) &&
                  product.name.toLowerCase().includes(searchTerm.toLowerCase())
                )
                .map((product) => (
                <tr key={product.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="font-medium text-gray-900">{product.name}</div>
                      <div className="text-sm text-gray-500">{product.description}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                    {product.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${product.price}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <input
                      type="number"
                      value={product.stock}
                      onChange={(e) => {
                        const newStock = parseInt(e.target.value) || 0
                        const newProducts = products.map(p =>
                          p.id === product.id ? { ...p, stock: newStock } : p
                        )
                        setProducts(newProducts)
                        showToast(`Stock updated for ${product.name}`, 'success')
                      }}
                      className="w-16 px-2 py-1 border border-gray-300 rounded text-center"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.sold}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
                      {product.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setEditingItem(product)
                          setModalType('view')
                        }}
                        className="transition-colors duration-200"
                        style={{ color: '#f3d016' }}
                        onMouseEnter={(e) => e.target.style.color = '#d4b014'}
                        onMouseLeave={(e) => e.target.style.color = '#f3d016'}
                        title="View Product"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          setEditingItem(product)
                          setModalType('edit')
                        }}
                        className="text-blue-600 hover:text-blue-700"
                        title="Edit Product"
                      >
                        <FiEdit3 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          showConfirmDialog(
                            'Delete Product',
                            `Are you sure you want to delete "${product.name}"? This action cannot be undone.`,
                            () => {
                              setProducts(products.filter(p => p.id !== product.id))
                              showToast('Product deleted successfully', 'success')
                            }
                          )
                        }}
                        className="text-red-600 hover:text-red-700"
                        title="Delete Product"
                      >
                        <FiTrash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Product Modal - Add/Edit/View */}
      {(showAddModal || (editingItem && modalType)) && (
        <ProductModal
          isOpen={showAddModal || !!editingItem}
          onClose={() => {
            setShowAddModal(false)
            setEditingItem(null)
            setModalType('')
          }}
          product={editingItem}
          mode={modalType}
          onSave={(productData) => {
            if (modalType === 'add') {
              const newProduct = {
                id: products.length + 1,
                ...productData,
                sold: 0,
                status: productData.stock > 10 ? 'active' : 'low-stock'
              }
              setProducts([...products, newProduct])
              showToast('Product added successfully!', 'success')
            } else if (modalType === 'edit') {
              const updatedProducts = products.map(p =>
                p.id === editingItem.id ? { ...p, ...productData } : p
              )
              setProducts(updatedProducts)
              showToast('Product updated successfully!', 'success')
            }
            setShowAddModal(false)
            setEditingItem(null)
            setModalType('')
          }}
        />
      )}
    </div>
  )

  const renderSettings = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Business Settings</h2>

      {/* Business Information */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Business Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Business Name</label>
            <input
              type="text"
              defaultValue="Goldie Locs by Tina"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = '#f3d016'
                e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Owner Name</label>
            <input
              type="text"
              defaultValue={adminData.name}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <input
              type="email"
              defaultValue={adminData.email}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
            <input
              type="tel"
              defaultValue="(*************"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
            <textarea
              defaultValue="123 Hair Studio Lane, Beauty City, BC 12345"
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              rows="3"
            />
          </div>
        </div>
      </div>

      {/* Business Hours */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Business Hours</h3>
        <div className="space-y-4">
          {[
            { day: 'Monday', open: '9:00 AM', close: '6:00 PM', closed: false },
            { day: 'Tuesday', open: '9:00 AM', close: '6:00 PM', closed: false },
            { day: 'Wednesday', open: '9:00 AM', close: '6:00 PM', closed: false },
            { day: 'Thursday', open: '9:00 AM', close: '8:00 PM', closed: false },
            { day: 'Friday', open: '9:00 AM', close: '8:00 PM', closed: false },
            { day: 'Saturday', open: '8:00 AM', close: '5:00 PM', closed: false },
            { day: 'Sunday', open: '', close: '', closed: true }
          ].map((schedule, index) => (
            <div key={index} className="flex items-center space-x-4">
              <div className="w-24 font-medium">{schedule.day}</div>
              <input
                type="checkbox"
                checked={!schedule.closed}
                className="rounded border-gray-300 focus:ring-2"
              style={{ accentColor: '#f3d016' }}
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
              }}
              onBlur={(e) => {
                e.target.style.boxShadow = 'none'
              }}
              />
              <span className="text-sm text-gray-600">Open</span>
              {!schedule.closed && (
                <>
                  <input
                    type="time"
                    defaultValue={schedule.open.replace(' AM', '').replace(' PM', '')}
                    className="px-3 py-1 border border-gray-300 rounded"
                  />
                  <span className="text-gray-500">to</span>
                  <input
                    type="time"
                    defaultValue={schedule.close.replace(' AM', '').replace(' PM', '')}
                    className="px-3 py-1 border border-gray-300 rounded"
                  />
                </>
              )}
              {schedule.closed && (
                <span className="text-gray-500 italic">Closed</span>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Service Settings */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Service Settings</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Online Booking</h4>
              <p className="text-sm text-gray-600">Allow customers to book appointments online</p>
            </div>
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Email Notifications</h4>
              <p className="text-sm text-gray-600">Send email confirmations and reminders</p>
            </div>
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">SMS Reminders</h4>
              <p className="text-sm text-gray-600">Send text message reminders to customers</p>
            </div>
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Booking Buffer Time (minutes)</label>
            <input
              type="number"
              defaultValue="15"
              className="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-600 mt-1">Time between appointments for cleanup and preparation</p>
          </div>
        </div>
      </div>

      {/* Payment Settings */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Payment Settings</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Accepted Payment Methods</label>
            <div className="space-y-2">
              {['Cash', 'Credit Card', 'Debit Card', 'PayPal', 'Venmo', 'CashApp'].map((method) => (
                <label key={method} className="flex items-center">
                  <input
                    type="checkbox"
                    defaultChecked={['Cash', 'Credit Card', 'PayPal'].includes(method)}
                    className="rounded border-gray-300 text-amber-600 focus:ring-amber-500 mr-2"
                  />
                  {method}
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Deposit Required (%)</label>
            <input
              type="number"
              defaultValue="25"
              min="0"
              max="100"
              className="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-600 mt-1">Percentage of service cost required as deposit</p>
          </div>
        </div>
      </div>

      {/* Notification Settings */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Notification Preferences</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">New Appointment Alerts</h4>
              <p className="text-sm text-gray-600">Get notified when customers book appointments</p>
            </div>
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Order Notifications</h4>
              <p className="text-sm text-gray-600">Get notified about new product orders</p>
            </div>
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Low Stock Alerts</h4>
              <p className="text-sm text-gray-600">Get notified when products are running low</p>
            </div>
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-amber-600 focus:ring-amber-500"
            />
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={() => showToast('All settings saved successfully!', 'success')}
          className="text-white px-6 py-3 rounded-lg transition-colors duration-200 cursor-pointer"
          style={{ backgroundColor: branding.colors.secondary }}
          onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
          onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
        >
          Save All Settings
        </button>
      </div>
    </div>
  )

  // Product Modal Component
  const ProductModal = ({ isOpen, onClose, product, mode, onSave }) => {
    const [formData, setFormData] = useState({
      name: product?.name || '',
      description: product?.description || '',
      price: product?.price || '',
      stock: product?.stock || '',
      category: product?.category || ''
    })

    const handleInputChange = (e) => {
      const { name, value } = e.target
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }

    const handleSubmit = (e) => {
      e.preventDefault()
      if (mode !== 'view') {
        onSave({
          ...formData,
          price: parseFloat(formData.price) || 0,
          stock: parseInt(formData.stock) || 0
        })
      }
    }

    if (!isOpen) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">
              {mode === 'view' ? 'Product Details' :
               mode === 'edit' ? 'Edit Product' : 'Add New Product'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <FiX className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Product Name</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                disabled={mode === 'view'}
                className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent ${
                  mode === 'view' ? 'bg-gray-50' : ''
                }`}
                placeholder="Enter product name"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                disabled={mode === 'view'}
                className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent ${
                  mode === 'view' ? 'bg-gray-50' : ''
                }`}
                rows="3"
                placeholder="Enter product description"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Price ($)</label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  disabled={mode === 'view'}
                  step="0.01"
                  min="0"
                  className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent ${
                    mode === 'view' ? 'bg-gray-50' : ''
                  }`}
                  placeholder="0.00"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Stock Quantity</label>
                <input
                  type="number"
                  name="stock"
                  value={formData.stock}
                  onChange={handleInputChange}
                  disabled={mode === 'view'}
                  min="0"
                  className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent ${
                    mode === 'view' ? 'bg-gray-50' : ''
                  }`}
                  placeholder="0"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <select
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                disabled={mode === 'view'}
                className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent ${
                  mode === 'view' ? 'bg-gray-50' : ''
                }`}
                required
              >
                <option value="">Select category</option>
                <option value="shampoo">Shampoo</option>
                <option value="conditioner">Conditioner</option>
                <option value="oil">Oil & Serum</option>
                <option value="styling">Styling</option>
                <option value="tools">Tools</option>
              </select>
            </div>

            {mode === 'view' && product && (
              <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Total Sold</label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg">
                    {product.sold}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <div className={`px-3 py-2 rounded-lg text-center ${getStatusColor(product.status)}`}>
                    {product.status}
                  </div>
                </div>
              </div>
            )}
          </form>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              {mode === 'view' ? 'Close' : 'Cancel'}
            </button>
            {mode !== 'view' && (
              <button
                onClick={handleSubmit}
                className="px-4 py-2 text-white rounded-lg transition-colors duration-200"
                style={{ backgroundColor: '#f3d016' }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#d4b014'}
                onMouseLeave={(e) => e.target.style.backgroundColor = '#f3d016'}
              >
                {mode === 'edit' ? 'Update' : 'Add'} Product
              </button>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderBranding = () => {
    const handleContentChange = (section, field, value) => {
      setBrandingContent(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value
        }
      }))
    }

    const handleNestedContentChange = (section, subsection, field, value) => {
      setBrandingContent(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [subsection]: {
            ...prev[section][subsection],
            [field]: value
          }
        }
      }))
    }

    const handleSave = () => {
      // Data is automatically saved to localStorage via useEffect
      // This function provides user feedback
      showToast('Branding content saved successfully! Changes will persist after page refresh.', 'success')

      // Scroll to top for better UX
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }

    const sections = [
      { id: 'global', name: 'Global/Site-wide', icon: FiSettings },
      { id: 'home', name: 'Home Page', icon: FiHome },
      { id: 'services', name: 'Services Page', icon: FiPackage },
      { id: 'shop', name: 'Shop Page', icon: FiShoppingBag },
      { id: 'consultation', name: 'Consultation Page', icon: FiCalendar },
      { id: 'login', name: 'Login Page', icon: FiUsers },
      { id: 'signup', name: 'Sign Up Page', icon: FiUserCheck },
      { id: 'cart', name: 'Cart Page', icon: FiShoppingBag },
      { id: 'productDetail', name: 'Product Detail', icon: FiPackage },
      { id: 'userDashboard', name: 'User Dashboard', icon: FiBarChart },
      { id: 'footer', name: 'Footer', icon: FiEdit }
    ]

    const renderInputField = (label, value, onChange, type = 'text', placeholder = '', rows = null) => (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
        {rows ? (
          <textarea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            rows={rows}
            placeholder={placeholder}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
            onFocus={(e) => {
              e.target.style.outline = 'none'
              e.target.style.borderColor = '#f3d016'
              e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#d1d5db'
              e.target.style.boxShadow = 'none'
            }}
          />
        ) : (
          <input
            type={type}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
            onFocus={(e) => {
              e.target.style.outline = 'none'
              e.target.style.borderColor = '#f3d016'
              e.target.style.boxShadow = '0 0 0 2px rgba(243, 208, 22, 0.2)'
            }}
            onBlur={(e) => {
              e.target.style.borderColor = '#d1d5db'
              e.target.style.boxShadow = 'none'
            }}
          />
        )}
      </div>
    )

    return (
      <div className="space-y-6">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold">Branding & Content Management</h2>
            <p className="text-gray-600 mt-1">
              Customize all text content, images, and branding elements across your website.
              Changes are automatically saved and will persist after page refresh.
            </p>
          </div>
          <button
            onClick={handleSave}
            className="text-white px-6 py-3 rounded-lg transition-colors duration-200 flex items-center self-start lg:self-auto cursor-pointer"
            style={{ backgroundColor: branding.colors.secondary }}
            onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
            onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
          >
            <FiSave className="w-4 h-4 mr-2" />
            Save All Changes
          </button>
        </div>

        {/* Tips Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <FiEdit className="w-5 h-5 text-blue-600 mt-0.5" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">Branding Management Tips</h3>
              <div className="mt-1 text-sm text-blue-700">
                <p>Use the tabs below to edit content for different pages. All changes are automatically saved to your browser's local storage.</p>
                <ul className="mt-2 list-disc list-inside space-y-1">
                  <li><strong>Global/Site-wide:</strong> Logo, contact info, and social media links</li>
                  <li><strong>Home Page:</strong> Hero section, features, and call-to-action content</li>
                  <li><strong>Services:</strong> Service descriptions, pricing, and benefits</li>
                  <li><strong>Shop:</strong> Product page headers and newsletter content</li>
                  <li><strong>Consultation:</strong> Form labels and information text</li>
                  <li><strong>Login:</strong> Authentication page content and links</li>
                  <li><strong>Sign Up:</strong> Registration form labels and benefits section</li>
                  <li><strong>Cart:</strong> Shopping cart labels and checkout text</li>
                  <li><strong>Product Detail:</strong> Product page labels and review section</li>
                  <li><strong>User Dashboard:</strong> Customer dashboard content and navigation</li>
                  <li><strong>Footer:</strong> Footer description and section titles</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Section Navigation */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex flex-wrap gap-2">
            {sections.map((section) => {
              const Icon = section.icon
              return (
                <button
                  key={section.id}
                  onClick={() => {
                    setActiveBrandingSection(section.id)
                    // Scroll to top when switching sections for better UX
                    window.scrollTo({ top: 0, behavior: 'smooth' })
                  }}
                  className={`flex items-center px-4 py-2 rounded-lg transition-colors duration-200 ${
                    activeBrandingSection === section.id
                      ? 'bg-yellow-50'
                      : 'text-gray-700 hover:bg-yellow-50'
                  }`}
                  style={activeBrandingSection === section.id ? { color: '#f3d016' } : {}}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {section.name}
                </button>
              )
            })}
          </div>
        </div>

        {/* Dynamic Content Based on Active Section */}
        {activeBrandingSection === 'global' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Site-wide Content */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiType className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Site Information</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Site Name', brandingContent.global.siteName, (value) => handleContentChange('global', 'siteName', value))}
                {renderInputField('Tagline', brandingContent.global.tagline, (value) => handleContentChange('global', 'tagline', value))}
                {renderInputField('Logo URL', brandingContent.global.logo, (value) => handleContentChange('global', 'logo', value), 'url', 'https://example.com/logo.png')}

                {/* Logo Preview */}
                {brandingContent.global.logo && (
                  <div className="mt-3">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Logo Preview</label>
                    <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                      <img
                        src={brandingContent.global.logo}
                        alt="Logo preview"
                        className="h-12 object-contain"
                        onError={(e) => {
                          e.target.style.display = 'none'
                          e.target.nextSibling.style.display = 'block'
                        }}
                      />
                      <div className="text-sm text-red-600 hidden">
                        Failed to load image. Please check the URL.
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Contact Information</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Phone Number', brandingContent.global.phone, (value) => handleContentChange('global', 'phone', value), 'tel')}
                {renderInputField('Email Address', brandingContent.global.email, (value) => handleContentChange('global', 'email', value), 'email')}
                {renderInputField('Address', brandingContent.global.address, (value) => handleContentChange('global', 'address', value), 'text', '', 3)}
              </div>
            </div>

            {/* Social Media */}
            <div className="bg-white rounded-xl p-6 shadow-sm lg:col-span-2">
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Social Media</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {renderInputField('Instagram', brandingContent.global.instagram, (value) => handleContentChange('global', 'instagram', value), 'text', '@username')}
                {renderInputField('Facebook', brandingContent.global.facebook, (value) => handleContentChange('global', 'facebook', value), 'text', 'Page Name')}
                {renderInputField('Twitter', brandingContent.global.twitter, (value) => handleContentChange('global', 'twitter', value), 'text', '@username')}
                {renderInputField('YouTube', brandingContent.global.youtube, (value) => handleContentChange('global', 'youtube', value), 'text', 'Channel Name')}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'home' && (
          <div className="space-y-6">
            {/* Hero Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Hero Section</h3>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {renderInputField('Hero Title (Part 1)', brandingContent.home.heroTitle, (value) => handleContentChange('home', 'heroTitle', value))}
                  {renderInputField('Hero Title Accent (Golden)', brandingContent.home.heroTitleAccent, (value) => handleContentChange('home', 'heroTitleAccent', value))}
                  {renderInputField('Hero Title (Part 3)', brandingContent.home.heroTitleEnd, (value) => handleContentChange('home', 'heroTitleEnd', value))}
                  {renderInputField('Hero Subtitle', brandingContent.home.heroSubtitle, (value) => handleContentChange('home', 'heroSubtitle', value), 'text', '', 4)}
                </div>

                <div className="space-y-4">
                  {renderInputField('Primary Button Text', brandingContent.home.heroButtonPrimary, (value) => handleContentChange('home', 'heroButtonPrimary', value))}
                  {renderInputField('Secondary Button Text', brandingContent.home.heroButtonSecondary, (value) => handleContentChange('home', 'heroButtonSecondary', value))}
                  {renderInputField('Hero Image URL', brandingContent.home.heroImage, (value) => handleContentChange('home', 'heroImage', value), 'url')}

                  {brandingContent.home.heroImage && (
                    <div className="mt-3">
                      <img
                        src={brandingContent.home.heroImage}
                        alt="Hero preview"
                        className="w-full h-32 object-cover rounded-lg"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Features Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Features Section</h3>
              </div>

              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {renderInputField('Features Title', brandingContent.home.featuresTitle, (value) => handleContentChange('home', 'featuresTitle', value))}
                  {renderInputField('Features Subtitle', brandingContent.home.featuresSubtitle, (value) => handleContentChange('home', 'featuresSubtitle', value))}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Feature 1</h4>
                    {renderInputField('Title', brandingContent.home.feature1Title, (value) => handleContentChange('home', 'feature1Title', value))}
                    {renderInputField('Description', brandingContent.home.feature1Description, (value) => handleContentChange('home', 'feature1Description', value), 'text', '', 3)}
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Feature 2</h4>
                    {renderInputField('Title', brandingContent.home.feature2Title, (value) => handleContentChange('home', 'feature2Title', value))}
                    {renderInputField('Description', brandingContent.home.feature2Description, (value) => handleContentChange('home', 'feature2Description', value), 'text', '', 3)}
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Feature 3</h4>
                    {renderInputField('Title', brandingContent.home.feature3Title, (value) => handleContentChange('home', 'feature3Title', value))}
                    {renderInputField('Description', brandingContent.home.feature3Description, (value) => handleContentChange('home', 'feature3Description', value), 'text', '', 3)}
                  </div>
                </div>
              </div>
            </div>



            {/* CTA Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiCalendar className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Call-to-Action Section</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {renderInputField('CTA Title', brandingContent.home.ctaTitle, (value) => handleContentChange('home', 'ctaTitle', value))}
                  {renderInputField('CTA Subtitle', brandingContent.home.ctaSubtitle, (value) => handleContentChange('home', 'ctaSubtitle', value))}
                  {renderInputField('CTA Button Text', brandingContent.home.ctaButtonText, (value) => handleContentChange('home', 'ctaButtonText', value))}
                </div>

                <div className="space-y-4">
                  {renderInputField('CTA Background Image', brandingContent.home.ctaBackgroundImage, (value) => handleContentChange('home', 'ctaBackgroundImage', value), 'url')}

                  {brandingContent.home.ctaBackgroundImage && (
                    <div className="mt-3">
                      <img
                        src={brandingContent.home.ctaBackgroundImage}
                        alt="CTA background preview"
                        className="w-full h-32 object-cover rounded-lg"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'services' && (
          <div className="space-y-6">
            {/* Services Page Header */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Services Page Header</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Page Title', brandingContent.services.pageTitle, (value) => handleContentChange('services', 'pageTitle', value))}
                {renderInputField('Page Subtitle', brandingContent.services.pageSubtitle, (value) => handleContentChange('services', 'pageSubtitle', value))}
              </div>
            </div>

            {/* Service Categories */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Service Categories</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Locs Installation</h4>
                  {renderInputField('Title', brandingContent.services.locsInstallation.title, (value) => handleNestedContentChange('services', 'locsInstallation', 'title', value))}
                  {renderInputField('Description', brandingContent.services.locsInstallation.description, (value) => handleNestedContentChange('services', 'locsInstallation', 'description', value), 'text', '', 3)}
                  {renderInputField('Price', brandingContent.services.locsInstallation.price, (value) => handleNestedContentChange('services', 'locsInstallation', 'price', value))}
                  {renderInputField('Duration', brandingContent.services.locsInstallation.duration, (value) => handleNestedContentChange('services', 'locsInstallation', 'duration', value))}
                  {renderInputField('Image URL', brandingContent.services.locsInstallation.image, (value) => handleNestedContentChange('services', 'locsInstallation', 'image', value), 'url')}
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Locs Maintenance</h4>
                  {renderInputField('Title', brandingContent.services.locsMaintenance.title, (value) => handleNestedContentChange('services', 'locsMaintenance', 'title', value))}
                  {renderInputField('Description', brandingContent.services.locsMaintenance.description, (value) => handleNestedContentChange('services', 'locsMaintenance', 'description', value), 'text', '', 3)}
                  {renderInputField('Price', brandingContent.services.locsMaintenance.price, (value) => handleNestedContentChange('services', 'locsMaintenance', 'price', value))}
                  {renderInputField('Duration', brandingContent.services.locsMaintenance.duration, (value) => handleNestedContentChange('services', 'locsMaintenance', 'duration', value))}
                  {renderInputField('Image URL', brandingContent.services.locsMaintenance.image, (value) => handleNestedContentChange('services', 'locsMaintenance', 'image', value), 'url')}
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Natural Hair Care</h4>
                  {renderInputField('Title', brandingContent.services.naturalHairCare.title, (value) => handleNestedContentChange('services', 'naturalHairCare', 'title', value))}
                  {renderInputField('Description', brandingContent.services.naturalHairCare.description, (value) => handleNestedContentChange('services', 'naturalHairCare', 'description', value), 'text', '', 3)}
                  {renderInputField('Price', brandingContent.services.naturalHairCare.price, (value) => handleNestedContentChange('services', 'naturalHairCare', 'price', value))}
                  {renderInputField('Duration', brandingContent.services.naturalHairCare.duration, (value) => handleNestedContentChange('services', 'naturalHairCare', 'duration', value))}
                  {renderInputField('Image URL', brandingContent.services.naturalHairCare.image, (value) => handleNestedContentChange('services', 'naturalHairCare', 'image', value), 'url')}
                </div>
              </div>
            </div>

            {/* Why Choose Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Why Choose Our Services</h3>
              </div>

              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {renderInputField('Section Title', brandingContent.services.whyChooseTitle, (value) => handleContentChange('services', 'whyChooseTitle', value))}
                  {renderInputField('Section Subtitle', brandingContent.services.whyChooseSubtitle, (value) => handleContentChange('services', 'whyChooseSubtitle', value))}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {renderInputField('Benefit 1', brandingContent.services.benefit1, (value) => handleContentChange('services', 'benefit1', value))}
                  {renderInputField('Benefit 2', brandingContent.services.benefit2, (value) => handleContentChange('services', 'benefit2', value))}
                  {renderInputField('Benefit 3', brandingContent.services.benefit3, (value) => handleContentChange('services', 'benefit3', value))}
                  {renderInputField('Benefit 4', brandingContent.services.benefit4, (value) => handleContentChange('services', 'benefit4', value))}
                </div>
              </div>
            </div>

            {/* Services CTA */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiCalendar className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Services Call-to-Action</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {renderInputField('CTA Title', brandingContent.services.ctaTitle, (value) => handleContentChange('services', 'ctaTitle', value))}
                {renderInputField('CTA Subtitle', brandingContent.services.ctaSubtitle, (value) => handleContentChange('services', 'ctaSubtitle', value))}
                {renderInputField('CTA Button Text', brandingContent.services.ctaButtonText, (value) => handleContentChange('services', 'ctaButtonText', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'shop' && (
          <div className="space-y-6">
            {/* Shop Page Header */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiShoppingBag className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Shop Page Header</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Page Title', brandingContent.shop.pageTitle, (value) => handleContentChange('shop', 'pageTitle', value))}
                {renderInputField('Page Subtitle', brandingContent.shop.pageSubtitle, (value) => handleContentChange('shop', 'pageSubtitle', value))}
              </div>
            </div>

            {/* Newsletter Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Newsletter Section</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {renderInputField('Newsletter Title', brandingContent.shop.newsletterTitle, (value) => handleContentChange('shop', 'newsletterTitle', value))}
                  {renderInputField('Newsletter Subtitle', brandingContent.shop.newsletterSubtitle, (value) => handleContentChange('shop', 'newsletterSubtitle', value))}
                </div>

                <div className="space-y-4">
                  {renderInputField('Button Text', brandingContent.shop.newsletterButtonText, (value) => handleContentChange('shop', 'newsletterButtonText', value))}
                  {renderInputField('Email Placeholder', brandingContent.shop.newsletterPlaceholder, (value) => handleContentChange('shop', 'newsletterPlaceholder', value))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add missing sections for consultation, login, and footer */}
        {activeBrandingSection === 'consultation' && (
          <div className="space-y-6">
            {/* Consultation Page Header */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiCalendar className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Consultation Page Header</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Page Title', brandingContent.consultation.pageTitle, (value) => handleContentChange('consultation', 'pageTitle', value))}
                {renderInputField('Page Subtitle', brandingContent.consultation.pageSubtitle, (value) => handleContentChange('consultation', 'pageSubtitle', value))}
              </div>
            </div>

            {/* Form Labels */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Form Labels</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInputField('Name Label', brandingContent.consultation.nameLabel, (value) => handleContentChange('consultation', 'nameLabel', value))}
                {renderInputField('Email Label', brandingContent.consultation.emailLabel, (value) => handleContentChange('consultation', 'emailLabel', value))}
                {renderInputField('Phone Label', brandingContent.consultation.phoneLabel, (value) => handleContentChange('consultation', 'phoneLabel', value))}
                {renderInputField('Service Label', brandingContent.consultation.serviceLabel, (value) => handleContentChange('consultation', 'serviceLabel', value))}
                {renderInputField('Date Label', brandingContent.consultation.dateLabel, (value) => handleContentChange('consultation', 'dateLabel', value))}
                {renderInputField('Time Label', brandingContent.consultation.timeLabel, (value) => handleContentChange('consultation', 'timeLabel', value))}
                {renderInputField('Message Label', brandingContent.consultation.messageLabel, (value) => handleContentChange('consultation', 'messageLabel', value))}
                {renderInputField('Submit Button Text', brandingContent.consultation.submitButtonText, (value) => handleContentChange('consultation', 'submitButtonText', value))}
              </div>
            </div>

            {/* Consultation Info */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Consultation Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {renderInputField('Info Title', brandingContent.consultation.infoTitle, (value) => handleContentChange('consultation', 'infoTitle', value))}
                  {renderInputField('Info Subtitle', brandingContent.consultation.infoSubtitle, (value) => handleContentChange('consultation', 'infoSubtitle', value))}
                </div>

                <div className="space-y-4">
                  {renderInputField('Info Point 1', brandingContent.consultation.info1, (value) => handleContentChange('consultation', 'info1', value))}
                  {renderInputField('Info Point 2', brandingContent.consultation.info2, (value) => handleContentChange('consultation', 'info2', value))}
                  {renderInputField('Info Point 3', brandingContent.consultation.info3, (value) => handleContentChange('consultation', 'info3', value))}
                  {renderInputField('Info Point 4', brandingContent.consultation.info4, (value) => handleContentChange('consultation', 'info4', value))}
                </div>
              </div>
            </div>

            {/* Success Message */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Success Message</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Success Title', brandingContent.consultation.successTitle, (value) => handleContentChange('consultation', 'successTitle', value))}
                {renderInputField('Success Message', brandingContent.consultation.successMessage, (value) => handleContentChange('consultation', 'successMessage', value), 'text', '', 3)}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'login' && (
          <div className="space-y-6">
            {/* Login Page Header */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Login Page Header</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Page Title', brandingContent.login.pageTitle, (value) => handleContentChange('login', 'pageTitle', value))}
                {renderInputField('Page Subtitle', brandingContent.login.pageSubtitle, (value) => handleContentChange('login', 'pageSubtitle', value))}
              </div>
            </div>

            {/* Form Labels */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Form Elements</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {renderInputField('Email Label', brandingContent.login.emailLabel, (value) => handleContentChange('login', 'emailLabel', value))}
                  {renderInputField('Password Label', brandingContent.login.passwordLabel, (value) => handleContentChange('login', 'passwordLabel', value))}
                  {renderInputField('Remember Me Label', brandingContent.login.rememberLabel, (value) => handleContentChange('login', 'rememberLabel', value))}
                  {renderInputField('Forgot Password Text', brandingContent.login.forgotPasswordText, (value) => handleContentChange('login', 'forgotPasswordText', value))}
                </div>

                <div className="space-y-4">
                  {renderInputField('Sign In Button Text', brandingContent.login.signInButtonText, (value) => handleContentChange('login', 'signInButtonText', value))}
                  {renderInputField('Sign Up Text', brandingContent.login.signUpText, (value) => handleContentChange('login', 'signUpText', value))}
                  {renderInputField('Sign Up Link Text', brandingContent.login.signUpLinkText, (value) => handleContentChange('login', 'signUpLinkText', value))}
                </div>
              </div>
            </div>

            {/* Footer Links */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Footer Links</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {renderInputField('Footer Text', brandingContent.login.footerText, (value) => handleContentChange('login', 'footerText', value))}
                {renderInputField('Contact Link Text', brandingContent.login.contactLinkText, (value) => handleContentChange('login', 'contactLinkText', value))}
                {renderInputField('Privacy Link Text', brandingContent.login.privacyLinkText, (value) => handleContentChange('login', 'privacyLinkText', value))}
                {renderInputField('Terms Link Text', brandingContent.login.termsLinkText, (value) => handleContentChange('login', 'termsLinkText', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'signup' && (
          <div className="space-y-6">
            {/* Sign Up Page Header */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiUserCheck className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Sign Up Page Header</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Page Title', brandingContent.signup.pageTitle, (value) => handleContentChange('signup', 'pageTitle', value))}
                {renderInputField('Page Subtitle', brandingContent.signup.pageSubtitle, (value) => handleContentChange('signup', 'pageSubtitle', value))}
              </div>
            </div>

            {/* Form Labels */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Form Elements</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInputField('Name Label', brandingContent.signup.nameLabel, (value) => handleContentChange('signup', 'nameLabel', value))}
                {renderInputField('Email Label', brandingContent.signup.emailLabel, (value) => handleContentChange('signup', 'emailLabel', value))}
                {renderInputField('Password Label', brandingContent.signup.passwordLabel, (value) => handleContentChange('signup', 'passwordLabel', value))}
                {renderInputField('Confirm Password Label', brandingContent.signup.confirmPasswordLabel, (value) => handleContentChange('signup', 'confirmPasswordLabel', value))}
                {renderInputField('Phone Label', brandingContent.signup.phoneLabel, (value) => handleContentChange('signup', 'phoneLabel', value))}
                {renderInputField('Sign Up Button Text', brandingContent.signup.signUpButtonText, (value) => handleContentChange('signup', 'signUpButtonText', value))}
              </div>
            </div>

            {/* Terms and Links */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Terms and Links</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {renderInputField('Agree Terms Text', brandingContent.signup.agreeTermsText, (value) => handleContentChange('signup', 'agreeTermsText', value))}
                  {renderInputField('Terms Link Text', brandingContent.signup.termsLinkText, (value) => handleContentChange('signup', 'termsLinkText', value))}
                  {renderInputField('And Text', brandingContent.signup.andText, (value) => handleContentChange('signup', 'andText', value))}
                  {renderInputField('Privacy Link Text', brandingContent.signup.privacyLinkText, (value) => handleContentChange('signup', 'privacyLinkText', value))}
                </div>

                <div className="space-y-4">
                  {renderInputField('Login Text', brandingContent.signup.loginText, (value) => handleContentChange('signup', 'loginText', value))}
                  {renderInputField('Login Link Text', brandingContent.signup.loginLinkText, (value) => handleContentChange('signup', 'loginLinkText', value))}
                </div>
              </div>
            </div>

            {/* Benefits Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Benefits Section</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {renderInputField('Benefits Title', brandingContent.signup.benefitsTitle, (value) => handleContentChange('signup', 'benefitsTitle', value))}
                </div>

                <div className="space-y-4">
                  {renderInputField('Benefit 1', brandingContent.signup.benefit1, (value) => handleContentChange('signup', 'benefit1', value))}
                  {renderInputField('Benefit 2', brandingContent.signup.benefit2, (value) => handleContentChange('signup', 'benefit2', value))}
                  {renderInputField('Benefit 3', brandingContent.signup.benefit3, (value) => handleContentChange('signup', 'benefit3', value))}
                  {renderInputField('Benefit 4', brandingContent.signup.benefit4, (value) => handleContentChange('signup', 'benefit4', value))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'cart' && (
          <div className="space-y-6">
            {/* Cart Page Header */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiShoppingBag className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Cart Page Content</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {renderInputField('Page Title', brandingContent.cart.pageTitle, (value) => handleContentChange('cart', 'pageTitle', value))}
                  {renderInputField('Empty Cart Title', brandingContent.cart.emptyCartTitle, (value) => handleContentChange('cart', 'emptyCartTitle', value))}
                  {renderInputField('Empty Cart Message', brandingContent.cart.emptyCartMessage, (value) => handleContentChange('cart', 'emptyCartMessage', value))}
                  {renderInputField('Continue Shopping Text', brandingContent.cart.continueShoppingText, (value) => handleContentChange('cart', 'continueShoppingText', value))}
                </div>

                <div className="space-y-4">
                  {renderInputField('Item Text', brandingContent.cart.itemText, (value) => handleContentChange('cart', 'itemText', value))}
                  {renderInputField('Quantity Text', brandingContent.cart.quantityText, (value) => handleContentChange('cart', 'quantityText', value))}
                  {renderInputField('Price Text', brandingContent.cart.priceText, (value) => handleContentChange('cart', 'priceText', value))}
                  {renderInputField('Total Text', brandingContent.cart.totalText, (value) => handleContentChange('cart', 'totalText', value))}
                </div>
              </div>
            </div>

            {/* Checkout Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Checkout Section</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInputField('Subtotal Text', brandingContent.cart.subtotalText, (value) => handleContentChange('cart', 'subtotalText', value))}
                {renderInputField('Shipping Text', brandingContent.cart.shippingText, (value) => handleContentChange('cart', 'shippingText', value))}
                {renderInputField('Tax Text', brandingContent.cart.taxText, (value) => handleContentChange('cart', 'taxText', value))}
                {renderInputField('Grand Total Text', brandingContent.cart.grandTotalText, (value) => handleContentChange('cart', 'grandTotalText', value))}
                {renderInputField('Checkout Button Text', brandingContent.cart.checkoutButtonText, (value) => handleContentChange('cart', 'checkoutButtonText', value))}
                {renderInputField('Update Cart Text', brandingContent.cart.updateCartText, (value) => handleContentChange('cart', 'updateCartText', value))}
                {renderInputField('Remove Text', brandingContent.cart.removeText, (value) => handleContentChange('cart', 'removeText', value))}
              </div>
            </div>

            {/* Shipping Info */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Shipping Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Free Shipping Threshold', brandingContent.cart.freeShippingThreshold, (value) => handleContentChange('cart', 'freeShippingThreshold', value))}
                {renderInputField('Shipping Calculated Text', brandingContent.cart.shippingCalculated, (value) => handleContentChange('cart', 'shippingCalculated', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'productDetail' && (
          <div className="space-y-6">
            {/* Product Detail Page Content */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Product Detail Content</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInputField('Add to Cart Text', brandingContent.productDetail.addToCartText, (value) => handleContentChange('productDetail', 'addToCartText', value))}
                {renderInputField('Quantity Text', brandingContent.productDetail.quantityText, (value) => handleContentChange('productDetail', 'quantityText', value))}
                {renderInputField('In Stock Text', brandingContent.productDetail.inStockText, (value) => handleContentChange('productDetail', 'inStockText', value))}
                {renderInputField('Out of Stock Text', brandingContent.productDetail.outOfStockText, (value) => handleContentChange('productDetail', 'outOfStockText', value))}
                {renderInputField('Low Stock Text', brandingContent.productDetail.lowStockText, (value) => handleContentChange('productDetail', 'lowStockText', value))}
              </div>
            </div>

            {/* Product Sections */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Product Sections</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {renderInputField('Description Title', brandingContent.productDetail.descriptionTitle, (value) => handleContentChange('productDetail', 'descriptionTitle', value))}
                {renderInputField('Ingredients Title', brandingContent.productDetail.ingredientsTitle, (value) => handleContentChange('productDetail', 'ingredientsTitle', value))}
                {renderInputField('How to Use Title', brandingContent.productDetail.howToUseTitle, (value) => handleContentChange('productDetail', 'howToUseTitle', value))}
                {renderInputField('Reviews Title', brandingContent.productDetail.reviewsTitle, (value) => handleContentChange('productDetail', 'reviewsTitle', value))}
                {renderInputField('Related Products Title', brandingContent.productDetail.relatedProductsTitle, (value) => handleContentChange('productDetail', 'relatedProductsTitle', value))}
              </div>
            </div>

            {/* Product Tabs */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Product Tabs</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {renderInputField('Overview Tab', brandingContent.productDetail.overviewTab, (value) => handleContentChange('productDetail', 'overviewTab', value))}
                {renderInputField('Ingredients Tab', brandingContent.productDetail.ingredientsTab, (value) => handleContentChange('productDetail', 'ingredientsTab', value))}
                {renderInputField('Reviews Tab', brandingContent.productDetail.reviewsTab, (value) => handleContentChange('productDetail', 'reviewsTab', value))}
              </div>
            </div>

            {/* Review Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Review Section</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Write Review Text', brandingContent.productDetail.writeReviewText, (value) => handleContentChange('productDetail', 'writeReviewText', value))}
                {renderInputField('No Reviews Text', brandingContent.productDetail.noReviewsText, (value) => handleContentChange('productDetail', 'noReviewsText', value))}
                {renderInputField('Rating Text', brandingContent.productDetail.ratingText, (value) => handleContentChange('productDetail', 'ratingText', value))}
                {renderInputField('Review Text', brandingContent.productDetail.reviewText, (value) => handleContentChange('productDetail', 'reviewText', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'userDashboard' && (
          <div className="space-y-6">
            {/* User Dashboard Header */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiBarChart className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Dashboard Header</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Welcome Title', brandingContent.userDashboard.welcomeTitle, (value) => handleContentChange('userDashboard', 'welcomeTitle', value))}
                {renderInputField('Welcome Subtitle', brandingContent.userDashboard.welcomeSubtitle, (value) => handleContentChange('userDashboard', 'welcomeSubtitle', value))}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Quick Stats Labels</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {renderInputField('Next Appointment Text', brandingContent.userDashboard.nextAppointmentText, (value) => handleContentChange('userDashboard', 'nextAppointmentText', value))}
                {renderInputField('Total Appointments Text', brandingContent.userDashboard.totalAppointmentsText, (value) => handleContentChange('userDashboard', 'totalAppointmentsText', value))}
                {renderInputField('Member Since Text', brandingContent.userDashboard.memberSinceText, (value) => handleContentChange('userDashboard', 'memberSinceText', value))}
                {renderInputField('Loyalty Points Text', brandingContent.userDashboard.loyaltyPointsText, (value) => handleContentChange('userDashboard', 'loyaltyPointsText', value))}
              </div>
            </div>

            {/* Section Titles */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Section Titles</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {renderInputField('Appointments Title', brandingContent.userDashboard.appointmentsTitle, (value) => handleContentChange('userDashboard', 'appointmentsTitle', value))}
                {renderInputField('Orders Title', brandingContent.userDashboard.ordersTitle, (value) => handleContentChange('userDashboard', 'ordersTitle', value))}
                {renderInputField('Profile Title', brandingContent.userDashboard.profileTitle, (value) => handleContentChange('userDashboard', 'profileTitle', value))}
                {renderInputField('Favorites Title', brandingContent.userDashboard.favoritesTitle, (value) => handleContentChange('userDashboard', 'favoritesTitle', value))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Action Buttons</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {renderInputField('Book Appointment Text', brandingContent.userDashboard.bookAppointmentText, (value) => handleContentChange('userDashboard', 'bookAppointmentText', value))}
                {renderInputField('View All Orders Text', brandingContent.userDashboard.viewAllOrdersText, (value) => handleContentChange('userDashboard', 'viewAllOrdersText', value))}
                {renderInputField('Update Profile Text', brandingContent.userDashboard.updateProfileText, (value) => handleContentChange('userDashboard', 'updateProfileText', value))}
              </div>
            </div>

            {/* Empty States */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Empty State Messages</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {renderInputField('No Appointments Text', brandingContent.userDashboard.noAppointmentsText, (value) => handleContentChange('userDashboard', 'noAppointmentsText', value))}
                {renderInputField('No Orders Text', brandingContent.userDashboard.noOrdersText, (value) => handleContentChange('userDashboard', 'noOrdersText', value))}
                {renderInputField('No Favorites Text', brandingContent.userDashboard.noFavoritesText, (value) => handleContentChange('userDashboard', 'noFavoritesText', value))}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'footer' && (
          <div className="space-y-6">
            {/* Footer Content */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center mb-4">
                <FiEdit className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Footer Content</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {renderInputField('Footer Description', brandingContent.footer.description, (value) => handleContentChange('footer', 'description', value), 'text', '', 4)}
                  {renderInputField('Copyright Text', brandingContent.footer.copyrightText, (value) => handleContentChange('footer', 'copyrightText', value))}
                </div>

                <div className="space-y-4">
                  {renderInputField('Quick Links Title', brandingContent.footer.quickLinksTitle, (value) => handleContentChange('footer', 'quickLinksTitle', value))}
                  {renderInputField('Services Title', brandingContent.footer.servicesTitle, (value) => handleContentChange('footer', 'servicesTitle', value))}
                  {renderInputField('Contact Title', brandingContent.footer.contactTitle, (value) => handleContentChange('footer', 'contactTitle', value))}
                  {renderInputField('Follow Title', brandingContent.footer.followTitle, (value) => handleContentChange('footer', 'followTitle', value))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview()
      case 'appointments':
        return renderAppointments()
      case 'customers':
        return renderCustomers()
      case 'orders':
        return renderOrders()
      case 'products':
        return renderProducts()
      case 'branding':
        return renderBranding()
      case 'settings':
        return renderSettings()
      default:
        return renderOverview()
    }
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Oops! Something went wrong</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchDashboardData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 cursor-pointer"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen relative overflow-hidden"
         style={{
           background: `linear-gradient(135deg, ${branding.colors.primary}15, ${branding.colors.secondary}10, ${branding.colors.accent}05)`,
           backgroundAttachment: 'fixed'
         }}>

      {/* Mobile Header */}
      <div className="lg:hidden bg-white/95 backdrop-blur-sm border-b border-gray-200/50 px-4 py-3 sticky top-0 z-40">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <img
              src={branding.logo}
              alt={branding.siteName}
              className="h-8 w-auto"
            />
            <div>
              <h1 className="text-lg font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-xs text-gray-600">{adminData.name}</p>
            </div>
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors duration-200 cursor-pointer"
          >
            <FiMenu className="w-6 h-6 text-gray-700" />
          </button>
        </div>
      </div>

      <div className="flex min-h-screen">
        {/* Desktop Sidebar */}
        <div className="hidden lg:flex lg:w-80 lg:flex-col lg:fixed lg:inset-y-0">
          <div className="flex flex-col flex-grow pt-8 pb-4 overflow-y-auto">
            <div className="flex items-center flex-shrink-0 px-6 mb-8">
              <img
                src={branding.logo}
                alt={branding.siteName}
                className="h-10 w-auto mr-3"
              />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Admin Dashboard</h1>
                <p className="text-sm text-gray-600">{branding.siteName}</p>
              </div>
            </div>

            <div className="px-6 mb-8">
              <div className="relative rounded-2xl p-6 text-white overflow-hidden transform hover:scale-[1.01] transition-all duration-300 shadow-xl border border-white/20"
                   style={{ background: `linear-gradient(135deg, ${branding.colors.secondary}, ${branding.colors.accent})` }}>
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                <div className="relative z-10">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-16 h-16 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center border border-white/30">
                      <FiUser className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-white">{adminData.name}</h3>
                      <p className="text-sm text-white/80">{adminData.role}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex-grow px-6">
              <div className="space-y-4">
                {/* Dashboard Navigation */}
                <nav className="space-y-2">
                  <h4 className="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-3 px-2">Dashboard</h4>
                  {tabs.map((tab, index) => {
                    const Icon = tab.icon
                    const isActive = activeTab === tab.id
                    return (
                      <button
                        key={tab.id}
                        onClick={() => handleTabChange(tab.id)}
                        className={`group w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 cursor-pointer transform hover:scale-[1.02] hover:shadow-lg ${
                          isActive
                            ? 'text-white shadow-lg border border-white/20'
                            : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-white hover:shadow-md border border-transparent'
                        }`}
                        style={{
                          background: isActive
                            ? `linear-gradient(135deg, ${branding.colors.secondary}, ${branding.colors.accent})`
                            : 'transparent',
                          animationDelay: `${index * 50}ms`
                        }}
                      >
                        <div className={`p-1.5 rounded-md transition-colors duration-200 ${
                          isActive ? 'bg-white/20' : 'bg-gray-100 group-hover:bg-gray-200'
                        }`}>
                          <Icon className={`w-4 h-4 ${isActive ? 'text-white' : 'text-gray-600'}`} />
                        </div>
                        <span className="font-medium">{tab.name}</span>
                        {isActive && (
                          <div className="ml-auto w-2 h-2 rounded-full bg-white/60 animate-pulse"></div>
                        )}
                      </button>
                  )
                })}
              </nav>

              <div className="mt-8 pt-6 border-t border-gray-200/50 space-y-3">
                {/* Site Navigation */}
                <div className="space-y-2">
                  <button
                    onClick={() => onNavigate('home')}
                    className="w-full text-left text-gray-600 hover:text-gray-800 transition-colors duration-200 cursor-pointer flex items-center space-x-3 px-2 py-2 rounded-lg hover:bg-gray-50"
                  >
                    <FiHome className="w-4 h-4" />
                    <span className="text-sm">Back to Home</span>
                  </button>
                  <button
                    onClick={() => onNavigate('user-dashboard')}
                    className="w-full text-left text-blue-600 hover:text-blue-700 transition-colors duration-200 cursor-pointer flex items-center space-x-3 px-2 py-2 rounded-lg hover:bg-blue-50"
                  >
                    <FiUser className="w-4 h-4" />
                    <span className="text-sm">User Dashboard</span>
                  </button>
                </div>

                <div className="pt-3 border-t border-gray-200/50">
                  <button
                    onClick={onLogout}
                    className="w-full text-left text-red-600 hover:text-red-700 transition-colors duration-200 cursor-pointer flex items-center space-x-3 px-2 py-2 rounded-lg hover:bg-red-50"
                  >
                    <FiLogOut className="w-4 h-4" />
                    <span className="text-sm">Logout</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:pl-80">
          <main className="py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              {renderContent()}
            </div>
          </main>
        </div>
      </div>

      {/* Mobile Navigation Modal */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300"
            onClick={() => setIsMobileMenuOpen(false)}
          />

          {/* Modal */}
          <div className="fixed inset-0 flex items-center justify-center p-4">
            <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl w-full max-w-sm max-h-[80vh] overflow-y-auto border border-white/20">
              <div className="p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <img
                      src={branding.logo}
                      alt={branding.siteName}
                      className="h-8 w-auto"
                    />
                    <div>
                      <h2 className="text-lg font-bold text-gray-900">Admin Dashboard</h2>
                      <p className="text-sm text-gray-600">{adminData.name}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors duration-200 cursor-pointer"
                  >
                    <FiX className="w-5 h-5 text-gray-700" />
                  </button>
                </div>

                {/* Navigation */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <h4 className="text-xs font-bold text-gray-600 uppercase tracking-wide">Dashboard</h4>
                  </div>
                  <nav className="space-y-1">
                    {tabs.map((tab) => {
                      const Icon = tab.icon
                      const isActive = activeTab === tab.id
                      return (
                        <button
                          key={tab.id}
                          onClick={() => {
                            handleTabChange(tab.id)
                            setIsMobileMenuOpen(false) // Close mobile menu on selection
                          }}
                          className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 cursor-pointer ${
                            isActive
                              ? 'text-white shadow-md transform scale-[1.02]'
                              : 'text-gray-700 hover:bg-gray-50 hover:transform hover:scale-[1.01]'
                          }`}
                          style={isActive ? { backgroundColor: branding.colors.secondary } : {}}
                        >
                          <div className={`p-1.5 rounded-md transition-colors duration-200 ${
                            isActive ? 'bg-white/20' : 'bg-gray-100'
                          }`}>
                            <Icon className={`w-4 h-4 ${isActive ? 'text-white' : 'text-gray-600'}`} />
                          </div>
                          <span className="font-medium">{tab.name}</span>
                        </button>
                      )
                    })}
                  </nav>

                  {/* Mobile Site Navigation */}
                  <div className="pt-4 mt-4 border-t border-gray-200 space-y-2">
                    <button
                      onClick={() => {
                        onNavigate('home')
                        setIsMobileMenuOpen(false)
                      }}
                      className="w-full text-left text-gray-600 hover:text-gray-800 transition-colors duration-200 cursor-pointer flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-gray-50"
                    >
                      <FiHome className="w-4 h-4" />
                      <span>Back to Home</span>
                    </button>
                    <button
                      onClick={() => {
                        onNavigate('user-dashboard')
                        setIsMobileMenuOpen(false)
                      }}
                      className="w-full text-left text-blue-600 hover:text-blue-700 transition-colors duration-200 cursor-pointer flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-blue-50"
                    >
                      <FiUser className="w-4 h-4" />
                      <span>User Dashboard</span>
                    </button>
                    <button
                      onClick={() => {
                        onLogout()
                        setIsMobileMenuOpen(false)
                      }}
                      className="w-full text-left text-red-600 hover:text-red-700 transition-colors duration-200 cursor-pointer flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-red-50"
                    >
                      <FiLogOut className="w-4 h-4" />
                      <span>Logout</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>

        {/* Toast Notification */}
        {toast && (
          <div className="fixed top-4 right-4 z-50">
            <div className={`px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 ${
              toast.type === 'success'
                ? 'bg-green-500 text-white'
                : toast.type === 'error'
                ? 'bg-red-500 text-white'
                : 'bg-blue-500 text-white'
            }`}>
              <div className="flex-shrink-0">
                {toast.type === 'success' && (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                )}
                {toast.type === 'error' && (
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <span className="font-medium">{toast.message}</span>
              <button
                onClick={() => setToast(null)}
                className="flex-shrink-0 ml-4 text-white hover:text-gray-200"
              >
                <FiX className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        {/* Confirmation Dialog */}
        {confirmDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">{confirmDialog.title}</h3>
                </div>
              </div>

              <p className="text-gray-600 mb-6">{confirmDialog.message}</p>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setConfirmDialog(null)}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    confirmDialog.onConfirm()
                    setConfirmDialog(null)
                  }}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default AdminDashboard

export default AdminDashboard
