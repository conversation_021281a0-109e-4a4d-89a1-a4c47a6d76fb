# MicroLocs Backend

A comprehensive TypeScript backend API for microlocation services with MVC architecture, MongoDB database, and complete authentication system.

## Features

- **Authentication & Authorization**: JWT-based auth with token blacklisting and role-based access control
- **Appointment Management**: Book, manage, and track appointments with availability checking
- **Service Management**: Manage available services and categories
- **E-commerce**: Product catalog, shopping cart, and order management with discount codes
- **User Management**: Profile management, favorites, password change, and notification preferences
- **Review System**: Product reviews and ratings with statistics
- **Admin Dashboard**: Complete admin panel with comprehensive analytics
- **Content Management**: Branding, FAQ, terms, privacy policy management
- **Business Management**: Business profile, hours, payment settings
- **Testimonials**: Customer testimonials management
- **Multi-Channel Notifications**: Email, SMS, push, and in-app notifications with templates
- **File Upload**: Image upload with validation, optimization, and management
- **Analytics**: Comprehensive business analytics with revenue, customer, and product insights
- **Discount System**: Discount codes, promotions, and gift cards with validation
- **Loyalty Program**: Points-based rewards system with tier management
- **Referral Program**: Customer referral system with automated rewards
- **Staff Management**: Staff profiles, availability scheduling, and service assignments
- **Waitlist Management**: Appointment waitlist with automated notifications
- **SEO Management**: Complete SEO settings, meta tags, and structured data
- **Theme Customization**: UI theme settings and comprehensive branding options
- **Cache Management**: Multi-level caching with Redis support and cleanup
- **Data Management**: Export/import functionality and database backup/restore
- **Customer Notes**: Private customer notes and interaction history
- **Email Templates**: Customizable email templates for all communications
- **Site Settings**: Global configuration and feature toggles
- **Rate Limiting**: Protection against abuse with different limits per endpoint
- **Input Validation**: Comprehensive request validation
- **Error Handling**: Centralized error handling with proper logging
- **Database Seeding**: Sample data for development
- **Production Ready**: Complete production deployment configuration

## Tech Stack

- **Runtime**: Node.js
- **Language**: TypeScript
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose
- **Authentication**: JWT (JSON Web Tokens)
- **Validation**: Express Validator
- **Email**: Nodemailer
- **Security**: Helmet, CORS, Rate Limiting
- **Documentation**: Built-in API documentation

## Project Structure

```
src/
├── config/          # Configuration files
├── controllers/     # Request handlers
├── middleware/      # Custom middleware
├── models/          # MongoDB models
├── routes/          # API routes
├── services/        # Business logic
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
├── scripts/         # Database seeding scripts
└── server.ts        # Main server file
```

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd microlocsbackend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your configuration:
   ```env
   PORT=3000
   NODE_ENV=development
   MONGODB_URI=mongodb://localhost:27017/microlocsbackend
   JWT_SECRET=your-super-secret-jwt-key-here
   EMAIL_HOST=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   ```

4. **Start MongoDB**
   Make sure MongoDB is running on your system.

5. **Seed the database** (optional)
   ```bash
   npm run seed
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

## Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build the TypeScript code
- `npm start` - Start production server
- `npm run seed` - Seed database with sample data
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/forgot-password` - Password reset request
- `POST /api/auth/reset-password` - Reset password
- `GET /api/auth/verify` - Verify JWT token

### Appointments
- `GET /api/appointments/availability` - Get available time slots
- `POST /api/appointments` - Create appointment
- `GET /api/appointments/user` - Get user appointments
- `PUT /api/appointments/:id` - Update appointment
- `DELETE /api/appointments/:id` - Cancel appointment

### Services
- `GET /api/services` - Get all services
- `GET /api/services/:id` - Get service details
- `GET /api/services/categories` - Get service categories

### Products
- `GET /api/products` - Get all products (with pagination)
- `GET /api/products/:id` - Get product details
- `GET /api/products/categories` - Get product categories

### Cart
- `GET /api/cart` - Get user cart
- `POST /api/cart/items` - Add item to cart
- `PUT /api/cart/items/:itemId` - Update cart item
- `DELETE /api/cart/items/:itemId` - Remove item from cart

### Orders
- `POST /api/orders` - Create order
- `GET /api/orders` - Get user orders
- `GET /api/orders/:id` - Get order details

### User Profile
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/favorites` - Get user favorites
- `POST /api/users/favorites` - Add to favorites
- `DELETE /api/users/favorites/:productId` - Remove from favorites

### Reviews
- `GET /api/products/:id/reviews` - Get product reviews
- `POST /api/products/:id/reviews` - Submit review

### Notifications
- `GET /api/notifications` - Get user notifications
- `PUT /api/notifications/:id/read` - Mark as read

### Admin (Admin only)
- `GET /api/admin/dashboard` - Dashboard statistics
- `GET /api/admin/appointments` - Manage appointments
- `GET /api/admin/customers` - Manage customers
- `GET /api/admin/orders` - Manage orders
- `GET /api/admin/products` - Manage products

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information"
}
```

## Rate Limiting

- General API: 100 requests per 15 minutes
- Authentication endpoints: 5 requests per 15 minutes
- Password reset: 3 requests per hour

## Development

### Adding New Features

1. Create model in `src/models/`
2. Add controller in `src/controllers/`
3. Define routes in `src/routes/`
4. Add validation in `src/utils/validation.ts`
5. Update types in `src/types/index.ts`

### Database Models

- **User**: User accounts and profiles
- **Service**: Available services
- **Product**: Product catalog
- **Appointment**: Appointment bookings
- **Cart**: Shopping cart items
- **Order**: Order management
- **Review**: Product reviews
- **Notification**: User notifications

## Deployment

1. Build the application:
   ```bash
   npm run build
   ```

2. Set production environment variables

3. Start the production server:
   ```bash
   npm start
   ```

## Extended API Features

### Gift Cards & Loyalty
- `POST /api/gift-cards` - Create gift card
- `GET /api/gift-cards/:code/balance` - Check gift card balance
- `GET /api/users/loyalty` - Get user loyalty points
- `POST /api/users/loyalty/redeem` - Redeem loyalty reward

### Staff & Service Management
- `GET /api/admin/staff` - Manage staff members
- `GET /api/services/:id/add-ons` - Get service add-ons
- `POST /api/appointments/waitlist` - Add to appointment waitlist

### Customer Management
- `GET /api/admin/customers/:id/notes` - Customer notes
- `POST /api/appointments/:id/remind` - Send appointment reminders

### Site Configuration
- `GET /api/admin/seo` - SEO settings management
- `GET /api/admin/theme` - Theme customization
- `GET /api/admin/settings` - Site settings
- `GET /api/policies/:type` - Business policies

### Data & Analytics
- `GET /api/admin/export/:type` - Export data (CSV/JSON)
- `POST /api/admin/backup` - Create database backup
- `POST /api/admin/cache/clear` - Cache management

### Templates & Communication
- `GET /api/admin/email-templates` - Email template management
- `GET /api/admin/notification-templates` - Notification templates

## Production Services Integration

### SMS Service (Twilio)
```typescript
// Automatic SMS reminders and notifications
await SMSService.sendAppointmentReminder(phone, serviceName, date, time);
```

### Payment Processing (Stripe)
```typescript
// Complete payment processing with refunds
const paymentIntent = await PaymentService.createPaymentIntent(amount);
```

### Cache Management
```typescript
// Multi-level caching with Redis support
CacheService.setMemoryCache(key, data, ttl);
CacheService.setRedisCache(key, data, ttl);
```

### Notification System
```typescript
// Multi-channel notifications
await NotificationService.sendNotification({
  userId,
  channels: ['email', 'sms', 'push', 'in-app'],
  templateVariables: { name, date, time }
});
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
