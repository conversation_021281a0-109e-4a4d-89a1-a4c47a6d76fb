import { Router } from 'express';
import { NotificationController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation, paginationValidation } from '../utils/validation';

const router = Router();

// GET /api/notifications
router.get(
  '/',
  authenticate,
  validate(paginationValidation),
  NotificationController.getUserNotifications
);

// PUT /api/notifications/:id/read
router.put(
  '/:id/read',
  authenticate,
  validate(mongoIdValidation()),
  NotificationController.markAsRead
);

// PUT /api/notifications/read-all
router.put(
  '/read-all',
  authenticate,
  NotificationController.markAllAsRead
);

// DELETE /api/notifications/:id
router.delete(
  '/:id',
  authenticate,
  validate(mongoIdValidation()),
  NotificationController.deleteNotification
);

// Admin routes
// POST /api/notifications (admin only)
router.post(
  '/',
  authenticate,
  authorize('admin'),
  NotificationController.createNotification
);

export default router;
