import { Request, Response } from 'express';
import { Notification } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class NotificationController {
  static async getUserNotifications(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { page = 1, limit = 20, isRead } = req.query;
      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const filter: any = { user: req.user._id };

      if (isRead !== undefined) {
        filter.isRead = isRead === 'true';
      }

      const [notifications, total, unreadCount] = await Promise.all([
        Notification.find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        Notification.countDocuments(filter),
        Notification.countDocuments({ user: req.user._id, isRead: false })
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Notifications retrieved successfully', {
        notifications,
        unreadCount,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get user notifications error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async markAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;

      const notification = await Notification.findOne({
        _id: id,
        user: req.user._id
      });

      if (!notification) {
        sendNotFound(res, 'Notification not found');
        return;
      }

      notification.isRead = true;
      await notification.save();

      sendSuccess(res, 'Notification marked as read', notification);
    } catch (error) {
      console.error('Mark notification as read error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async markAllAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      await Notification.updateMany(
        { user: req.user._id, isRead: false },
        { isRead: true }
      );

      sendSuccess(res, 'All notifications marked as read');
    } catch (error) {
      console.error('Mark all notifications as read error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteNotification(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;

      const notification = await Notification.findOne({
        _id: id,
        user: req.user._id
      });

      if (!notification) {
        sendNotFound(res, 'Notification not found');
        return;
      }

      await Notification.findByIdAndDelete(id);

      sendSuccess(res, 'Notification deleted successfully');
    } catch (error) {
      console.error('Delete notification error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createNotification(req: Request, res: Response): Promise<void> {
    try {
      const { user, title, message, type } = req.body;

      const notification = await Notification.create({
        user,
        title,
        message,
        type
      });

      sendSuccess(res, 'Notification created successfully', notification, 201);
    } catch (error) {
      console.error('Create notification error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
