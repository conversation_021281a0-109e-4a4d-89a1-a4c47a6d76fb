import { Router } from 'express';
import {
  SiteSettingsController,
  StaffController,
  ServiceAddonController,
  WaitlistController,
  ReferralController,
  PolicyController,
  ThemeController,
  NotificationTemplateController,
  DataManagementController,
  ServiceCategoryController,
  CustomerNoteController,
  EmailTemplateController,
  AppointmentReminderController
} from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation, paginationValidation } from '../utils/validation';
import multer from 'multer';

const router = Router();
const upload = multer({ dest: 'uploads/temp/' });

// Site Settings Routes
router.get('/settings', SiteSettingsController.getPublicSettings);
router.get('/admin/settings', authenticate, authorize('admin'), SiteSettingsController.getSiteSettings);
router.put('/admin/settings', authenticate, authorize('admin'), SiteSettingsController.updateSiteSettings);

// Staff Management Routes
router.get('/admin/staff', authenticate, authorize('admin'), validate(paginationValidation), StaffController.getAllStaff);
router.get('/admin/staff/:id', authenticate, authorize('admin'), validate(mongoIdValidation()), StaffController.getStaffById);
router.post('/admin/staff', authenticate, authorize('admin'), StaffController.createStaff);
router.put('/admin/staff/:id', authenticate, authorize('admin'), validate(mongoIdValidation()), StaffController.updateStaff);
router.delete('/admin/staff/:id', authenticate, authorize('admin'), validate(mongoIdValidation()), StaffController.deleteStaff);
router.get('/admin/staff/:id/availability', authenticate, authorize('admin'), validate(mongoIdValidation()), StaffController.getStaffAvailability);
router.put('/admin/staff/:id/availability', authenticate, authorize('admin'), validate(mongoIdValidation()), StaffController.updateStaffAvailability);

// Service Add-ons Routes
router.get('/services/:id/add-ons', validate(mongoIdValidation()), ServiceAddonController.getServiceAddons);
router.post('/admin/services/:id/add-ons', authenticate, authorize('admin'), validate(mongoIdValidation()), ServiceAddonController.createServiceAddon);
router.put('/admin/services/add-ons/:addonId', authenticate, authorize('admin'), validate(mongoIdValidation('addonId')), ServiceAddonController.updateServiceAddon);
router.delete('/admin/services/add-ons/:addonId', authenticate, authorize('admin'), validate(mongoIdValidation('addonId')), ServiceAddonController.deleteServiceAddon);

// Waitlist Routes
router.post('/appointments/waitlist', WaitlistController.addToWaitlist);
router.get('/admin/waitlist', authenticate, authorize('admin'), validate(paginationValidation), WaitlistController.getWaitlist);
router.put('/admin/waitlist/:id', authenticate, authorize('admin'), validate(mongoIdValidation()), WaitlistController.updateWaitlistStatus);
router.delete('/admin/waitlist/:id', authenticate, authorize('admin'), validate(mongoIdValidation()), WaitlistController.removeFromWaitlist);
router.post('/admin/waitlist/notify', authenticate, authorize('admin'), WaitlistController.notifyWaitlist);

// Referral Program Routes
router.get('/users/referral', authenticate, ReferralController.getUserReferral);
router.post('/referrals/validate', ReferralController.validateReferralCode);
router.get('/admin/referrals', authenticate, authorize('admin'), validate(paginationValidation), ReferralController.getAllReferrals);
router.get('/admin/referrals/settings', authenticate, authorize('admin'), ReferralController.getReferralSettings);
router.put('/admin/referrals/settings', authenticate, authorize('admin'), ReferralController.updateReferralSettings);

// Policy Routes
router.get('/policies/:type', PolicyController.getPolicy);
router.get('/admin/policies', authenticate, authorize('admin'), PolicyController.getAllPolicies);
router.put('/admin/policies/:type', authenticate, authorize('admin'), PolicyController.updatePolicy);

// Theme Settings Routes
router.get('/theme', ThemeController.getPublicTheme);
router.get('/admin/theme', authenticate, authorize('admin'), ThemeController.getThemeSettings);
router.put('/admin/theme', authenticate, authorize('admin'), ThemeController.updateThemeSettings);
router.post('/admin/theme/reset', authenticate, authorize('admin'), ThemeController.resetTheme);

// Notification Templates Routes
router.get('/admin/notification-templates', authenticate, authorize('admin'), NotificationTemplateController.getAllNotificationTemplates);
router.get('/admin/notification-templates/:type/:channel', authenticate, authorize('admin'), NotificationTemplateController.getNotificationTemplate);
router.put('/admin/notification-templates/:type/:channel', authenticate, authorize('admin'), NotificationTemplateController.updateNotificationTemplate);
router.delete('/admin/notification-templates/:type/:channel', authenticate, authorize('admin'), NotificationTemplateController.deleteNotificationTemplate);

// Data Management Routes
router.get('/admin/export/:type', authenticate, authorize('admin'), DataManagementController.exportData);
router.post('/admin/import/:type', authenticate, authorize('admin'), upload.single('file'), DataManagementController.importData);
router.post('/admin/backup', authenticate, authorize('admin'), DataManagementController.createBackup);
router.post('/admin/restore', authenticate, authorize('admin'), upload.single('backup'), DataManagementController.restoreBackup);
router.post('/admin/cache/clear', authenticate, authorize('admin'), DataManagementController.clearCache);
router.get('/admin/cache/stats', authenticate, authorize('admin'), DataManagementController.getCacheStats);
router.post('/admin/cache/cleanup', authenticate, authorize('admin'), DataManagementController.cleanupCache);

// Service Categories Routes
router.get('/services/categories', ServiceCategoryController.getServiceCategories);
router.post('/admin/services/categories', authenticate, authorize('admin'), ServiceCategoryController.createServiceCategory);
router.put('/admin/services/categories/:id', authenticate, authorize('admin'), ServiceCategoryController.updateServiceCategory);
router.delete('/admin/services/categories/:id', authenticate, authorize('admin'), ServiceCategoryController.deleteServiceCategory);

// Customer Notes Routes
router.get('/admin/customers/:id/notes', authenticate, authorize('admin'), validate(mongoIdValidation()), CustomerNoteController.getCustomerNotes);
router.post('/admin/customers/:id/notes', authenticate, authorize('admin'), validate(mongoIdValidation()), CustomerNoteController.createCustomerNote);
router.put('/admin/customers/notes/:noteId', authenticate, authorize('admin'), validate(mongoIdValidation('noteId')), CustomerNoteController.updateCustomerNote);
router.delete('/admin/customers/notes/:noteId', authenticate, authorize('admin'), validate(mongoIdValidation('noteId')), CustomerNoteController.deleteCustomerNote);

// Email Templates Routes
router.get('/admin/email-templates', authenticate, authorize('admin'), EmailTemplateController.getAllEmailTemplates);
router.get('/admin/email-templates/:type', authenticate, authorize('admin'), EmailTemplateController.getEmailTemplate);
router.put('/admin/email-templates/:type', authenticate, authorize('admin'), EmailTemplateController.updateEmailTemplate);
router.post('/admin/email-templates', authenticate, authorize('admin'), EmailTemplateController.createEmailTemplate);
router.delete('/admin/email-templates/:type', authenticate, authorize('admin'), EmailTemplateController.deleteEmailTemplate);
router.post('/admin/email-templates/:type/preview', authenticate, authorize('admin'), EmailTemplateController.previewEmailTemplate);

// Appointment Reminders Routes
router.post('/appointments/:id/remind', authenticate, authorize('admin'), validate(mongoIdValidation()), AppointmentReminderController.sendReminder);
router.post('/admin/appointments/reminders/schedule', authenticate, authorize('admin'), AppointmentReminderController.scheduleAutomaticReminders);

export default router;
