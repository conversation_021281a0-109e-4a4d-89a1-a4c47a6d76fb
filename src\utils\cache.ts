import fs from 'fs';
import path from 'path';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  maxSize?: number; // Maximum cache size in MB
}

export class CacheService {
  private static memoryCache = new Map<string, { data: any; expires: number }>();
  private static cacheDir = path.join(process.cwd(), 'cache');

  static {
    // Ensure cache directory exists
    if (!fs.existsSync(this.cacheDir)) {
      fs.mkdirSync(this.cacheDir, { recursive: true });
    }
  }

  // Memory Cache Methods
  static setMemoryCache(key: string, data: any, ttl: number = 3600): void {
    const expires = Date.now() + (ttl * 1000);
    this.memoryCache.set(key, { data, expires });
  }

  static getMemoryCache(key: string): any | null {
    const cached = this.memoryCache.get(key);
    if (!cached) return null;

    if (Date.now() > cached.expires) {
      this.memoryCache.delete(key);
      return null;
    }

    return cached.data;
  }

  static deleteMemoryCache(key: string): boolean {
    return this.memoryCache.delete(key);
  }

  static clearMemoryCache(): void {
    this.memoryCache.clear();
  }

  // File Cache Methods
  static setFileCache(key: string, data: any, ttl: number = 3600): void {
    try {
      const cacheFile = path.join(this.cacheDir, `${key}.json`);
      const cacheData = {
        data,
        expires: Date.now() + (ttl * 1000),
        created: Date.now()
      };
      fs.writeFileSync(cacheFile, JSON.stringify(cacheData));
    } catch (error) {
      console.error('File cache write error:', error);
    }
  }

  static getFileCache(key: string): any | null {
    try {
      const cacheFile = path.join(this.cacheDir, `${key}.json`);
      if (!fs.existsSync(cacheFile)) return null;

      const cacheData = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
      
      if (Date.now() > cacheData.expires) {
        fs.unlinkSync(cacheFile);
        return null;
      }

      return cacheData.data;
    } catch (error) {
      console.error('File cache read error:', error);
      return null;
    }
  }

  static deleteFileCache(key: string): boolean {
    try {
      const cacheFile = path.join(this.cacheDir, `${key}.json`);
      if (fs.existsSync(cacheFile)) {
        fs.unlinkSync(cacheFile);
        return true;
      }
      return false;
    } catch (error) {
      console.error('File cache delete error:', error);
      return false;
    }
  }

  // Image Cache Methods
  static clearImageCache(): { success: boolean; error?: string } {
    try {
      const imageCacheDir = path.join(this.cacheDir, 'images');
      if (fs.existsSync(imageCacheDir)) {
        fs.rmSync(imageCacheDir, { recursive: true, force: true });
        fs.mkdirSync(imageCacheDir, { recursive: true });
      }
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  // Data Cache Methods
  static clearDataCache(): { success: boolean; error?: string } {
    try {
      const dataCacheDir = path.join(this.cacheDir, 'data');
      if (fs.existsSync(dataCacheDir)) {
        fs.rmSync(dataCacheDir, { recursive: true, force: true });
        fs.mkdirSync(dataCacheDir, { recursive: true });
      }
      
      // Clear memory cache for data
      const dataKeys = Array.from(this.memoryCache.keys()).filter(key => key.startsWith('data:'));
      dataKeys.forEach(key => this.memoryCache.delete(key));
      
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  // Clear All Caches
  static clearAllCaches(): { success: boolean; error?: string } {
    try {
      // Clear memory cache
      this.clearMemoryCache();

      // Clear file cache
      if (fs.existsSync(this.cacheDir)) {
        fs.rmSync(this.cacheDir, { recursive: true, force: true });
        fs.mkdirSync(this.cacheDir, { recursive: true });
        
        // Recreate subdirectories
        fs.mkdirSync(path.join(this.cacheDir, 'images'), { recursive: true });
        fs.mkdirSync(path.join(this.cacheDir, 'data'), { recursive: true });
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  // Cache Statistics
  static getCacheStats(): {
    memoryCache: { size: number; keys: string[] };
    fileCache: { size: number; files: string[] };
    diskUsage: number; // in MB
  } {
    const memoryCacheStats = {
      size: this.memoryCache.size,
      keys: Array.from(this.memoryCache.keys())
    };

    let fileCacheFiles: string[] = [];
    let diskUsage = 0;

    try {
      if (fs.existsSync(this.cacheDir)) {
        const files = fs.readdirSync(this.cacheDir, { recursive: true });
        fileCacheFiles = files.filter(file => typeof file === 'string') as string[];
        
        // Calculate disk usage
        const stats = fs.statSync(this.cacheDir);
        diskUsage = stats.size / (1024 * 1024); // Convert to MB
      }
    } catch (error) {
      console.error('Cache stats error:', error);
    }

    return {
      memoryCache: memoryCacheStats,
      fileCache: { size: fileCacheFiles.length, files: fileCacheFiles },
      diskUsage
    };
  }

  // Cleanup expired cache entries
  static cleanupExpiredCache(): { cleaned: number; errors: number } {
    let cleaned = 0;
    let errors = 0;

    // Cleanup memory cache
    for (const [key, value] of this.memoryCache.entries()) {
      if (Date.now() > value.expires) {
        this.memoryCache.delete(key);
        cleaned++;
      }
    }

    // Cleanup file cache
    try {
      if (fs.existsSync(this.cacheDir)) {
        const files = fs.readdirSync(this.cacheDir);
        for (const file of files) {
          if (file.endsWith('.json')) {
            try {
              const filePath = path.join(this.cacheDir, file);
              const cacheData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
              
              if (Date.now() > cacheData.expires) {
                fs.unlinkSync(filePath);
                cleaned++;
              }
            } catch (error) {
              errors++;
              console.error(`Error cleaning cache file ${file}:`, error);
            }
          }
        }
      }
    } catch (error) {
      errors++;
      console.error('Cache cleanup error:', error);
    }

    return { cleaned, errors };
  }

  // Redis Cache Methods (for production with Redis)
  static async setRedisCache(key: string, data: any, ttl: number = 3600): Promise<boolean> {
    try {
      // In production, implement Redis caching
      // const redis = require('redis');
      // const client = redis.createClient();
      // await client.setEx(key, ttl, JSON.stringify(data));
      
      // Fallback to memory cache
      this.setMemoryCache(key, data, ttl);
      return true;
    } catch (error) {
      console.error('Redis cache error:', error);
      return false;
    }
  }

  static async getRedisCache(key: string): Promise<any | null> {
    try {
      // In production, implement Redis caching
      // const redis = require('redis');
      // const client = redis.createClient();
      // const cached = await client.get(key);
      // return cached ? JSON.parse(cached) : null;
      
      // Fallback to memory cache
      return this.getMemoryCache(key);
    } catch (error) {
      console.error('Redis cache error:', error);
      return null;
    }
  }
}
