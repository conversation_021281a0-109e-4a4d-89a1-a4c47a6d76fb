import { FiLoader } from 'react-icons/fi'

const Loading = ({ 
  size = 'md', 
  text = 'Loading...', 
  fullScreen = false,
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  }

  const LoadingSpinner = () => (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <FiLoader 
        className={`${sizeClasses[size]} animate-spin mb-4`}
        style={{ color: '#f3d016' }}
      />
      {text && (
        <p className={`${textSizeClasses[size]} text-gray-600 font-medium`}>
          {text}
        </p>
      )}
    </div>
  )

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
        <LoadingSpinner />
      </div>
    )
  }

  return <LoadingSpinner />
}

// Skeleton loading component for better UX
export const SkeletonLoader = ({ className = '', lines = 3 }) => (
  <div className={`animate-pulse ${className}`}>
    {Array.from({ length: lines }).map((_, index) => (
      <div
        key={index}
        className={`bg-gray-200 rounded h-4 mb-2 ${
          index === lines - 1 ? 'w-3/4' : 'w-full'
        }`}
      />
    ))}
  </div>
)

// Card skeleton for product/service cards
export const CardSkeleton = ({ className = '' }) => (
  <div className={`animate-pulse bg-white rounded-2xl overflow-hidden shadow-lg ${className}`}>
    <div className="aspect-video bg-gray-200" />
    <div className="p-6">
      <div className="h-6 bg-gray-200 rounded mb-3" />
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded" />
        <div className="h-4 bg-gray-200 rounded w-3/4" />
      </div>
      <div className="mt-4 h-10 bg-gray-200 rounded" />
    </div>
  </div>
)

export default Loading
