import mongoose, { Schema, Document } from 'mongoose';

export interface IContent extends Document {
  _id: string;
  type: 'terms' | 'privacy' | 'faq';
  content: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IFAQ extends Document {
  _id: string;
  question: string;
  answer: string;
  category: string;
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const contentSchema = new Schema<IContent>({
  type: {
    type: String,
    required: true,
    enum: ['terms', 'privacy'],
    unique: true
  },
  content: {
    type: String,
    required: true
  }
}, {
  timestamps: true
});

const faqSchema = new Schema<IFAQ>({
  question: {
    type: String,
    required: true,
    trim: true
  },
  answer: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    required: true,
    trim: true,
    default: 'General'
  },
  order: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better query performance
faqSchema.index({ category: 1, order: 1 });
faqSchema.index({ isActive: 1 });

export const Content = mongoose.model<IContent>('Content', contentSchema);
export const FAQ = mongoose.model<IFAQ>('FAQ', faqSchema);
