import { Router } from 'express';
import { TestimonialController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation, paginationValidation } from '../utils/validation';

const router = Router();

// GET /api/testimonials
router.get(
  '/',
  validate(paginationValidation),
  TestimonialController.getTestimonials
);

// GET /api/testimonials/:id
router.get(
  '/:id',
  validate(mongoIdValidation()),
  TestimonialController.getTestimonialById
);

// Admin routes
// POST /api/testimonials (admin only)
router.post(
  '/',
  authenticate,
  authorize('admin'),
  TestimonialController.createTestimonial
);

// PUT /api/testimonials/:id (admin only)
router.put(
  '/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  TestimonialController.updateTestimonial
);

// DELETE /api/testimonials/:id (admin only)
router.delete(
  '/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  TestimonialController.deleteTestimonial
);

export default router;
