import mongoose, { Schema, Document } from 'mongoose';

export interface INotificationTemplate extends Document {
  _id: string;
  type: string;
  channel: 'email' | 'sms' | 'push';
  subject?: string;
  content: string;
  variables: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const notificationTemplateSchema = new Schema<INotificationTemplate>({
  type: {
    type: String,
    required: true,
    enum: [
      'appointment-reminder',
      'appointment-confirmation',
      'appointment-cancelled',
      'order-status',
      'order-shipped',
      'order-delivered',
      'payment-received',
      'welcome',
      'password-reset',
      'promotion'
    ]
  },
  channel: {
    type: String,
    required: true,
    enum: ['email', 'sms', 'push']
  },
  subject: {
    type: String,
    trim: true,
    maxlength: 200,
    required: function(this: any) {
      return this.channel === 'email';
    }
  },
  content: {
    type: String,
    required: true
  },
  variables: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Compound index for unique template per type and channel
notificationTemplateSchema.index({ type: 1, channel: 1 }, { unique: true });
notificationTemplateSchema.index({ isActive: 1 });

export const NotificationTemplate = mongoose.model<INotificationTemplate>('NotificationTemplate', notificationTemplateSchema);
