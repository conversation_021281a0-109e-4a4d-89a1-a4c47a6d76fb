import { useState } from 'react'

// Test component to trigger errors for testing the ErrorBoundary
const ErrorTest = () => {
  const [shouldError, setShouldError] = useState(false)

  if (shouldError) {
    throw new Error('Test error for ErrorBoundary')
  }

  return (
    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <h3 className="text-lg font-semibold text-yellow-800 mb-2">Error Boundary Test</h3>
      <p className="text-yellow-700 mb-4">
        This component is for testing the error boundary. Click the button below to trigger an error.
      </p>
      <button
        onClick={() => setShouldError(true)}
        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
      >
        Trigger Error
      </button>
    </div>
  )
}

export default ErrorTest
