import { apiService } from './apiService'

class BrandingService {
  /**
   * Get branding configuration from API
   */
  async getBranding() {
    try {
      const response = await apiService.get('/branding')
      return response
    } catch (error) {
      console.error('Error fetching branding:', error)
      return {
        success: false,
        message: 'Failed to fetch branding data',
        error: error.message
      }
    }
  }

  /**
   * Get business profile information
   */
  async getBusinessProfile() {
    try {
      const response = await apiService.get('/business-profile')
      return response
    } catch (error) {
      console.error('Error fetching business profile:', error)
      return {
        success: false,
        message: 'Failed to fetch business profile',
        error: error.message
      }
    }
  }

  /**
   * Get theme settings
   */
  async getThemeSettings() {
    try {
      const response = await apiService.get('/theme-settings')
      return response
    } catch (error) {
      console.error('Error fetching theme settings:', error)
      return {
        success: false,
        message: 'Failed to fetch theme settings',
        error: error.message
      }
    }
  }

  /**
   * Get site settings
   */
  async getSiteSettings() {
    try {
      const response = await apiService.get('/site-settings')
      return response
    } catch (error) {
      console.error('Error fetching site settings:', error)
      return {
        success: false,
        message: 'Failed to fetch site settings',
        error: error.message
      }
    }
  }

  /**
   * Get complete branding configuration including all related data
   */
  async getCompleteBranding() {
    try {
      const [
        brandingResponse,
        businessResponse,
        themeResponse,
        siteResponse
      ] = await Promise.all([
        this.getBranding(),
        this.getBusinessProfile(),
        this.getThemeSettings(),
        this.getSiteSettings()
      ])

      return {
        success: true,
        data: {
          branding: brandingResponse.success ? brandingResponse.data : null,
          business: businessResponse.success ? businessResponse.data : null,
          theme: themeResponse.success ? themeResponse.data : null,
          site: siteResponse.success ? siteResponse.data : null
        }
      }
    } catch (error) {
      console.error('Error fetching complete branding:', error)
      return {
        success: false,
        message: 'Failed to fetch complete branding data',
        error: error.message
      }
    }
  }
}

export const brandingService = new BrandingService()
