import apiService from './api.js'

class BrandingService {
  /**
   * Get branding configuration from API
   */
  async getBranding() {
    try {
      const response = await apiService.get('/branding')
      return response
    } catch (error) {
      console.error('Error fetching branding:', error)
      return {
        success: false,
        message: 'Failed to fetch branding data',
        error: error.message
      }
    }
  }

  /**
   * Get business profile information
   */
  async getBusinessProfile() {
    try {
      const response = await apiService.get('/business-profile')
      return response
    } catch (error) {
      console.error('Error fetching business profile:', error)
      return {
        success: false,
        message: 'Failed to fetch business profile',
        error: error.message
      }
    }
  }

  /**
   * Get theme settings
   */
  async getThemeSettings() {
    try {
      const response = await apiService.get('/theme-settings')
      return response
    } catch (error) {
      console.error('Error fetching theme settings:', error)
      return {
        success: false,
        message: 'Failed to fetch theme settings',
        error: error.message
      }
    }
  }

  /**
   * Get site settings
   */
  async getSiteSettings() {
    try {
      const response = await apiService.get('/site-settings')
      return response
    } catch (error) {
      console.error('Error fetching site settings:', error)
      return {
        success: false,
        message: 'Failed to fetch site settings',
        error: error.message
      }
    }
  }

  /**
   * Get complete branding configuration from single aggregated endpoint
   * This replaces multiple API calls with one efficient request
   */
  async getCompleteBranding() {
    try {
      // Try the new aggregated endpoint first
      const response = await apiService.get('/branding/complete')

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error('Invalid response format from aggregated endpoint')
      }
    } catch (error) {
      console.warn('Aggregated branding endpoint failed, falling back to individual requests:', error.message)

      // Fallback: use individual endpoints if aggregated endpoint fails
      try {
        const [
          brandingResponse,
          businessResponse,
          themeResponse,
          siteResponse
        ] = await Promise.all([
          this.getBranding(),
          this.getBusinessProfile(),
          this.getThemeSettings(),
          this.getSiteSettings()
        ])

        return {
          success: true,
          data: {
            branding: brandingResponse.success ? brandingResponse.data : null,
            business: businessResponse.success ? businessResponse.data : null,
            theme: themeResponse.success ? themeResponse.data : null,
            site: siteResponse.success ? siteResponse.data : null
          }
        }
      } catch (fallbackError) {
        console.error('Both aggregated and individual branding requests failed:', fallbackError)
        return {
          success: false,
          message: 'Failed to fetch complete branding data',
          error: fallbackError.message
        }
      }
    }
  }
}

export const brandingService = new BrandingService()
