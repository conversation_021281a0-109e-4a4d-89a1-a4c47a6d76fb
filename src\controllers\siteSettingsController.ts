import { Request, Response } from 'express';
import { SiteSettings } from '../models';
import { sendSuccess, sendError } from '../utils/response';

export class SiteSettingsController {
  static async getSiteSettings(req: Request, res: Response): Promise<void> {
    try {
      let settings = await SiteSettings.findOne();
      
      if (!settings) {
        // Create default settings if none exist
        settings = await SiteSettings.create({});
      }

      sendSuccess(res, 'Site settings retrieved successfully', settings);
    } catch (error) {
      console.error('Get site settings error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateSiteSettings(req: Request, res: Response): Promise<void> {
    try {
      const updateData = req.body;

      let settings = await SiteSettings.findOne();
      
      if (!settings) {
        settings = await SiteSettings.create(updateData);
      } else {
        // Deep merge the update data
        Object.keys(updateData).forEach(key => {
          if (typeof updateData[key] === 'object' && updateData[key] !== null) {
            (settings as any)[key] = { ...(settings as any)[key], ...updateData[key] };
          } else {
            (settings as any)[key] = updateData[key];
          }
        });
        await settings.save();
      }

      sendSuccess(res, 'Site settings updated successfully', settings);
    } catch (error) {
      console.error('Update site settings error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getPublicSettings(req: Request, res: Response): Promise<void> {
    try {
      const settings = await SiteSettings.findOne();
      
      if (!settings) {
        sendSuccess(res, 'Public settings retrieved successfully', {
          general: {
            siteName: 'MicroLocs',
            siteDescription: 'Professional hair care services',
            contactEmail: '<EMAIL>',
            contactPhone: '',
            address: '',
            timezone: 'America/New_York',
            currency: 'USD',
            language: 'en'
          },
          features: {
            enableAppointments: true,
            enableEcommerce: true,
            enableReviews: true,
            enableLoyaltyProgram: false,
            enableGiftCards: false,
            enableWaitlist: false,
            enableReferrals: false
          }
        });
        return;
      }

      // Return only public settings (exclude sensitive data)
      const publicSettings = {
        general: settings.general,
        features: settings.features,
        maintenance: {
          isMaintenanceMode: settings.maintenance.isMaintenanceMode,
          maintenanceMessage: settings.maintenance.maintenanceMessage
        }
      };

      sendSuccess(res, 'Public settings retrieved successfully', publicSettings);
    } catch (error) {
      console.error('Get public settings error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
