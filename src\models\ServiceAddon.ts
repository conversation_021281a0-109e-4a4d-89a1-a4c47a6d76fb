import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IServiceAddon extends Document {
  _id: string;
  service: Types.ObjectId;
  name: string;
  description: string;
  price: number;
  duration: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const serviceAddonSchema = new Schema<IServiceAddon>({
  service: {
    type: Schema.Types.ObjectId,
    ref: 'Service',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  duration: {
    type: Number,
    required: true,
    min: 5,
    max: 240
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better query performance
serviceAddonSchema.index({ service: 1 });
serviceAddonSchema.index({ isActive: 1 });

export const ServiceAddon = mongoose.model<IServiceAddon>('ServiceAddon', serviceAddonSchema);
