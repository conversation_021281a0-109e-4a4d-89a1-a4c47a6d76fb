import { Request, Response } from 'express';
import { AuthService } from '../services';
import { sendSuccess, sendError, sendCreated } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class AuthController {
  static async register(req: Request, res: Response): Promise<void> {
    try {
      const { firstName, lastName, email, phone, password } = req.body;

      const result = await AuthService.register({
        firstName,
        lastName,
        name: `${firstName} ${lastName}`.trim(),
        email,
        phone,
        password
      });

      sendCreated(res, 'User registered successfully', {
        user: result.user,
        token: result.token,
        refreshToken: result.refreshToken
      });
    } catch (error) {
      console.error('Registration error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { email, password, rememberMe } = req.body;

      const result = await AuthService.login(email, password);

      sendSuccess(res, 'Login successful', {
        user: result.user,
        token: result.token,
        refreshToken: result.refreshToken,
        rememberMe: rememberMe || false
      });
    } catch (error) {
      console.error('Login error:', error);
      sendError(res, (error as Error).message, undefined, 401);
    }
  }

  static async forgotPassword(req: Request, res: Response): Promise<void> {
    try {
      const { email } = req.body;

      await AuthService.forgotPassword(email);

      sendSuccess(res, 'Password reset instructions sent to your email');
    } catch (error) {
      console.error('Forgot password error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async resetPassword(req: Request, res: Response): Promise<void> {
    try {
      const { token, password } = req.body;

      await AuthService.resetPassword(token, password);

      sendSuccess(res, 'Password reset successful');
    } catch (error) {
      console.error('Reset password error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async verify(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'User not found', undefined, 401);
        return;
      }

      const user = await AuthService.verifyToken(req.user._id);

      sendSuccess(res, 'Token verified successfully', { user });
    } catch (error) {
      console.error('Token verification error:', error);
      sendError(res, (error as Error).message, undefined, 401);
    }
  }

  static async logout(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      // Extract token from Authorization header
      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);

        // Add token to blacklist (implement token blacklist service)
        await AuthService.blacklistToken(token);
      }

      sendSuccess(res, 'Logout successful');
    } catch (error) {
      console.error('Logout error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
