import { Request, Response } from 'express';
import { Testimonial } from '../models';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';

export class TestimonialController {
  static async getTestimonials(req: Request, res: Response): Promise<void> {
    try {
      const { featured, page = 1, limit = 10 } = req.query;
      const filter: any = { isActive: true };

      if (featured !== undefined) {
        filter.featured = featured === 'true';
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [testimonials, total] = await Promise.all([
        Testimonial.find(filter)
          .sort({ featured: -1, rating: -1, createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        Testimonial.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Testimonials retrieved successfully', {
        testimonials,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get testimonials error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createTestimonial(req: Request, res: Response): Promise<void> {
    try {
      const testimonialData = req.body;

      const testimonial = await Testimonial.create(testimonialData);

      sendCreated(res, 'Testimonial created successfully', testimonial);
    } catch (error) {
      console.error('Create testimonial error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateTestimonial(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const testimonial = await Testimonial.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!testimonial) {
        sendNotFound(res, 'Testimonial not found');
        return;
      }

      sendSuccess(res, 'Testimonial updated successfully', testimonial);
    } catch (error) {
      console.error('Update testimonial error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteTestimonial(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const testimonial = await Testimonial.findByIdAndDelete(id);

      if (!testimonial) {
        sendNotFound(res, 'Testimonial not found');
        return;
      }

      sendSuccess(res, 'Testimonial deleted successfully');
    } catch (error) {
      console.error('Delete testimonial error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getTestimonialById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const testimonial = await Testimonial.findById(id);

      if (!testimonial) {
        sendNotFound(res, 'Testimonial not found');
        return;
      }

      sendSuccess(res, 'Testimonial retrieved successfully', testimonial);
    } catch (error) {
      console.error('Get testimonial by ID error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
