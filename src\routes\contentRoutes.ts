import { Router } from 'express';
import { ContentController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation } from '../utils/validation';

const router = Router();

// GET /api/content/legal/:type
router.get(
  '/legal/:type',
  ContentController.getLegalContent
);

// PUT /api/content/legal/:type (admin only)
router.put(
  '/legal/:type',
  authenticate,
  authorize('admin'),
  ContentController.updateLegalContent
);

// GET /api/content/faq
router.get(
  '/faq',
  ContentController.getFAQContent
);

// PUT /api/content/faq (admin only)
router.put(
  '/faq',
  authenticate,
  authorize('admin'),
  ContentController.updateFAQContent
);

// POST /api/content/faq (admin only)
router.post(
  '/faq',
  authenticate,
  authorize('admin'),
  ContentController.createFAQ
);

// PUT /api/content/faq/:id (admin only)
router.put(
  '/faq/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  ContentController.updateFAQ
);

// DELETE /api/content/faq/:id (admin only)
router.delete(
  '/faq/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  ContentController.deleteFAQ
);

export default router;
