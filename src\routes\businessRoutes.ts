import { Router } from 'express';
import { BusinessController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';

const router = Router();

// GET /api/business-hours
router.get(
  '/business-hours',
  BusinessController.getBusinessHours
);

// Admin routes
// GET /api/admin/business-profile (admin only)
router.get(
  '/business-profile',
  authenticate,
  authorize('admin'),
  BusinessController.getBusinessProfile
);

// PUT /api/admin/business-profile (admin only)
router.put(
  '/business-profile',
  authenticate,
  authorize('admin'),
  BusinessController.updateBusinessProfile
);

// PUT /api/admin/business-hours (admin only)
router.put(
  '/business-hours',
  authenticate,
  authorize('admin'),
  BusinessController.updateBusinessHours
);

export default router;
