import { Request, Response } from 'express';
import { Review, Product } from '../models';
import { sendSuccess, sendError, sendNotFound, sendCreated } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class ReviewController {
  static async getProductReviews(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

      // Verify product exists
      const product = await Product.findById(id);
      if (!product) {
        sendNotFound(res, 'Product not found');
        return;
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      // Sort
      const sort: any = {};
      sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

      const [reviews, total] = await Promise.all([
        Review.find({ product: id })
          .populate('user', 'name')
          .sort(sort)
          .skip(skip)
          .limit(limitNum),
        Review.countDocuments({ product: id })
      ]);

      const totalPages = Math.ceil(total / limitNum);

      // Calculate rating statistics
      const ratingStats = await Review.aggregate([
        { $match: { product: product._id } },
        {
          $group: {
            _id: '$rating',
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: -1 } }
      ]);

      const averageRating = await Review.aggregate([
        { $match: { product: product._id } },
        {
          $group: {
            _id: null,
            average: { $avg: '$rating' },
            total: { $sum: 1 }
          }
        }
      ]);

      sendSuccess(res, 'Product reviews retrieved successfully', {
        reviews,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        },
        statistics: {
          averageRating: averageRating[0]?.average || 0,
          totalReviews: averageRating[0]?.total || 0,
          ratingDistribution: ratingStats
        }
      });
    } catch (error) {
      console.error('Get product reviews error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createReview(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;
      const { rating, comment } = req.body;

      // Verify product exists
      const product = await Product.findById(id);
      if (!product) {
        sendNotFound(res, 'Product not found');
        return;
      }

      // Check if user already reviewed this product
      const existingReview = await Review.findOne({
        user: req.user._id,
        product: id
      });

      if (existingReview) {
        sendError(res, 'You have already reviewed this product');
        return;
      }

      // Create review
      const review = await Review.create({
        user: req.user._id,
        product: id,
        rating,
        comment
      });

      await review.populate('user', 'name');

      // Update product rating and review count
      const reviews = await Review.find({ product: id });
      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = totalRating / reviews.length;

      await Product.findByIdAndUpdate(id, {
        rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
        reviewCount: reviews.length
      });

      sendCreated(res, 'Review created successfully', review);
    } catch (error) {
      console.error('Create review error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateReview(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;
      const { rating, comment } = req.body;

      const review = await Review.findOne({
        _id: id,
        user: req.user._id
      });

      if (!review) {
        sendNotFound(res, 'Review not found');
        return;
      }

      review.rating = rating;
      review.comment = comment;
      await review.save();

      await review.populate('user', 'name');

      // Update product rating
      const reviews = await Review.find({ product: review.product });
      const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
      const averageRating = totalRating / reviews.length;

      await Product.findByIdAndUpdate(review.product, {
        rating: Math.round(averageRating * 10) / 10
      });

      sendSuccess(res, 'Review updated successfully', review);
    } catch (error) {
      console.error('Update review error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteReview(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;

      const review = await Review.findOne({
        _id: id,
        user: req.user._id
      });

      if (!review) {
        sendNotFound(res, 'Review not found');
        return;
      }

      const productId = review.product;
      await Review.findByIdAndDelete(id);

      // Update product rating and review count
      const reviews = await Review.find({ product: productId });
      const averageRating = reviews.length > 0 
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
        : 0;

      await Product.findByIdAndUpdate(productId, {
        rating: Math.round(averageRating * 10) / 10,
        reviewCount: reviews.length
      });

      sendSuccess(res, 'Review deleted successfully');
    } catch (error) {
      console.error('Delete review error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
