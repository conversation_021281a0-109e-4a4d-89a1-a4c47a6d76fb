import { Request, Response } from 'express';
import { NotificationTemplate } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';

export class NotificationTemplateController {
  static async getNotificationTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { type, channel } = req.params;

      const template = await NotificationTemplate.findOne({ 
        type, 
        channel, 
        isActive: true 
      });

      if (!template) {
        sendNotFound(res, 'Notification template not found');
        return;
      }

      sendSuccess(res, 'Notification template retrieved successfully', template);
    } catch (error) {
      console.error('Get notification template error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateNotificationTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { type, channel } = req.params;
      const { subject, content, variables, isActive } = req.body;

      let template = await NotificationTemplate.findOne({ type, channel });

      if (!template) {
        // Create new template
        template = await NotificationTemplate.create({
          type,
          channel,
          subject,
          content,
          variables: variables || [],
          isActive: isActive !== undefined ? isActive : true
        });
      } else {
        // Update existing template
        if (subject !== undefined) template.subject = subject;
        if (content !== undefined) template.content = content;
        if (variables !== undefined) template.variables = variables;
        if (isActive !== undefined) template.isActive = isActive;
        await template.save();
      }

      sendSuccess(res, 'Notification template updated successfully', template);
    } catch (error) {
      console.error('Update notification template error:', error);
      if ((error as any).code === 11000) {
        sendError(res, 'Notification template for this type and channel already exists');
      } else {
        sendError(res, (error as Error).message);
      }
    }
  }

  static async getAllNotificationTemplates(req: Request, res: Response): Promise<void> {
    try {
      const { type, channel, active } = req.query;
      const filter: any = {};

      if (type) filter.type = type;
      if (channel) filter.channel = channel;
      if (active !== undefined) filter.isActive = active === 'true';

      const templates = await NotificationTemplate.find(filter)
        .sort({ type: 1, channel: 1 });

      sendSuccess(res, 'Notification templates retrieved successfully', templates);
    } catch (error) {
      console.error('Get all notification templates error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteNotificationTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { type, channel } = req.params;

      const template = await NotificationTemplate.findOneAndDelete({ type, channel });

      if (!template) {
        sendNotFound(res, 'Notification template not found');
        return;
      }

      sendSuccess(res, 'Notification template deleted successfully');
    } catch (error) {
      console.error('Delete notification template error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
