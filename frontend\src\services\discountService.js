import apiService from './api.js'

/**
 * Discount service for handling discount codes and promotions
 */
class DiscountService {
  /**
   * Validate discount code
   */
  async validateDiscount(code) {
    try {
      const response = await apiService.post('/discount-codes/validate', {
        code
      })
      return response
    } catch (error) {
      console.error('Validate discount error:', error)
      throw error
    }
  }

  /**
   * Apply discount to cart
   */
  async applyDiscount(code) {
    try {
      const response = await apiService.post('/cart/discount', {
        code
      })
      return response
    } catch (error) {
      console.error('Apply discount error:', error)
      throw error
    }
  }

  /**
   * Remove discount from cart
   */
  async removeDiscount() {
    try {
      const response = await apiService.delete('/cart/discount')
      return response
    } catch (error) {
      console.error('Remove discount error:', error)
      throw error
    }
  }

  // Admin methods

  /**
   * Get all discount codes (Admin)
   */
  async getDiscountCodes(params = {}) {
    try {
      const response = await apiService.get('/admin/discount-codes', params)
      return response
    } catch (error) {
      console.error('Get discount codes error:', error)
      throw error
    }
  }

  /**
   * Create discount code (Admin)
   */
  async createDiscountCode(discountData) {
    try {
      const response = await apiService.post('/admin/discount-codes', discountData)
      return response
    } catch (error) {
      console.error('Create discount code error:', error)
      throw error
    }
  }

  /**
   * Update discount code (Admin)
   */
  async updateDiscountCode(discountId, discountData) {
    try {
      const response = await apiService.put(`/admin/discount-codes/${discountId}`, discountData)
      return response
    } catch (error) {
      console.error('Update discount code error:', error)
      throw error
    }
  }

  /**
   * Delete discount code (Admin)
   */
  async deleteDiscountCode(discountId) {
    try {
      const response = await apiService.delete(`/admin/discount-codes/${discountId}`)
      return response
    } catch (error) {
      console.error('Delete discount code error:', error)
      throw error
    }
  }

  /**
   * Get discount code usage statistics (Admin)
   */
  async getDiscountUsage(discountId) {
    try {
      const response = await apiService.get(`/admin/discount-codes/${discountId}/usage`)
      return response
    } catch (error) {
      console.error('Get discount usage error:', error)
      throw error
    }
  }
}

/**
 * Gift card service for handling gift card operations
 */
class GiftCardService {
  /**
   * Create gift card
   */
  async createGiftCard(giftCardData) {
    try {
      const response = await apiService.post('/gift-cards', giftCardData)
      return response
    } catch (error) {
      console.error('Create gift card error:', error)
      throw error
    }
  }

  /**
   * Get gift card balance
   */
  async getGiftCardBalance(code) {
    try {
      const response = await apiService.get(`/gift-cards/${code}/balance`)
      return response
    } catch (error) {
      console.error('Get gift card balance error:', error)
      throw error
    }
  }

  /**
   * Get user gift cards
   */
  async getUserGiftCards() {
    try {
      const response = await apiService.get('/gift-cards/user')
      return response
    } catch (error) {
      console.error('Get user gift cards error:', error)
      throw error
    }
  }

  /**
   * Apply gift card to order
   */
  async applyGiftCard(code, amount) {
    try {
      const response = await apiService.post('/orders/gift-card', {
        code,
        amount
      })
      return response
    } catch (error) {
      console.error('Apply gift card error:', error)
      throw error
    }
  }

  // Admin methods

  /**
   * Get all gift cards (Admin)
   */
  async getAdminGiftCards(params = {}) {
    try {
      const response = await apiService.get('/admin/gift-cards', params)
      return response
    } catch (error) {
      console.error('Get admin gift cards error:', error)
      throw error
    }
  }

  /**
   * Update gift card (Admin)
   */
  async updateGiftCard(giftCardId, giftCardData) {
    try {
      const response = await apiService.put(`/admin/gift-cards/${giftCardId}`, giftCardData)
      return response
    } catch (error) {
      console.error('Update gift card error:', error)
      throw error
    }
  }

  /**
   * Deactivate gift card (Admin)
   */
  async deactivateGiftCard(giftCardId) {
    try {
      const response = await apiService.put(`/admin/gift-cards/${giftCardId}/deactivate`)
      return response
    } catch (error) {
      console.error('Deactivate gift card error:', error)
      throw error
    }
  }
}

/**
 * Loyalty service for handling loyalty program
 */
class LoyaltyService {
  /**
   * Get available rewards
   */
  async getRewards() {
    try {
      const response = await apiService.get('/loyalty/rewards')
      return response
    } catch (error) {
      console.error('Get rewards error:', error)
      throw error
    }
  }

  /**
   * Get user loyalty data
   */
  async getUserLoyalty() {
    try {
      const response = await apiService.get('/users/loyalty')
      return response
    } catch (error) {
      console.error('Get user loyalty error:', error)
      throw error
    }
  }

  /**
   * Redeem loyalty reward
   */
  async redeemReward(rewardId) {
    try {
      const response = await apiService.post('/users/loyalty/redeem', {
        rewardId
      })
      return response
    } catch (error) {
      console.error('Redeem reward error:', error)
      throw error
    }
  }

  /**
   * Get loyalty transactions
   */
  async getLoyaltyTransactions(params = {}) {
    try {
      const response = await apiService.get('/users/loyalty/transactions', params)
      return response
    } catch (error) {
      console.error('Get loyalty transactions error:', error)
      throw error
    }
  }

  // Admin methods

  /**
   * Create loyalty reward (Admin)
   */
  async createReward(rewardData) {
    try {
      const response = await apiService.post('/admin/loyalty/rewards', rewardData)
      return response
    } catch (error) {
      console.error('Create reward error:', error)
      throw error
    }
  }

  /**
   * Update loyalty reward (Admin)
   */
  async updateReward(rewardId, rewardData) {
    try {
      const response = await apiService.put(`/admin/loyalty/rewards/${rewardId}`, rewardData)
      return response
    } catch (error) {
      console.error('Update reward error:', error)
      throw error
    }
  }

  /**
   * Get loyalty program settings (Admin)
   */
  async getLoyaltySettings() {
    try {
      const response = await apiService.get('/admin/loyalty/settings')
      return response
    } catch (error) {
      console.error('Get loyalty settings error:', error)
      throw error
    }
  }

  /**
   * Update loyalty program settings (Admin)
   */
  async updateLoyaltySettings(settingsData) {
    try {
      const response = await apiService.put('/admin/loyalty/settings', settingsData)
      return response
    } catch (error) {
      console.error('Update loyalty settings error:', error)
      throw error
    }
  }
}

// Create and export singleton instances
const discountService = new DiscountService()
const giftCardService = new GiftCardService()
const loyaltyService = new LoyaltyService()

export { discountService, giftCardService, loyaltyService }
export default discountService
