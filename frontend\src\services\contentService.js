import apiService from './api.js'

/**
 * Content service for handling content management
 */
class ContentService {
  // Branding

  /**
   * Get branding content
   */
  async getBranding() {
    try {
      const response = await apiService.get('/branding')
      return response
    } catch (error) {
      console.error('Get branding error:', error)
      throw error
    }
  }

  /**
   * Update branding content (Admin)
   */
  async updateBranding(brandingData) {
    try {
      const response = await apiService.put('/branding', brandingData)
      return response
    } catch (error) {
      console.error('Update branding error:', error)
      throw error
    }
  }

  /**
   * Update specific branding section (Admin)
   */
  async updateBrandingSection(section, sectionData) {
    try {
      const response = await apiService.put(`/branding/${section}`, sectionData)
      return response
    } catch (error) {
      console.error('Update branding section error:', error)
      throw error
    }
  }

  // Legal Content

  /**
   * Get legal content (terms, privacy, etc.)
   */
  async getLegalContent(type) {
    try {
      const response = await apiService.get(`/content/legal/${type}`)
      return response
    } catch (error) {
      console.error('Get legal content error:', error)
      throw error
    }
  }

  /**
   * Update legal content (Admin)
   */
  async updateLegalContent(type, contentData) {
    try {
      const response = await apiService.put(`/content/legal/${type}`, contentData)
      return response
    } catch (error) {
      console.error('Update legal content error:', error)
      throw error
    }
  }

  // FAQ

  /**
   * Get FAQ content
   */
  async getFAQ() {
    try {
      const response = await apiService.get('/content/faq')
      return response
    } catch (error) {
      console.error('Get FAQ error:', error)
      throw error
    }
  }

  /**
   * Update FAQ content (Admin)
   */
  async updateFAQ(faqData) {
    try {
      const response = await apiService.put('/content/faq', faqData)
      return response
    } catch (error) {
      console.error('Update FAQ error:', error)
      throw error
    }
  }

  /**
   * Create FAQ item (Admin)
   */
  async createFAQ(faqItem) {
    try {
      const response = await apiService.post('/content/faq', faqItem)
      return response
    } catch (error) {
      console.error('Create FAQ error:', error)
      throw error
    }
  }

  /**
   * Update FAQ item (Admin)
   */
  async updateFAQItem(faqId, faqItem) {
    try {
      const response = await apiService.put(`/content/faq/${faqId}`, faqItem)
      return response
    } catch (error) {
      console.error('Update FAQ item error:', error)
      throw error
    }
  }

  /**
   * Delete FAQ item (Admin)
   */
  async deleteFAQItem(faqId) {
    try {
      const response = await apiService.delete(`/content/faq/${faqId}`)
      return response
    } catch (error) {
      console.error('Delete FAQ item error:', error)
      throw error
    }
  }

  // Testimonials

  /**
   * Get testimonials
   */
  async getTestimonials() {
    try {
      const response = await apiService.get('/testimonials')
      return response
    } catch (error) {
      console.error('Get testimonials error:', error)
      throw error
    }
  }

  /**
   * Add testimonial (Admin)
   */
  async addTestimonial(testimonialData) {
    try {
      const response = await apiService.post('/testimonials', testimonialData)
      return response
    } catch (error) {
      console.error('Add testimonial error:', error)
      throw error
    }
  }

  /**
   * Update testimonial (Admin)
   */
  async updateTestimonial(testimonialId, testimonialData) {
    try {
      const response = await apiService.put(`/testimonials/${testimonialId}`, testimonialData)
      return response
    } catch (error) {
      console.error('Update testimonial error:', error)
      throw error
    }
  }

  /**
   * Delete testimonial (Admin)
   */
  async deleteTestimonial(testimonialId) {
    try {
      const response = await apiService.delete(`/testimonials/${testimonialId}`)
      return response
    } catch (error) {
      console.error('Delete testimonial error:', error)
      throw error
    }
  }

  // SEO

  /**
   * Get SEO settings (Admin)
   */
  async getSEOSettings() {
    try {
      const response = await apiService.get('/admin/seo')
      return response
    } catch (error) {
      console.error('Get SEO settings error:', error)
      throw error
    }
  }

  /**
   * Update SEO settings (Admin)
   */
  async updateSEOSettings(seoData) {
    try {
      const response = await apiService.put('/admin/seo', seoData)
      return response
    } catch (error) {
      console.error('Update SEO settings error:', error)
      throw error
    }
  }

  // Theme

  /**
   * Get public theme settings
   */
  async getTheme() {
    try {
      const response = await apiService.get('/theme')
      return response
    } catch (error) {
      console.error('Get theme error:', error)
      throw error
    }
  }

  /**
   * Get theme settings (Admin)
   */
  async getThemeSettings() {
    try {
      const response = await apiService.get('/admin/theme')
      return response
    } catch (error) {
      console.error('Get theme settings error:', error)
      throw error
    }
  }

  /**
   * Update theme settings (Admin)
   */
  async updateThemeSettings(themeData) {
    try {
      const response = await apiService.put('/admin/theme', themeData)
      return response
    } catch (error) {
      console.error('Update theme settings error:', error)
      throw error
    }
  }

  // Policies

  /**
   * Get policy (cancellation, refund, etc.)
   */
  async getPolicy(type) {
    try {
      const response = await apiService.get(`/policies/${type}`)
      return response
    } catch (error) {
      console.error('Get policy error:', error)
      throw error
    }
  }

  /**
   * Update policy (Admin)
   */
  async updatePolicy(type, policyData) {
    try {
      const response = await apiService.put(`/admin/policies/${type}`, policyData)
      return response
    } catch (error) {
      console.error('Update policy error:', error)
      throw error
    }
  }

  // Email Templates

  /**
   * Get email templates (Admin)
   */
  async getEmailTemplates() {
    try {
      const response = await apiService.get('/admin/email-templates')
      return response
    } catch (error) {
      console.error('Get email templates error:', error)
      throw error
    }
  }

  /**
   * Get email template (Admin)
   */
  async getEmailTemplate(type) {
    try {
      const response = await apiService.get(`/admin/email-templates/${type}`)
      return response
    } catch (error) {
      console.error('Get email template error:', error)
      throw error
    }
  }

  /**
   * Update email template (Admin)
   */
  async updateEmailTemplate(type, templateData) {
    try {
      const response = await apiService.put(`/admin/email-templates/${type}`, templateData)
      return response
    } catch (error) {
      console.error('Update email template error:', error)
      throw error
    }
  }

  /**
   * Preview email template (Admin)
   */
  async previewEmailTemplate(type, previewData) {
    try {
      const response = await apiService.post(`/admin/email-templates/${type}/preview`, previewData)
      return response
    } catch (error) {
      console.error('Preview email template error:', error)
      throw error
    }
  }

  // Notification Templates

  /**
   * Get notification template (Admin)
   */
  async getNotificationTemplate(type, channel) {
    try {
      const response = await apiService.get(`/admin/notification-templates/${type}/${channel}`)
      return response
    } catch (error) {
      console.error('Get notification template error:', error)
      throw error
    }
  }

  /**
   * Update notification template (Admin)
   */
  async updateNotificationTemplate(type, channel, templateData) {
    try {
      const response = await apiService.put(`/admin/notification-templates/${type}/${channel}`, templateData)
      return response
    } catch (error) {
      console.error('Update notification template error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const contentService = new ContentService()
export default contentService
