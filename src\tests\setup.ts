import { connectDatabase, disconnectDatabase } from '../config/database';

// Setup test database connection
beforeAll(async () => {
  // Use test database
  process.env.MONGODB_URI = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/microlocsbackend_test';
  await connectDatabase();
});

// Cleanup after tests
afterAll(async () => {
  await disconnectDatabase();
});

// Set test environment
process.env.NODE_ENV = 'test';
