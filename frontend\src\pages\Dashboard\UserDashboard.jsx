import { useState } from 'react'
import {
  <PERSON>Calendar,
  FiShoppingBag,
  FiUser,
  FiHeart,
  FiClock,
  FiEdit3,
  <PERSON>Eye
} from 'react-icons/fi'

const UserDashboard = ({ onNavigate, onLogout }) => {
  const [activeTab, setActiveTab] = useState('overview')

  // Mock user data
  const userData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    joinDate: 'March 2024',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
  }

  // Mock appointments data
  const appointments = [
    {
      id: 1,
      service: 'Micro Locs Installation',
      date: '2024-02-15',
      time: '10:00 AM',
      status: 'confirmed',
      stylist: 'Tina',
      price: 300
    },
    {
      id: 2,
      service: 'Loc Maintenance',
      date: '2024-01-20',
      time: '2:00 PM',
      status: 'completed',
      stylist: 'Tina',
      price: 80
    },
    {
      id: 3,
      service: 'Consultation',
      date: '2024-01-05',
      time: '11:00 AM',
      status: 'completed',
      stylist: 'Tina',
      price: 50
    }
  ]

  // Mock orders data
  const orders = [
    {
      id: 'ORD-001',
      date: '2024-02-10',
      items: ['Loc Maintenance Shampoo', 'Natural Hair Oil'],
      total: 43.98,
      status: 'delivered'
    },
    {
      id: 'ORD-002',
      date: '2024-01-25',
      items: ['Deep Conditioning Mask'],
      total: 28.99,
      status: 'delivered'
    }
  ]

  // Mock favorites
  const favorites = [
    {
      id: 1,
      name: 'Loc Maintenance Shampoo',
      price: 24.99,
      image: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=100&h=100&fit=crop'
    },
    {
      id: 2,
      name: 'Natural Hair Oil Blend',
      price: 18.99,
      image: 'https://images.unsplash.com/photo-1608248543803-ba4f8c70ae0b?w=100&h=100&fit=crop'
    }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'delivered':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: FiUser },
    { id: 'appointments', name: 'Appointments', icon: FiCalendar },
    { id: 'orders', name: 'Orders', icon: FiShoppingBag },
    { id: 'favorites', name: 'Favorites', icon: FiHeart },
    { id: 'profile', name: 'Profile', icon: FiEdit3 }
  ]

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="rounded-2xl p-8 text-white" style={{ background: 'linear-gradient(to right, #008000, #006600)' }}>
        <h2 className="text-2xl font-bold mb-2">Welcome back, {userData.name}!</h2>
        <p className="text-green-100">Here's what's happening with your hair care journey</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 rounded-lg">
              <FiCalendar className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Next Appointment</p>
              <p className="text-lg font-semibold">Feb 15, 2024</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 rounded-lg">
              <FiShoppingBag className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Total Orders</p>
              <p className="text-lg font-semibold">{orders.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-3 bg-purple-100 rounded-lg">
              <FiHeart className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Favorite Products</p>
              <p className="text-lg font-semibold">{favorites.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {appointments.slice(0, 2).map((appointment) => (
            <div key={appointment.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <FiCalendar className="w-5 h-5 text-gray-400 mr-3" />
                <div>
                  <p className="font-medium">{appointment.service}</p>
                  <p className="text-sm text-gray-600">{appointment.date} at {appointment.time}</p>
                </div>
              </div>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                {appointment.status}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  const renderAppointments = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">My Appointments</h2>
        <button
          onClick={() => onNavigate('consultation')}
          className="text-white px-4 py-2 rounded-lg transition-colors duration-200"
          style={{ backgroundColor: '#008000' }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#006600'}
          onMouseLeave={(e) => e.target.style.backgroundColor = '#008000'}
        >
          Book New Appointment
        </button>
      </div>

      <div className="space-y-4">
        {appointments.map((appointment) => (
          <div key={appointment.id} className="bg-white rounded-xl p-6 shadow-sm">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="text-lg font-semibold mb-2">{appointment.service}</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center">
                    <FiCalendar className="w-4 h-4 mr-2" />
                    {appointment.date}
                  </div>
                  <div className="flex items-center">
                    <FiClock className="w-4 h-4 mr-2" />
                    {appointment.time}
                  </div>
                  <div className="flex items-center">
                    <FiUser className="w-4 h-4 mr-2" />
                    Stylist: {appointment.stylist}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                  {appointment.status}
                </span>
                <p className="text-lg font-semibold mt-2">${appointment.price}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const renderOrders = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Order History</h2>
        <button
          onClick={() => onNavigate('shop')}
          className="text-white px-4 py-2 rounded-lg transition-colors duration-200"
          style={{ backgroundColor: '#f3d016' }}
          onMouseEnter={(e) => e.target.style.backgroundColor = '#d4b014'}
          onMouseLeave={(e) => e.target.style.backgroundColor = '#f3d016'}
        >
          Shop Now
        </button>
      </div>

      <div className="space-y-4">
        {orders.map((order) => (
          <div key={order.id} className="bg-white rounded-xl p-6 shadow-sm">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="text-lg font-semibold mb-2">Order #{order.id}</h3>
                <p className="text-sm text-gray-600 mb-2">Ordered on {order.date}</p>
                <div className="space-y-1">
                  {order.items.map((item, index) => (
                    <p key={index} className="text-sm text-gray-700">• {item}</p>
                  ))}
                </div>
              </div>
              <div className="text-right">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                  {order.status}
                </span>
                <p className="text-lg font-semibold mt-2">${order.total}</p>
                <button
                  className="text-sm mt-2 flex items-center transition-colors duration-200"
                  style={{ color: '#008000' }}
                  onMouseEnter={(e) => e.target.style.color = '#006600'}
                  onMouseLeave={(e) => e.target.style.color = '#008000'}
                >
                  <FiEye className="w-4 h-4 mr-1" />
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const renderFavorites = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Favorite Products</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {favorites.map((product) => (
          <div key={product.id} className="bg-white rounded-xl overflow-hidden shadow-sm">
            <img
              src={product.image}
              alt={product.name}
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <h3 className="font-semibold mb-2">{product.name}</h3>
              <div className="flex justify-between items-center">
                <span className="text-lg font-bold" style={{ color: '#008000' }}>${product.price}</span>
                <button
                  className="text-white px-4 py-2 rounded-lg transition-colors duration-200"
                  style={{ backgroundColor: '#008000' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = '#006600'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = '#008000'}
                >
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const renderProfile = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Profile Settings</h2>

      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="flex items-center space-x-6 mb-6">
          <img
            src={userData.avatar}
            alt={userData.name}
            className="w-20 h-20 rounded-full object-cover"
          />
          <div>
            <h3 className="text-xl font-semibold">{userData.name}</h3>
            <p className="text-gray-600">Member since {userData.joinDate}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
            <input
              type="text"
              value={userData.name}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <input
              type="email"
              value={userData.email}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
            <input
              type="tel"
              value={userData.phone}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
            <button className="w-full px-4 py-2 border border-gray-300 rounded-lg text-left text-gray-500 hover:bg-gray-50">
              Change Password
            </button>
          </div>
        </div>

        <div className="mt-6 flex space-x-4">
          <button
            className="text-white px-6 py-2 rounded-lg transition-colors duration-200"
            style={{ backgroundColor: '#008000' }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#006600'}
            onMouseLeave={(e) => e.target.style.backgroundColor = '#008000'}
          >
            Save Changes
          </button>
          <button className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            Cancel
          </button>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview()
      case 'appointments':
        return renderAppointments()
      case 'orders':
        return renderOrders()
      case 'favorites':
        return renderFavorites()
      case 'profile':
        return renderProfile()
      default:
        return renderOverview()
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex items-center space-x-3 mb-6">
                <img
                  src={userData.avatar}
                  alt={userData.name}
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div>
                  <h3 className="font-semibold">{userData.name}</h3>
                  <p className="text-sm text-gray-600">Customer</p>
                </div>
              </div>

              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors duration-200 ${
                        activeTab === tab.id
                          ? 'bg-green-50 border border-green-200'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                      style={activeTab === tab.id ? { color: '#008000' } : {}}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{tab.name}</span>
                    </button>
                  )
                })}
              </nav>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={onLogout}
                  className="w-full text-left text-red-600 hover:text-red-700 transition-colors duration-200"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserDashboard
