import { useState, useEffect, useCallback, useRef } from 'react'
import {
  FiCalendar,
  FiShoppingBag,
  FiUser,
  FiHeart,
  FiClock,
  FiEdit3,
  FiEye,
  FiAlertCircle,
  FiMenu,
  FiX,
  FiLogOut
} from 'react-icons/fi'
import {
  userService,
  appointmentService,
  orderService,
  cartService,
  notificationService
} from '../../services'
import { FALLBACK_IMAGES } from '../../utils/constants'
import { useBranding } from '../../contexts/BrandingContext'
import Loading from '../../components/Loading'

const UserDashboard = ({ onNavigate, onLogout }) => {
  const { branding } = useBranding()
  const [activeTab, setActiveTab] = useState('overview')
  const [userData, setUserData] = useState(null)
  const [appointments, setAppointments] = useState([])
  const [orders, setOrders] = useState([])
  const [favorites, setFavorites] = useState([])
  const [notifications, setNotifications] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [isUpdating, setIsUpdating] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  })
  const loadingRef = useRef(false)

  // Memoized dashboard data loading function
  const loadDashboardData = useCallback(async () => {
    if (loadingRef.current) return // Prevent duplicate calls
      try {
        loadingRef.current = true
        setIsLoading(true)
        setError('')

        // Load all dashboard data in parallel
        const [
          profileResponse,
          appointmentsResponse,
          ordersResponse,
          favoritesResponse,
          notificationsResponse
        ] = await Promise.all([
          userService.getProfile(),
          appointmentService.getUserAppointments(),
          orderService.getUserOrders(),
          userService.getFavorites(),
          notificationService.getNotifications({ limit: 5 })
        ])

        if (profileResponse.success) {
          const user = profileResponse.data
          setUserData(user)
          setProfileData({
            firstName: user.firstName || '',
            lastName: user.lastName || '',
            email: user.email || '',
            phone: user.phone || ''
          })
        }

        if (appointmentsResponse.success) {
          setAppointments(appointmentsResponse.data)
        }

        if (ordersResponse.success) {
          setOrders(ordersResponse.data)
        }

        if (favoritesResponse.success) {
          setFavorites(favoritesResponse.data)
        }

        if (notificationsResponse.success) {
          setNotifications(notificationsResponse.data)
        }
      } catch (error) {
        console.error('Error loading dashboard data:', error)
        setError('Failed to load dashboard data. Please try again.')
      } finally {
        setIsLoading(false)
        loadingRef.current = false
      }
  }, []) // No dependencies to prevent recreation

  // Load data on component mount
  useEffect(() => {
    loadDashboardData()
  }, [loadDashboardData])

  // Close mobile menu when screen size changes to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) { // lg breakpoint
        setIsMobileMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Helper functions
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmed':
      case 'scheduled':
        return 'bg-blue-100 text-blue-800'
      case 'completed':
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'processing':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch (error) {
      return dateString
    }
  }

  const getNextAppointment = () => {
    const upcoming = appointments.filter(apt =>
      new Date(apt.date) > new Date() &&
      (apt.status === 'confirmed' || apt.status === 'scheduled')
    ).sort((a, b) => new Date(a.date) - new Date(b.date))

    return upcoming[0]
  }

  const addToCart = async (productId) => {
    try {
      setIsUpdating(true)
      const response = await cartService.addToCart({
        productId,
        quantity: 1
      })

      if (response.success) {
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'success',
            message: 'Product added to cart!'
          }
        }))
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'Failed to add product to cart'
        }
      }))
    } finally {
      setIsUpdating(false)
    }
  }

  const handleProfileChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleProfileSave = async () => {
    try {
      setIsUpdating(true)
      const response = await userService.updateProfile(profileData)

      if (response.success) {
        setUserData(prev => ({ ...prev, ...profileData }))
        window.dispatchEvent(new CustomEvent('show-notification', {
          detail: {
            type: 'success',
            message: 'Profile updated successfully!'
          }
        }))
      }
    } catch (error) {
      console.error('Error updating profile:', error)
      window.dispatchEvent(new CustomEvent('show-notification', {
        detail: {
          type: 'error',
          message: 'Failed to update profile'
        }
      }))
    } finally {
      setIsUpdating(false)
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: FiUser },
    { id: 'appointments', name: branding.content.dashboardAppointmentsTitle, icon: FiCalendar },
    { id: 'orders', name: branding.content.dashboardOrdersTitle, icon: FiShoppingBag },
    { id: 'favorites', name: branding.content.dashboardFavoritesTitle, icon: FiHeart },
    { id: 'profile', name: branding.content.dashboardProfileTitle, icon: FiEdit3 }
  ]

  const renderOverview = () => {
    const nextAppointment = getNextAppointment()

    return (
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="relative rounded-3xl p-8 text-white overflow-hidden transform hover:scale-[1.02] transition-all duration-500 shadow-2xl border border-white/20"
             style={{ background: 'linear-gradient(135deg, #008000, #006600, #004d00)' }}>
          {/* Glassmorphism overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5"></div>
          {/* Floating orbs */}
          <div className="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-4 left-4 w-16 h-16 bg-white/5 rounded-full blur-lg"></div>

          <div className="relative z-10">
            <h2 className="text-3xl font-bold mb-3 bg-gradient-to-r from-white to-green-100 bg-clip-text text-transparent">
              {branding.content.dashboardWelcome.replace('Welcome back', `Welcome back, ${userData?.firstName || userData?.name || 'User'}`)}
            </h2>
            <p className="text-green-100 text-lg font-medium">{branding.content.dashboardOverviewTitle}</p>
            <div className="mt-4 flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
              <span className="text-green-200 text-sm">Dashboard Active</span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="group bg-gradient-to-br from-white via-blue-50/30 to-white rounded-3xl p-8 shadow-2xl border border-blue-100/50 backdrop-blur-sm transform hover:scale-105 hover:shadow-3xl transition-all duration-500 cursor-pointer">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-xl group-hover:shadow-2xl transition-all duration-300 group-hover:scale-110">
                  <FiCalendar className="w-8 h-8 text-white" />
                  <div className="absolute inset-0 bg-white/20 rounded-2xl"></div>
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide">{branding.content.dashboardNextAppointment}</p>
                  <p className="text-2xl font-bold text-gray-800 mt-1">
                    {nextAppointment ? formatDate(nextAppointment.date) : 'None scheduled'}
                  </p>
                </div>
              </div>
              <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
            </div>
          </div>

          <div className="group bg-gradient-to-br from-white via-green-50/30 to-white rounded-3xl p-8 shadow-2xl border border-green-100/50 backdrop-blur-sm transform hover:scale-105 hover:shadow-3xl transition-all duration-500 cursor-pointer">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl shadow-xl group-hover:shadow-2xl transition-all duration-300 group-hover:scale-110">
                  <FiShoppingBag className="w-8 h-8 text-white" />
                  <div className="absolute inset-0 bg-white/20 rounded-2xl"></div>
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide">{branding.content.dashboardOrdersTitle}</p>
                  <p className="text-2xl font-bold text-gray-800 mt-1">{orders.length}</p>
                </div>
              </div>
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </div>

          <div className="group bg-gradient-to-br from-white via-purple-50/30 to-white rounded-3xl p-8 shadow-2xl border border-purple-100/50 backdrop-blur-sm transform hover:scale-105 hover:shadow-3xl transition-all duration-500 cursor-pointer">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl shadow-xl group-hover:shadow-2xl transition-all duration-300 group-hover:scale-110">
                  <FiHeart className="w-8 h-8 text-white" />
                  <div className="absolute inset-0 bg-white/20 rounded-2xl"></div>
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide">{branding.content.dashboardFavoritesTitle}</p>
                  <p className="text-2xl font-bold text-gray-800 mt-1">{favorites.length}</p>
                </div>
              </div>
              <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-3xl p-8 shadow-2xl border border-gray-100/50 backdrop-blur-sm">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-xl">
              <FiClock className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Recent Activity</h3>
          </div>
          <div className="space-y-4">
            {appointments.length > 0 ? (
              appointments.slice(0, 2).map((appointment, index) => (
                <div key={appointment.id} className="group flex items-center justify-between p-6 bg-gradient-to-r from-white to-gray-50/50 rounded-2xl shadow-lg border border-gray-100/50 hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl group-hover:shadow-lg transition-all duration-300">
                      <FiCalendar className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-bold text-gray-800 text-lg">{appointment.service?.name || appointment.serviceName || 'Service'}</p>
                      <p className="text-sm text-gray-500 font-medium">
                        {formatDate(appointment.date)} at {appointment.time}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`px-4 py-2 rounded-xl text-sm font-bold shadow-lg ${getStatusColor(appointment.status)}`}>
                      {appointment.status}
                    </span>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <div className="p-4 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <FiCalendar className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-gray-500 text-lg font-medium">No recent appointments</p>
                <p className="text-gray-400 text-sm mt-1">Your activity will appear here</p>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderAppointments = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">My Appointments</h2>
        <button
          onClick={() => onNavigate('consultation')}
          className="text-white px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer"
          style={{ backgroundColor: branding.colors.secondary }}
          onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
          onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
        >
          Book New Appointment
        </button>
      </div>

      <div className="space-y-4">
        {appointments.map((appointment) => (
          <div key={appointment.id} className="bg-white rounded-xl p-6 shadow-sm">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="text-lg font-semibold mb-2">{appointment.service}</h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center">
                    <FiCalendar className="w-4 h-4 mr-2" />
                    {appointment.date}
                  </div>
                  <div className="flex items-center">
                    <FiClock className="w-4 h-4 mr-2" />
                    {appointment.time}
                  </div>
                  <div className="flex items-center">
                    <FiUser className="w-4 h-4 mr-2" />
                    Stylist: {appointment.stylist}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                  {appointment.status}
                </span>
                <p className="text-lg font-semibold mt-2">${appointment.price}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const renderOrders = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Order History</h2>
        <button
          onClick={() => onNavigate('shop')}
          className="text-white px-4 py-2 rounded-lg transition-colors duration-200 cursor-pointer"
          style={{ backgroundColor: branding.colors.secondary }}
          onMouseEnter={(e) => e.target.style.backgroundColor = branding.colors.accent}
          onMouseLeave={(e) => e.target.style.backgroundColor = branding.colors.secondary}
        >
          Shop Now
        </button>
      </div>

      <div className="space-y-4">
        {orders.map((order) => (
          <div key={order.id} className="bg-white rounded-xl p-6 shadow-sm">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="text-lg font-semibold mb-2">Order #{order.id}</h3>
                <p className="text-sm text-gray-600 mb-2">Ordered on {order.date}</p>
                <div className="space-y-1">
                  {order.items.map((item, index) => (
                    <p key={index} className="text-sm text-gray-700">• {item}</p>
                  ))}
                </div>
              </div>
              <div className="text-right">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                  {order.status}
                </span>
                <p className="text-lg font-semibold mt-2">${order.total}</p>
                <button
                  className="text-sm mt-2 flex items-center transition-colors duration-200"
                  style={{ color: '#008000' }}
                  onMouseEnter={(e) => e.target.style.color = '#006600'}
                  onMouseLeave={(e) => e.target.style.color = '#008000'}
                >
                  <FiEye className="w-4 h-4 mr-1" />
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const renderFavorites = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Favorite Products</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {favorites.map((product) => (
          <div key={product.id} className="bg-white rounded-xl overflow-hidden shadow-sm">
            <img
              src={product.image}
              alt={product.name}
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <h3 className="font-semibold mb-2">{product.name}</h3>
              <div className="flex justify-between items-center">
                <span className="text-lg font-bold" style={{ color: '#008000' }}>${product.price}</span>
                <button
                  className="text-white px-4 py-2 rounded-lg transition-colors duration-200"
                  style={{ backgroundColor: '#008000' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = '#006600'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = '#008000'}
                >
                  Add to Cart
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const renderProfile = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Profile Settings</h2>

      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="flex items-center space-x-6 mb-6">
          <img
            src={userData.avatar}
            alt={userData.name}
            className="w-20 h-20 rounded-full object-cover"
          />
          <div>
            <h3 className="text-xl font-semibold">{userData.name}</h3>
            <p className="text-gray-600">Member since {userData.joinDate}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
            <input
              type="text"
              value={profileData.firstName}
              onChange={(e) => handleProfileChange('firstName', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = branding.colors.secondary
                e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
            <input
              type="text"
              value={profileData.lastName}
              onChange={(e) => handleProfileChange('lastName', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = branding.colors.secondary
                e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <input
              type="email"
              value={profileData.email}
              onChange={(e) => handleProfileChange('email', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = branding.colors.secondary
                e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
            <input
              type="tel"
              value={profileData.phone}
              onChange={(e) => handleProfileChange('phone', e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg transition-colors duration-200"
              onFocus={(e) => {
                e.target.style.outline = 'none'
                e.target.style.borderColor = branding.colors.secondary
                e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db'
                e.target.style.boxShadow = 'none'
              }}
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
            <button
              className="w-full px-4 py-2 border border-gray-300 rounded-lg text-left text-gray-500 hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
              onClick={() => {
                // TODO: Implement password change modal
                window.dispatchEvent(new CustomEvent('show-notification', {
                  detail: {
                    type: 'info',
                    message: 'Password change feature coming soon!'
                  }
                }))
              }}
            >
              Change Password
            </button>
          </div>
        </div>

        <div className="mt-6 flex space-x-4">
          <button
            onClick={handleProfileSave}
            disabled={isUpdating}
            className="text-white px-6 py-2 rounded-lg transition-colors duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ backgroundColor: branding.colors.secondary }}
            onMouseEnter={(e) => !isUpdating && (e.target.style.backgroundColor = branding.colors.accent)}
            onMouseLeave={(e) => !isUpdating && (e.target.style.backgroundColor = branding.colors.secondary)}
          >
            {isUpdating ? 'Saving...' : 'Save Changes'}
          </button>
          <button
            onClick={() => {
              setProfileData({
                firstName: userData?.firstName || '',
                lastName: userData?.lastName || '',
                email: userData?.email || '',
                phone: userData?.phone || ''
              })
            }}
            className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview()
      case 'appointments':
        return renderAppointments()
      case 'orders':
        return renderOrders()
      case 'favorites':
        return renderFavorites()
      case 'profile':
        return renderProfile()
      default:
        return renderOverview()
    }
  }

  // Show loading state
  if (isLoading) {
    return <Loading message="Loading your dashboard..." />
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <FiAlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Dashboard</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      <div className="max-w-7xl mx-auto lg:px-4">
        {/* Mobile Header - Only show on mobile */}
        <div className="lg:hidden bg-white shadow-sm px-4 py-3 flex items-center justify-between sticky top-0 z-30">
          <h1 className="text-lg font-semibold">Dashboard</h1>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
          >
            <FiMenu className="w-6 h-6" />
          </button>
        </div>

        <div className="flex flex-col lg:flex-row">
          {/* Mobile Sidebar Overlay */}
          {isMobileMenuOpen && (
            <div
              className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setIsMobileMenuOpen(false)}
            />
          )}

          {/* Desktop Sidebar - Always visible on lg+ */}
          <div className="hidden lg:block lg:w-80 xl:w-96 lg:flex-shrink-0">
            <div className="bg-gradient-to-br from-white via-gray-50 to-white rounded-3xl shadow-2xl border border-gray-100/50 backdrop-blur-sm p-8 mt-8 mx-6 transform hover:scale-[1.02] transition-all duration-500 ease-out">
              {/* Glassmorphism overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-transparent to-white/40 rounded-3xl"></div>

              <div className="relative z-10">
                {/* User Profile Section */}
                <div className="flex items-center space-x-4 mb-8 p-4 bg-gradient-to-r from-purple-50 via-blue-50 to-indigo-50 rounded-2xl border border-purple-100/50 shadow-lg backdrop-blur-sm">
                  <div className="relative">
                    <img
                      src={userData?.avatar || userData?.profilePicture || FALLBACK_IMAGES.profile}
                      alt={userData?.firstName || userData?.name || 'User'}
                      className="w-16 h-16 rounded-2xl object-cover shadow-xl border-2 border-white/80"
                      onError={(e) => {
                        e.target.src = FALLBACK_IMAGES.profile
                      }}
                    />
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-400 rounded-full border-2 border-white shadow-lg"></div>
                  </div>
                  <div>
                    <h3 className="font-bold text-lg bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                      {userData?.firstName ? `${userData.firstName} ${userData.lastName || ''}` : userData?.name || 'User'}
                    </h3>
                    <p className="text-sm text-gray-500 font-medium">Premium Customer</p>
                    <div className="flex items-center mt-1">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                      <span className="text-xs text-green-600 font-semibold">Online</span>
                    </div>
                  </div>
                </div>

                {/* Navigation Menu */}
                <nav className="space-y-3">
                  {tabs.map((tab, index) => {
                    const Icon = tab.icon
                    const isActive = activeTab === tab.id
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`group w-full flex items-center space-x-4 px-6 py-4 rounded-2xl text-left transition-all duration-300 cursor-pointer transform hover:scale-105 hover:shadow-xl ${
                          isActive
                            ? 'text-white shadow-2xl border border-white/20'
                            : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-white hover:shadow-lg border border-transparent'
                        }`}
                        style={{
                          background: isActive
                            ? `linear-gradient(135deg, ${branding.colors.secondary}, ${branding.colors.accent})`
                            : 'transparent',
                          animationDelay: `${index * 100}ms`
                        }}
                      >
                        <div className={`p-2 rounded-xl transition-all duration-300 ${
                          isActive
                            ? 'bg-white/20 shadow-lg'
                            : 'bg-gray-100 group-hover:bg-white group-hover:shadow-md'
                        }`}>
                          <Icon className={`w-5 h-5 transition-all duration-300 ${
                            isActive ? 'text-white' : 'text-gray-600 group-hover:text-gray-800'
                          }`} />
                        </div>
                        <span className={`font-semibold transition-all duration-300 ${
                          isActive ? 'text-white' : 'text-gray-700 group-hover:text-gray-900'
                        }`}>
                          {tab.name}
                        </span>
                        {isActive && (
                          <div className="ml-auto w-2 h-2 bg-white rounded-full animate-pulse"></div>
                        )}
                      </button>
                    )
                  })}
                </nav>

                {/* Logout Section */}
                <div className="mt-8 pt-6 border-t border-gray-200/50">
                  <button
                    onClick={onLogout}
                    className="group w-full flex items-center space-x-4 px-6 py-4 text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 rounded-2xl transition-all duration-300 cursor-pointer transform hover:scale-105 hover:shadow-xl border border-transparent hover:border-red-100"
                  >
                    <div className="p-2 bg-red-100 group-hover:bg-red-200 rounded-xl transition-all duration-300 group-hover:shadow-md">
                      <FiLogOut className="w-5 h-5 text-red-600" />
                    </div>
                    <span className="font-semibold">Logout</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Menu Popup - Center modal on mobile */}
          {isMobileMenuOpen && (
            <div className="lg:hidden fixed inset-0 z-50 flex items-center justify-center p-4">
              {/* Modal backdrop */}
              <div
                className="absolute inset-0 bg-white bg-opacity-20 backdrop-blur-md"
                onClick={() => setIsMobileMenuOpen(false)}
              />

              {/* Modal content */}
              <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-sm mx-auto transform transition-all duration-300 ease-out">
                {/* Header with close button */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <div className="flex items-center space-x-3">
                    <img
                      src={userData?.avatar || userData?.profilePicture || FALLBACK_IMAGES.profile}
                      alt={userData?.firstName || userData?.name || 'User'}
                      className="w-10 h-10 rounded-full object-cover"
                      onError={(e) => {
                        e.target.src = FALLBACK_IMAGES.profile
                      }}
                    />
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {userData?.firstName ? `${userData.firstName} ${userData.lastName || ''}` : userData?.name || 'User'}
                      </h3>
                      <p className="text-sm text-gray-500">Customer</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
                  >
                    <FiX className="w-5 h-5 text-gray-500" />
                  </button>
                </div>

                {/* Navigation menu */}
                <div className="p-6">
                  <nav className="space-y-2">
                    {tabs.map((tab) => {
                      const Icon = tab.icon
                      return (
                        <button
                          key={tab.id}
                          onClick={() => {
                            setActiveTab(tab.id)
                            setIsMobileMenuOpen(false) // Close mobile menu on selection
                          }}
                          className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 cursor-pointer ${
                            activeTab === tab.id
                              ? 'text-white shadow-lg transform scale-105'
                              : 'text-gray-700 hover:bg-gray-50 hover:transform hover:scale-105'
                          }`}
                          style={activeTab === tab.id ? { backgroundColor: branding.colors.secondary } : {}}
                        >
                          <Icon className="w-5 h-5" />
                          <span className="font-medium">{tab.name}</span>
                        </button>
                      )
                    })}
                  </nav>

                  {/* Logout button */}
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <button
                      onClick={() => {
                        onLogout()
                        setIsMobileMenuOpen(false)
                      }}
                      className="w-full flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200 cursor-pointer hover:transform hover:scale-105"
                    >
                      <FiLogOut className="w-5 h-5" />
                      <span className="font-medium">Logout</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}


          {/* Main Content */}
          <div className="flex-1 p-4 lg:p-8 lg:pl-4">
            <div className="max-w-7xl mx-auto">
              {renderContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserDashboard
