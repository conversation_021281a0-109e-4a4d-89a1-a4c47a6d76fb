import { sendEmail } from './email';
import { SMSService } from './sms';
import { Notification, NotificationTemplate } from '../models';

export interface NotificationData {
  userId: string;
  type: 'appointment' | 'order' | 'general' | 'promotion';
  title: string;
  message: string;
  channels: ('email' | 'sms' | 'push' | 'in-app')[];
  templateVariables?: Record<string, string>;
  metadata?: Record<string, any>;
}

export interface NotificationResult {
  success: boolean;
  results: {
    email?: { success: boolean; error?: string };
    sms?: { success: boolean; messageId?: string; error?: string };
    push?: { success: boolean; error?: string };
    inApp?: { success: boolean; notificationId?: string; error?: string };
  };
}

export class NotificationService {
  static async sendNotification(data: NotificationData): Promise<NotificationResult> {
    const results: NotificationResult['results'] = {};
    let overallSuccess = true;

    // Send in-app notification first (always create for record keeping)
    if (data.channels.includes('in-app')) {
      try {
        const notification = await Notification.create({
          user: data.userId,
          title: data.title,
          message: data.message,
          type: data.type,
          metadata: data.metadata
        });

        results.inApp = { 
          success: true, 
          notificationId: notification._id.toString() 
        };
      } catch (error) {
        results.inApp = { 
          success: false, 
          error: (error as Error).message 
        };
        overallSuccess = false;
      }
    }

    // Send email notification
    if (data.channels.includes('email')) {
      try {
        const emailResult = await this.sendEmailNotification(data);
        results.email = emailResult;
        if (!emailResult.success) overallSuccess = false;
      } catch (error) {
        results.email = { 
          success: false, 
          error: (error as Error).message 
        };
        overallSuccess = false;
      }
    }

    // Send SMS notification
    if (data.channels.includes('sms')) {
      try {
        const smsResult = await this.sendSMSNotification(data);
        results.sms = smsResult;
        if (!smsResult.success) overallSuccess = false;
      } catch (error) {
        results.sms = { 
          success: false, 
          error: (error as Error).message 
        };
        overallSuccess = false;
      }
    }

    // Send push notification
    if (data.channels.includes('push')) {
      try {
        const pushResult = await this.sendPushNotification(data);
        results.push = pushResult;
        if (!pushResult.success) overallSuccess = false;
      } catch (error) {
        results.push = { 
          success: false, 
          error: (error as Error).message 
        };
        overallSuccess = false;
      }
    }

    return { success: overallSuccess, results };
  }

  private static async sendEmailNotification(data: NotificationData): Promise<{ success: boolean; error?: string }> {
    try {
      // Get user email
      const { User } = await import('../models');
      const user = await User.findById(data.userId).select('email name');
      
      if (!user || !user.email) {
        return { success: false, error: 'User email not found' };
      }

      // Try to get email template
      let emailContent = {
        subject: data.title,
        html: `<p>${data.message}</p>`,
        text: data.message
      };

      try {
        const template = await NotificationTemplate.findOne({
          type: data.type,
          channel: 'email',
          isActive: true
        });

        if (template) {
          emailContent.subject = this.replaceVariables(template.subject || data.title, data.templateVariables);
          emailContent.html = this.replaceVariables(template.content, data.templateVariables);
          emailContent.text = this.stripHtml(emailContent.html);
        }
      } catch (templateError) {
        console.warn('Failed to load email template, using default content:', templateError);
      }

      await sendEmail({
        to: user.email,
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  private static async sendSMSNotification(data: NotificationData): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Get user phone
      const { User } = await import('../models');
      const user = await User.findById(data.userId).select('phone notificationPreferences');
      
      if (!user || !user.phone) {
        return { success: false, error: 'User phone not found' };
      }

      if (!user.notificationPreferences?.sms) {
        return { success: false, error: 'User has disabled SMS notifications' };
      }

      // Try to get SMS template
      let smsMessage = data.message;

      try {
        const template = await NotificationTemplate.findOne({
          type: data.type,
          channel: 'sms',
          isActive: true
        });

        if (template) {
          smsMessage = this.replaceVariables(template.content, data.templateVariables);
        }
      } catch (templateError) {
        console.warn('Failed to load SMS template, using default content:', templateError);
      }

      const result = await SMSService.sendSMS({
        to: user.phone,
        message: smsMessage
      });

      return result;
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  private static async sendPushNotification(data: NotificationData): Promise<{ success: boolean; error?: string }> {
    try {
      // Get user push tokens
      const { User } = await import('../models');
      const user = await User.findById(data.userId).select('pushTokens notificationPreferences');
      
      if (!user || !user.notificationPreferences?.push) {
        return { success: false, error: 'User has disabled push notifications' };
      }

      // In production, implement push notification service (Firebase, OneSignal, etc.)
      // Example Firebase implementation:
      // const admin = require('firebase-admin');
      // const message = {
      //   notification: {
      //     title: data.title,
      //     body: data.message
      //   },
      //   data: data.metadata || {},
      //   tokens: user.pushTokens || []
      // };
      // const response = await admin.messaging().sendMulticast(message);

      console.log(`Push notification would be sent to user ${data.userId}: ${data.title}`);
      return { success: true };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  private static replaceVariables(content: string, variables?: Record<string, string>): string {
    if (!variables) return content;

    let result = content;
    Object.keys(variables).forEach(key => {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), variables[key]);
    });

    return result;
  }

  private static stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }

  // Convenience methods for common notification types
  static async sendAppointmentConfirmation(
    userId: string,
    appointmentDetails: { serviceName: string; date: string; time: string }
  ): Promise<NotificationResult> {
    return this.sendNotification({
      userId,
      type: 'appointment',
      title: 'Appointment Confirmed',
      message: `Your appointment for ${appointmentDetails.serviceName} on ${appointmentDetails.date} at ${appointmentDetails.time} has been confirmed.`,
      channels: ['email', 'in-app'],
      templateVariables: appointmentDetails
    });
  }

  static async sendOrderUpdate(
    userId: string,
    orderDetails: { orderNumber: string; status: string; trackingNumber?: string }
  ): Promise<NotificationResult> {
    return this.sendNotification({
      userId,
      type: 'order',
      title: 'Order Update',
      message: `Your order #${orderDetails.orderNumber} is now ${orderDetails.status}.`,
      channels: ['email', 'sms', 'in-app'],
      templateVariables: orderDetails
    });
  }

  static async sendWelcomeMessage(
    userId: string,
    userDetails: { name: string }
  ): Promise<NotificationResult> {
    return this.sendNotification({
      userId,
      type: 'general',
      title: 'Welcome to MicroLocs!',
      message: `Welcome ${userDetails.name}! Thank you for joining MicroLocs. We're excited to help you with your hair care journey.`,
      channels: ['email', 'in-app'],
      templateVariables: userDetails
    });
  }

  static async sendPromotionalMessage(
    userId: string,
    promotionDetails: { title: string; description: string; code?: string }
  ): Promise<NotificationResult> {
    return this.sendNotification({
      userId,
      type: 'promotion',
      title: promotionDetails.title,
      message: promotionDetails.description,
      channels: ['email', 'in-app'],
      templateVariables: promotionDetails
    });
  }
}
