import { useEffect } from 'react'

const SEO = ({
  title = '<PERSON><PERSON> Locs By Tina - Professional Locs & Natural Hair Care',
  description = 'Transform your hair with professional locs installation and maintenance. Specializing in micro locs, traditional locs, and natural hair care in Atlanta, GA.',
  keywords = 'locs, micro locs, natural hair, hair care, Atlanta, dreadlocks, loc maintenance, loc installation',
  image = 'https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=1200',
  url = window.location.href,
  type = 'website'
}) => {
  useEffect(() => {
    // Update document title
    document.title = title

    // Update or create meta tags
    const updateMetaTag = (name, content, property = false) => {
      const attribute = property ? 'property' : 'name'
      let element = document.querySelector(`meta[${attribute}="${name}"]`)
      
      if (element) {
        element.setAttribute('content', content)
      } else {
        element = document.createElement('meta')
        element.setAttribute(attribute, name)
        element.setAttribute('content', content)
        document.head.appendChild(element)
      }
    }

    // Basic meta tags
    updateMetaTag('description', description)
    updateMetaTag('keywords', keywords)
    updateMetaTag('author', 'Goldie Locs By Tina')
    updateMetaTag('robots', 'index, follow')
    updateMetaTag('viewport', 'width=device-width, initial-scale=1.0')

    // Open Graph tags
    updateMetaTag('og:title', title, true)
    updateMetaTag('og:description', description, true)
    updateMetaTag('og:image', image, true)
    updateMetaTag('og:url', url, true)
    updateMetaTag('og:type', type, true)
    updateMetaTag('og:site_name', 'Goldie Locs By Tina', true)

    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image')
    updateMetaTag('twitter:title', title)
    updateMetaTag('twitter:description', description)
    updateMetaTag('twitter:image', image)

    // Business-specific meta tags
    updateMetaTag('geo.region', 'US-GA')
    updateMetaTag('geo.placename', 'Atlanta')
    updateMetaTag('geo.position', '33.7490;-84.3880')
    updateMetaTag('ICBM', '33.7490, -84.3880')

    // Theme color for mobile browsers
    updateMetaTag('theme-color', '#f3d016')
    updateMetaTag('msapplication-TileColor', '#f3d016')

    // Canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]')
    if (canonicalLink) {
      canonicalLink.setAttribute('href', url)
    } else {
      canonicalLink = document.createElement('link')
      canonicalLink.setAttribute('rel', 'canonical')
      canonicalLink.setAttribute('href', url)
      document.head.appendChild(canonicalLink)
    }

    // JSON-LD structured data for local business
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "HairSalon",
      "name": "Goldie Locs By Tina",
      "description": description,
      "url": url,
      "image": image,
      "telephone": "(*************",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "123 Beauty Street",
        "addressLocality": "Atlanta",
        "addressRegion": "GA",
        "postalCode": "30309",
        "addressCountry": "US"
      },
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "33.7490",
        "longitude": "-84.3880"
      },
      "openingHours": [
        "Mo-Fr 09:00-18:00",
        "Sa 08:00-16:00"
      ],
      "priceRange": "$$",
      "servedCuisine": [],
      "serviceArea": {
        "@type": "City",
        "name": "Atlanta"
      },
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Hair Services",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Micro Locs Installation",
              "description": "Professional micro locs installation"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Loc Maintenance",
              "description": "Regular maintenance for healthy locs"
            }
          }
        ]
      }
    }

    // Add or update structured data
    let structuredDataScript = document.querySelector('script[type="application/ld+json"]')
    if (structuredDataScript) {
      structuredDataScript.textContent = JSON.stringify(structuredData)
    } else {
      structuredDataScript = document.createElement('script')
      structuredDataScript.type = 'application/ld+json'
      structuredDataScript.textContent = JSON.stringify(structuredData)
      document.head.appendChild(structuredDataScript)
    }

  }, [title, description, keywords, image, url, type])

  return null // This component doesn't render anything
}

export default SEO
