
import { FiInstagram, FiFacebook, FiTwitter, FiMail, FiPhone, FiMapPin } from 'react-icons/fi'

const Footer = ({ onNavigate }) => {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="text-2xl font-bold" style={{ color: '#f3d016' }}>
                Goldie Locs
              </div>
              <div className="text-sm text-gray-300">
                By Tina
              </div>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              Professional locs and natural hair care services. Specializing in micro locs,
              traditional locs, and natural hair maintenance with over 10 years of experience.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-amber-400 transition-colors duration-200">
                <FiInstagram className="w-6 h-6" />
              </a>
              <a href="#" className="text-gray-300 hover:text-amber-400 transition-colors duration-200">
                <FiFacebook className="w-6 h-6" />
              </a>
              <a href="#" className="text-gray-300 hover:text-amber-400 transition-colors duration-200">
                <FiTwitter className="w-6 h-6" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4" style={{ color: '#FFFF00' }}>Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <button
                  onClick={() => onNavigate('home')}
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Home
                </button>
              </li>
              <li>
                <button
                  onClick={() => onNavigate('services')}
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Services
                </button>
              </li>
              <li>
                <button
                  onClick={() => onNavigate('consultation')}
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Book Consultation
                </button>
              </li>
              <li>
                <button
                  onClick={() => onNavigate('shop')}
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  Shop
                </button>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4" style={{ color: '#FFFF00' }}>Contact</h3>
            <ul className="space-y-3">
              <li className="flex items-center space-x-2">
                <FiPhone className="w-4 h-4" style={{ color: '#FFFF00' }} />
                <span className="text-gray-300">(*************</span>
              </li>
              <li className="flex items-center space-x-2">
                <FiMail className="w-4 h-4" style={{ color: '#FFFF00' }} />
                <span className="text-gray-300"><EMAIL></span>
              </li>
              <li className="flex items-start space-x-2">
                <FiMapPin className="w-4 h-4 mt-1" style={{ color: '#FFFF00' }} />
                <span className="text-gray-300">
                  123 Beauty Street<br />
                  Atlanta, GA 30309
                </span>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 Goldie Locs By Tina. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <button className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                Privacy Policy
              </button>
              <button className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                Terms of Service
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
