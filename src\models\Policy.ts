import mongoose, { Schema, Document } from 'mongoose';

export interface IPolicy extends Document {
  _id: string;
  type: 'cancellation' | 'refund' | 'privacy' | 'terms';
  title: string;
  content: string;
  rules: Array<{
    condition: string;
    action: string;
    timeframe: string;
    fee?: number;
  }>;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const policySchema = new Schema<IPolicy>({
  type: {
    type: String,
    required: true,
    enum: ['cancellation', 'refund', 'privacy', 'terms'],
    unique: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  content: {
    type: String,
    required: true
  },
  rules: [{
    condition: {
      type: String,
      required: true,
      trim: true
    },
    action: {
      type: String,
      required: true,
      trim: true
    },
    timeframe: {
      type: String,
      required: true,
      trim: true
    },
    fee: {
      type: Number,
      min: 0,
      default: 0
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better query performance
policySchema.index({ type: 1 });
policySchema.index({ isActive: 1 });

export const Policy = mongoose.model<IPolicy>('Policy', policySchema);
