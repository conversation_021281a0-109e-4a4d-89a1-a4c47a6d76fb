import apiService from './api.js'

/**
 * Notification service for handling user notifications
 */
class NotificationService {
  /**
   * Get user notifications
   */
  async getNotifications(params = {}) {
    try {
      const response = await apiService.get('/notifications', params)
      return response
    } catch (error) {
      console.error('Get notifications error:', error)
      throw error
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId) {
    try {
      const response = await apiService.put(`/notifications/${notificationId}/read`)
      return response
    } catch (error) {
      console.error('Mark notification as read error:', error)
      throw error
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead() {
    try {
      const response = await apiService.put('/notifications/read-all')
      return response
    } catch (error) {
      console.error('Mark all notifications as read error:', error)
      throw error
    }
  }

  /**
   * Delete notification
   */
  async deleteNotification(notificationId) {
    try {
      const response = await apiService.delete(`/notifications/${notificationId}`)
      return response
    } catch (error) {
      console.error('Delete notification error:', error)
      throw error
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount() {
    try {
      const response = await apiService.get('/notifications/unread-count')
      return response
    } catch (error) {
      console.error('Get unread count error:', error)
      throw error
    }
  }

  /**
   * Subscribe to push notifications
   */
  async subscribeToPush(subscription) {
    try {
      const response = await apiService.post('/notifications/push/subscribe', subscription)
      return response
    } catch (error) {
      console.error('Subscribe to push notifications error:', error)
      throw error
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribeFromPush(endpoint) {
    try {
      const response = await apiService.post('/notifications/push/unsubscribe', {
        endpoint
      })
      return response
    } catch (error) {
      console.error('Unsubscribe from push notifications error:', error)
      throw error
    }
  }

  /**
   * Test notification (for development)
   */
  async testNotification(type = 'info', message = 'Test notification') {
    try {
      const response = await apiService.post('/notifications/test', {
        type,
        message
      })
      return response
    } catch (error) {
      console.error('Test notification error:', error)
      throw error
    }
  }

  // Real-time notification handling
  
  /**
   * Initialize WebSocket connection for real-time notifications
   */
  initializeRealTime(onNotification) {
    try {
      const token = apiService.getAuthToken()
      if (!token) {
        console.warn('No auth token available for WebSocket connection')
        return null
      }

      const wsUrl = apiService.baseURL.replace('http', 'ws').replace('/api', '/ws')
      const ws = new WebSocket(`${wsUrl}?token=${token}`)

      ws.onopen = () => {
        console.log('WebSocket connection established for notifications')
      }

      ws.onmessage = (event) => {
        try {
          const notification = JSON.parse(event.data)
          if (onNotification && typeof onNotification === 'function') {
            onNotification(notification)
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      ws.onclose = () => {
        console.log('WebSocket connection closed')
      }

      ws.onerror = (error) => {
        console.error('WebSocket error:', error)
      }

      return ws
    } catch (error) {
      console.error('Initialize real-time notifications error:', error)
      return null
    }
  }

  // Local notification management

  /**
   * Show browser notification (requires permission)
   */
  async showBrowserNotification(title, options = {}) {
    try {
      if (!('Notification' in window)) {
        console.warn('This browser does not support notifications')
        return false
      }

      if (Notification.permission === 'granted') {
        new Notification(title, {
          icon: '/favicon.ico',
          badge: '/favicon.ico',
          ...options
        })
        return true
      } else if (Notification.permission !== 'denied') {
        const permission = await Notification.requestPermission()
        if (permission === 'granted') {
          new Notification(title, {
            icon: '/favicon.ico',
            badge: '/favicon.ico',
            ...options
          })
          return true
        }
      }

      return false
    } catch (error) {
      console.error('Show browser notification error:', error)
      return false
    }
  }

  /**
   * Request notification permission
   */
  async requestPermission() {
    try {
      if (!('Notification' in window)) {
        return 'not-supported'
      }

      if (Notification.permission === 'granted') {
        return 'granted'
      }

      if (Notification.permission === 'denied') {
        return 'denied'
      }

      const permission = await Notification.requestPermission()
      return permission
    } catch (error) {
      console.error('Request notification permission error:', error)
      return 'error'
    }
  }
}

// Create and export singleton instance
const notificationService = new NotificationService()
export default notificationService
