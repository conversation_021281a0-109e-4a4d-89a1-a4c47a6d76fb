import { useState, useEffect } from 'react'
import { FiMail, FiLock, FiEye, FiEyeOff, FiAlertCircle, FiCheckCircle } from 'react-icons/fi'
import { authService } from '../../services'
import { useBranding } from '../../contexts/BrandingContext'

const Login = ({ onNavigate, onLogin }) => {
  const { branding } = useBranding()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const [error, setError] = useState('')
  const [successMessage, setSuccessMessage] = useState('')

  // Check for signup success message
  useEffect(() => {
    const signupSuccess = sessionStorage.getItem('signupSuccess')
    if (signupSuccess) {
      setSuccessMessage(signupSuccess)
      sessionStorage.removeItem('signupSuccess')
    }
  }, [])

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    // Clear messages when user starts typing
    if (error) setError('')
    if (successMessage) setSuccessMessage('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const credentials = {
        email: formData.email,
        password: formData.password,
        rememberMe
      }

      const response = await authService.login(credentials)

      if (response.success) {
        // Extract user from nested data object
        const user = response.data ? response.data.user : response.user
        const userType = user?.role === 'admin' ? 'admin' : 'user'
        onLogin(userType, rememberMe)

        // Navigate to appropriate dashboard
        if (userType === 'admin') {
          onNavigate('admin-dashboard')
        } else {
          onNavigate('user-dashboard')
        }
      } else {
        setError(response.message || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      setError(error.message || 'Login failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{branding.content.loginTitle}</h1>
          <p className="text-gray-600">{branding.content.loginSubtitle}</p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-lg sm:rounded-2xl sm:px-10">
          {successMessage && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center">
              <FiCheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
              <p className="text-sm text-green-700">{successMessage}</p>
            </div>
          )}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
              <FiAlertCircle className="w-5 h-5 text-red-500 mr-3 flex-shrink-0" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <div className="relative">
                <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.borderColor = branding.colors.secondary
                    e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db'
                    e.target.style.boxShadow = 'none'
                  }}
                  placeholder="Enter your email"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <FiLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg transition-colors duration-200"
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.borderColor = branding.colors.secondary
                    e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db'
                    e.target.style.boxShadow = 'none'
                  }}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  {showPassword ? <FiEyeOff className="w-5 h-5" /> : <FiEye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="h-4 w-4 border-gray-300 rounded focus:ring-2"
                  style={{ accentColor: branding.colors.secondary }}
                  onFocus={(e) => {
                    e.target.style.outline = 'none'
                    e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                  }}
                  onBlur={(e) => {
                    e.target.style.boxShadow = 'none'
                  }}
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <button
                  onClick={() => onNavigate('forgot-password')}
                  className="transition-colors duration-200"
                  style={{ color: branding.colors.secondary }}
                  onMouseEnter={(e) => e.target.style.color = branding.colors.accent}
                  onMouseLeave={(e) => e.target.style.color = branding.colors.secondary}
                >
                  {branding.content.forgotPasswordLink}
                </button>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ backgroundColor: branding.colors.secondary }}
                onMouseEnter={(e) => !isLoading && (e.target.style.backgroundColor = branding.colors.accent)}
                onMouseLeave={(e) => !isLoading && (e.target.style.backgroundColor = branding.colors.secondary)}
                onFocus={(e) => {
                  e.target.style.outline = 'none'
                  e.target.style.boxShadow = `0 0 0 2px ${branding.colors.secondary}20`
                }}
                onBlur={(e) => {
                  e.target.style.boxShadow = 'none'
                }}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {branding.content.signingInText}
                  </div>
                ) : (
                  branding.content.signInButton
                )}
              </button>
            </div>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              {branding.content.noAccountText}{' '}
              <button
                onClick={() => onNavigate('signup')}
                className="font-medium transition-colors duration-200"
                style={{ color: branding.colors.secondary }}
                onMouseEnter={(e) => e.target.style.color = branding.colors.accent}
                onMouseLeave={(e) => e.target.style.color = branding.colors.secondary}
              >
                {branding.content.signUpLink}
              </button>
            </p>
          </div>
        </div>
      </div>

      {/* Additional Links */}
      <div className="mt-8 text-center">
        <div className="flex justify-center space-x-6 text-sm text-gray-600">
          <button
            className="transition-colors duration-200"
            onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
            onMouseLeave={(e) => e.target.style.color = ''}
          >
            {branding.content.privacyPolicyLink}
          </button>
          <button
            className="transition-colors duration-200"
            onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
            onMouseLeave={(e) => e.target.style.color = ''}
          >
            {branding.content.termsOfServiceLink}
          </button>
          <button
            className="transition-colors duration-200"
            onMouseEnter={(e) => e.target.style.color = branding.colors.secondary}
            onMouseLeave={(e) => e.target.style.color = ''}
          >
            {branding.content.helpCenterLink}
          </button>
        </div>
      </div>
    </div>
  )
}

export default Login
