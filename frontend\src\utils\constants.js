// Brand colors
export const COLORS = {
  primary: '#f3d016',
  primaryHover: '#d4b014',
  secondary: '#FFFF00',
  secondaryHover: '#E6E600',
  accent: '#F0FFF0',
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827'
  }
}

// Common button styles
export const BUTTON_STYLES = {
  primary: `
    inline-flex items-center justify-center px-6 py-3 text-base font-medium text-white 
    rounded-lg transition-all duration-200 
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500
    hover:shadow-lg transform hover:-translate-y-0.5
  `,
  secondary: `
    inline-flex items-center justify-center px-6 py-3 text-base font-medium 
    bg-white rounded-lg transition-all duration-200 border-2
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500
    hover:shadow-lg transform hover:-translate-y-0.5
  `,
  ghost: `
    inline-flex items-center justify-center px-6 py-3 text-base font-medium 
    bg-transparent rounded-lg transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500
    hover:bg-gray-100
  `
}

// Common spacing
export const SPACING = {
  section: 'py-20',
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  grid: {
    cols1: 'grid grid-cols-1',
    cols2: 'grid grid-cols-1 md:grid-cols-2',
    cols3: 'grid grid-cols-1 md:grid-cols-3',
    cols4: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
  }
}

// Animation classes
export const ANIMATIONS = {
  fadeIn: 'animate-fade-in',
  slideUp: 'animate-slide-up',
  bounce: 'animate-bounce',
  pulse: 'animate-pulse',
  spin: 'animate-spin'
}

// Common shadows
export const SHADOWS = {
  sm: 'shadow-sm',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl',
  card: 'shadow-lg hover:shadow-xl transition-shadow duration-200'
}

// Responsive breakpoints (for reference)
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
}

// Common form styles
export const FORM_STYLES = {
  input: `
    w-full px-4 py-3 border border-gray-300 rounded-lg 
    transition-colors duration-200
    focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200
    placeholder-gray-400
  `,
  textarea: `
    w-full px-4 py-3 border border-gray-300 rounded-lg 
    transition-colors duration-200 resize-vertical
    focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200
    placeholder-gray-400
  `,
  select: `
    w-full px-4 py-3 border border-gray-300 rounded-lg 
    transition-colors duration-200 bg-white
    focus:outline-none focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200
  `,
  label: 'block text-sm font-medium text-gray-700 mb-2',
  error: 'text-sm text-red-600 mt-1',
  helper: 'text-sm text-gray-500 mt-1'
}

// Common card styles
export const CARD_STYLES = {
  base: 'bg-white rounded-2xl overflow-hidden',
  shadow: 'bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-200',
  bordered: 'bg-white rounded-2xl overflow-hidden border border-gray-200',
  elevated: 'bg-white rounded-2xl overflow-hidden shadow-xl'
}

// Image fallbacks
export const FALLBACK_IMAGES = {
  hero: 'https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600',
  service: 'https://images.pexels.com/photos/3993449/pexels-photo-3993449.jpeg?auto=compress&cs=tinysrgb&w=600',
  product: 'https://via.placeholder.com/400x400/f3d016/000000?text=Product+Image',
  avatar: 'https://via.placeholder.com/150x150/f3d016/000000?text=Avatar'
}

// SEO defaults
export const SEO_DEFAULTS = {
  siteName: 'Goldie Locs By Tina',
  description: 'Transform your hair with professional locs installation and maintenance. Specializing in micro locs, traditional locs, and natural hair care in Atlanta, GA.',
  keywords: 'locs, micro locs, natural hair, hair care, Atlanta, dreadlocks, loc maintenance, loc installation',
  image: FALLBACK_IMAGES.hero,
  type: 'website',
  locale: 'en_US'
}

// Business information
export const BUSINESS_INFO = {
  name: 'Goldie Locs By Tina',
  tagline: 'By Tina',
  phone: '(*************',
  email: '<EMAIL>',
  address: {
    street: '123 Beauty Street',
    city: 'Atlanta',
    state: 'GA',
    zip: '30309',
    full: '123 Beauty Street, Atlanta, GA 30309'
  },
  social: {
    instagram: '@goldielocsbytina',
    facebook: 'GoldieLocsByTina',
    twitter: '@goldielocs',
    youtube: 'GoldieLocsTV'
  },
  hours: {
    monday: '9:00 AM - 6:00 PM',
    tuesday: '9:00 AM - 6:00 PM',
    wednesday: '9:00 AM - 6:00 PM',
    thursday: '9:00 AM - 6:00 PM',
    friday: '9:00 AM - 6:00 PM',
    saturday: '8:00 AM - 4:00 PM',
    sunday: 'Closed'
  }
}
