import { Request, Response } from 'express';
import { Content, FAQ } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';

export class ContentController {
  static async getLegalContent(req: Request, res: Response): Promise<void> {
    try {
      const { type } = req.params;

      if (!['terms', 'privacy'].includes(type)) {
        sendError(res, 'Invalid content type');
        return;
      }

      let content = await Content.findOne({ type });
      
      if (!content) {
        // Create comprehensive default content
        const defaultContent = type === 'terms'
          ? `
            <h1>Terms of Service</h1>
            <p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>

            <h2>1. Acceptance of Terms</h2>
            <p>By accessing and using MicroLocs services, you accept and agree to be bound by the terms and provision of this agreement.</p>

            <h2>2. Services</h2>
            <p>MicroLocs provides professional hair care services including microloc installation, maintenance, and related products.</p>

            <h2>3. Appointments</h2>
            <p>Appointments must be booked through our platform. Cancellations must be made at least 24 hours in advance to avoid fees.</p>

            <h2>4. Payment</h2>
            <p>Payment is due at the time of service. We accept cash, credit cards, and digital payments.</p>

            <h2>5. Liability</h2>
            <p>MicroLocs is not liable for any damages resulting from the use of our services beyond the cost of the service provided.</p>

            <h2>6. Contact Information</h2>
            <p>For questions about these Terms of Service, please contact <NAME_EMAIL></p>
          `
          : `
            <h1>Privacy Policy</h1>
            <p><strong>Last updated:</strong> ${new Date().toLocaleDateString()}</p>

            <h2>1. Information We Collect</h2>
            <p>We collect information you provide directly to us, such as when you create an account, book appointments, or contact us.</p>

            <h2>2. How We Use Your Information</h2>
            <p>We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.</p>

            <h2>3. Information Sharing</h2>
            <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>

            <h2>4. Data Security</h2>
            <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>

            <h2>5. Your Rights</h2>
            <p>You have the right to access, update, or delete your personal information. Contact <NAME_EMAIL> to exercise these rights.</p>

            <h2>6. Contact Us</h2>
            <p>If you have questions about this Privacy Policy, please contact <NAME_EMAIL></p>
          `;

        content = await Content.create({
          type,
          content: defaultContent
        });
      }

      sendSuccess(res, `${type} content retrieved successfully`, content);
    } catch (error) {
      console.error('Get legal content error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateLegalContent(req: Request, res: Response): Promise<void> {
    try {
      const { type } = req.params;
      const { content } = req.body;

      if (!['terms', 'privacy'].includes(type)) {
        sendError(res, 'Invalid content type');
        return;
      }

      let existingContent = await Content.findOne({ type });
      
      if (!existingContent) {
        existingContent = await Content.create({ type, content });
      } else {
        existingContent.content = content;
        await existingContent.save();
      }

      sendSuccess(res, `${type} content updated successfully`, existingContent);
    } catch (error) {
      console.error('Update legal content error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getFAQContent(req: Request, res: Response): Promise<void> {
    try {
      const { category } = req.query;
      const filter: any = { isActive: true };

      if (category) {
        filter.category = category;
      }

      const faqs = await FAQ.find(filter).sort({ category: 1, order: 1 });

      // Group FAQs by category
      const groupedFAQs = faqs.reduce((acc: any, faq) => {
        if (!acc[faq.category]) {
          acc[faq.category] = [];
        }
        acc[faq.category].push(faq);
        return acc;
      }, {});

      sendSuccess(res, 'FAQ content retrieved successfully', {
        faqs,
        groupedFAQs
      });
    } catch (error) {
      console.error('Get FAQ content error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateFAQContent(req: Request, res: Response): Promise<void> {
    try {
      const faqData = req.body;

      // Delete existing FAQs
      await FAQ.deleteMany({});

      // Create new FAQs
      const faqs = await FAQ.insertMany(faqData.map((faq: any, index: number) => ({
        ...faq,
        order: index
      })));

      sendSuccess(res, 'FAQ content updated successfully', faqs);
    } catch (error) {
      console.error('Update FAQ content error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createFAQ(req: Request, res: Response): Promise<void> {
    try {
      const faqData = req.body;

      const faq = await FAQ.create(faqData);

      sendSuccess(res, 'FAQ created successfully', faq, 201);
    } catch (error) {
      console.error('Create FAQ error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateFAQ(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const faq = await FAQ.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!faq) {
        sendNotFound(res, 'FAQ not found');
        return;
      }

      sendSuccess(res, 'FAQ updated successfully', faq);
    } catch (error) {
      console.error('Update FAQ error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteFAQ(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const faq = await FAQ.findByIdAndDelete(id);

      if (!faq) {
        sendNotFound(res, 'FAQ not found');
        return;
      }

      sendSuccess(res, 'FAQ deleted successfully');
    } catch (error) {
      console.error('Delete FAQ error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
