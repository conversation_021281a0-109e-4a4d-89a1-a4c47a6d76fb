import { Request, Response } from 'express';
import { User, Appointment, Order, Product, Service } from '../models';
import { sendSuccess, sendError } from '../utils/response';
import { AdminQuery } from '../types';

export class AdminController {
  static async getDashboardStats(req: Request, res: Response): Promise<void> {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const startOfYear = new Date(today.getFullYear(), 0, 1);

      const [
        totalUsers,
        totalAppointments,
        totalOrders,
        totalProducts,
        totalServices,
        todayAppointments,
        todayOrders,
        monthlyRevenue,
        yearlyRevenue,
        pendingAppointments,
        processingOrders
      ] = await Promise.all([
        User.countDocuments({ role: 'user' }),
        Appointment.countDocuments(),
        Order.countDocuments(),
        Product.countDocuments({ isActive: true }),
        Service.countDocuments({ isActive: true }),
        Appointment.countDocuments({ 
          date: { $gte: startOfDay },
          status: { $in: ['pending', 'confirmed'] }
        }),
        Order.countDocuments({ 
          createdAt: { $gte: startOfDay }
        }),
        Order.aggregate([
          { $match: { 
            createdAt: { $gte: startOfMonth },
            paymentStatus: 'paid'
          }},
          { $group: { _id: null, total: { $sum: '$totalAmount' } }}
        ]),
        Order.aggregate([
          { $match: { 
            createdAt: { $gte: startOfYear },
            paymentStatus: 'paid'
          }},
          { $group: { _id: null, total: { $sum: '$totalAmount' } }}
        ]),
        Appointment.countDocuments({ status: 'pending' }),
        Order.countDocuments({ status: 'processing' })
      ]);

      const stats = {
        overview: {
          totalUsers,
          totalAppointments,
          totalOrders,
          totalProducts,
          totalServices
        },
        today: {
          appointments: todayAppointments,
          orders: todayOrders
        },
        revenue: {
          monthly: monthlyRevenue[0]?.total || 0,
          yearly: yearlyRevenue[0]?.total || 0
        },
        pending: {
          appointments: pendingAppointments,
          orders: processingOrders
        }
      };

      sendSuccess(res, 'Dashboard statistics retrieved successfully', stats);
    } catch (error) {
      console.error('Get dashboard stats error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getAppointments(req: Request, res: Response): Promise<void> {
    try {
      const {
        status,
        date,
        page = 1,
        limit = 20,
        search
      } = req.query as AdminQuery;

      const filter: any = {};

      if (status) {
        filter.status = status;
      }

      if (date) {
        const searchDate = new Date(date);
        filter.date = {
          $gte: searchDate,
          $lt: new Date(searchDate.getTime() + 24 * 60 * 60 * 1000)
        };
      }

      if (search) {
        filter.$or = [
          { 'customerInfo.name': { $regex: search, $options: 'i' } },
          { 'customerInfo.email': { $regex: search, $options: 'i' } },
          { 'customerInfo.phone': { $regex: search, $options: 'i' } }
        ];
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [appointments, total] = await Promise.all([
        Appointment.find(filter)
          .populate('user', 'name email phone')
          .populate('service', 'name duration price')
          .sort({ date: -1, time: -1 })
          .skip(skip)
          .limit(limitNum),
        Appointment.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Appointments retrieved successfully', {
        appointments,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get admin appointments error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getCustomers(req: Request, res: Response): Promise<void> {
    try {
      const {
        search,
        page = 1,
        limit = 20
      } = req.query as AdminQuery;

      const filter: any = { role: 'user' };

      if (search) {
        filter.$or = [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { phone: { $regex: search, $options: 'i' } }
        ];
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [customers, total] = await Promise.all([
        User.find(filter)
          .select('-password')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        User.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Customers retrieved successfully', {
        customers,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get admin customers error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getOrders(req: Request, res: Response): Promise<void> {
    try {
      const {
        status,
        page = 1,
        limit = 20,
        search
      } = req.query as AdminQuery;

      const filter: any = {};

      if (status) {
        filter.status = status;
      }

      if (search) {
        filter.$or = [
          { orderNumber: { $regex: search, $options: 'i' } }
        ];
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [orders, total] = await Promise.all([
        Order.find(filter)
          .populate('user', 'name email phone')
          .populate('items.product', 'name images')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        Order.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Orders retrieved successfully', {
        orders,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get admin orders error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getProducts(req: Request, res: Response): Promise<void> {
    try {
      const {
        category,
        page = 1,
        limit = 20,
        search
      } = req.query as AdminQuery;

      const filter: any = {};

      if (category) {
        filter.category = category;
      }

      if (search) {
        filter.$or = [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [products, total] = await Promise.all([
        Product.find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        Product.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Products retrieved successfully', {
        products,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get admin products error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
