export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'requires_capture' | 'canceled' | 'succeeded';
  client_secret?: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_account' | 'digital_wallet';
  last4?: string;
  brand?: string;
  exp_month?: number;
  exp_year?: number;
}

export interface RefundRequest {
  paymentIntentId: string;
  amount?: number; // If not provided, refunds full amount
  reason?: 'duplicate' | 'fraudulent' | 'requested_by_customer';
}

export interface RefundResponse {
  id: string;
  amount: number;
  status: 'pending' | 'succeeded' | 'failed' | 'canceled';
  reason?: string;
}

export class PaymentService {
  private static isStripeConfigured(): boolean {
    return !!(process.env.STRIPE_SECRET_KEY && process.env.STRIPE_PUBLISHABLE_KEY);
  }

  static async createPaymentIntent(
    amount: number, 
    currency: string = 'usd',
    metadata?: Record<string, string>
  ): Promise<{ success: boolean; paymentIntent?: PaymentIntent; error?: string }> {
    try {
      if (!this.isStripeConfigured()) {
        // Mock payment intent for development
        const mockPaymentIntent: PaymentIntent = {
          id: `pi_mock_${Date.now()}`,
          amount,
          currency,
          status: 'requires_payment_method',
          client_secret: `pi_mock_${Date.now()}_secret_mock`
        };

        console.log(`Mock payment intent created: ${mockPaymentIntent.id} for $${amount/100}`);
        return { success: true, paymentIntent: mockPaymentIntent };
      }

      // Production Stripe implementation
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      
      const paymentIntent = await stripe.paymentIntents.create({
        amount,
        currency,
        metadata: metadata || {},
        automatic_payment_methods: {
          enabled: true,
        },
      });

      return { 
        success: true, 
        paymentIntent: {
          id: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          status: paymentIntent.status,
          client_secret: paymentIntent.client_secret
        }
      };

    } catch (error) {
      console.error('Payment intent creation error:', error);
      return { 
        success: false, 
        error: (error as Error).message 
      };
    }
  }

  static async confirmPaymentIntent(
    paymentIntentId: string,
    paymentMethodId?: string
  ): Promise<{ success: boolean; paymentIntent?: PaymentIntent; error?: string }> {
    try {
      if (!this.isStripeConfigured()) {
        // Mock payment confirmation
        const mockPaymentIntent: PaymentIntent = {
          id: paymentIntentId,
          amount: 0, // Would be retrieved from database in real implementation
          currency: 'usd',
          status: 'succeeded'
        };

        console.log(`Mock payment confirmed: ${paymentIntentId}`);
        return { success: true, paymentIntent: mockPaymentIntent };
      }

      // Production Stripe implementation
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      
      const confirmParams: any = {};
      if (paymentMethodId) {
        confirmParams.payment_method = paymentMethodId;
      }

      const paymentIntent = await stripe.paymentIntents.confirm(
        paymentIntentId,
        confirmParams
      );

      return { 
        success: true, 
        paymentIntent: {
          id: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          status: paymentIntent.status
        }
      };

    } catch (error) {
      console.error('Payment confirmation error:', error);
      return { 
        success: false, 
        error: (error as Error).message 
      };
    }
  }

  static async retrievePaymentIntent(
    paymentIntentId: string
  ): Promise<{ success: boolean; paymentIntent?: PaymentIntent; error?: string }> {
    try {
      if (!this.isStripeConfigured()) {
        // Mock payment intent retrieval
        const mockPaymentIntent: PaymentIntent = {
          id: paymentIntentId,
          amount: 0,
          currency: 'usd',
          status: 'succeeded'
        };

        return { success: true, paymentIntent: mockPaymentIntent };
      }

      // Production Stripe implementation
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

      return { 
        success: true, 
        paymentIntent: {
          id: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          status: paymentIntent.status
        }
      };

    } catch (error) {
      console.error('Payment intent retrieval error:', error);
      return { 
        success: false, 
        error: (error as Error).message 
      };
    }
  }

  static async createRefund(
    refundRequest: RefundRequest
  ): Promise<{ success: boolean; refund?: RefundResponse; error?: string }> {
    try {
      if (!this.isStripeConfigured()) {
        // Mock refund
        const mockRefund: RefundResponse = {
          id: `re_mock_${Date.now()}`,
          amount: refundRequest.amount || 0,
          status: 'succeeded',
          reason: refundRequest.reason
        };

        console.log(`Mock refund created: ${mockRefund.id}`);
        return { success: true, refund: mockRefund };
      }

      // Production Stripe implementation
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      
      const refundParams: any = {
        payment_intent: refundRequest.paymentIntentId
      };

      if (refundRequest.amount) {
        refundParams.amount = refundRequest.amount;
      }

      if (refundRequest.reason) {
        refundParams.reason = refundRequest.reason;
      }

      const refund = await stripe.refunds.create(refundParams);

      return { 
        success: true, 
        refund: {
          id: refund.id,
          amount: refund.amount,
          status: refund.status,
          reason: refund.reason
        }
      };

    } catch (error) {
      console.error('Refund creation error:', error);
      return { 
        success: false, 
        error: (error as Error).message 
      };
    }
  }

  static async processGiftCardPayment(
    giftCardCode: string,
    amount: number
  ): Promise<{ success: boolean; remainingBalance?: number; error?: string }> {
    try {
      // This would integrate with the gift card controller
      const { GiftCardController } = await import('../controllers/giftCardController');
      
      const result = await GiftCardController.redeemGiftCard(giftCardCode, amount);
      
      if (result.success) {
        return { 
          success: true, 
          remainingBalance: result.remainingBalance 
        };
      } else {
        return { 
          success: false, 
          error: result.error 
        };
      }

    } catch (error) {
      console.error('Gift card payment error:', error);
      return { 
        success: false, 
        error: (error as Error).message 
      };
    }
  }

  static calculateTax(amount: number, taxRate: number): number {
    return Math.round(amount * taxRate);
  }

  static calculateShipping(amount: number, shippingRate: number, freeShippingThreshold?: number): number {
    if (freeShippingThreshold && amount >= freeShippingThreshold) {
      return 0;
    }
    return shippingRate;
  }

  static formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  }
}
