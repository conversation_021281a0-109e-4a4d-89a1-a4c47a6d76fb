import { Router } from 'express';
import { ServiceController } from '../controllers';
import { authenticate, authorize } from '../middleware/auth';
import { validate } from '../middleware/validation';
import { mongoIdValidation } from '../utils/validation';

const router = Router();

// GET /api/services
router.get(
  '/',
  ServiceController.getAllServices
);

// GET /api/services/categories
router.get(
  '/categories',
  ServiceController.getServiceCategories
);

// GET /api/services/:id
router.get(
  '/:id',
  validate(mongoIdValidation()),
  ServiceController.getServiceById
);

// Admin routes
// POST /api/services (admin only)
router.post(
  '/',
  authenticate,
  authorize('admin'),
  ServiceController.createService
);

// PUT /api/services/:id (admin only)
router.put(
  '/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  ServiceController.updateService
);

// DELETE /api/services/:id (admin only)
router.delete(
  '/:id',
  authenticate,
  authorize('admin'),
  validate(mongoIdValidation()),
  ServiceController.deleteService
);

export default router;
