import { Appointment, Service, User } from '../models';
import { sendEmail, emailTemplates } from '../utils/email';
import { IAppointment, AppointmentQuery } from '../types';

export class AppointmentService {
  static async getAvailableTimeSlots(date: string, serviceId?: string): Promise<string[]> {
    const appointmentDate = new Date(date);
    
    // Get all booked appointments for the date
    const bookedAppointments = await Appointment.find({
      date: appointmentDate,
      status: { $in: ['pending', 'confirmed'] }
    }).select('time');

    const bookedTimes = bookedAppointments.map(apt => apt.time);

    // Generate available time slots (9 AM to 6 PM, 30-minute intervals)
    const allTimeSlots = [];
    for (let hour = 9; hour < 18; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeSlot = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        allTimeSlots.push(timeSlot);
      }
    }

    // Filter out booked times
    const availableSlots = allTimeSlots.filter(slot => !bookedTimes.includes(slot));

    return availableSlots;
  }

  static async createAppointment(appointmentData: {
    user: string;
    service: string;
    date: Date;
    time: string;
    customerInfo: {
      name: string;
      email: string;
      phone: string;
    };
    message?: string;
  }): Promise<IAppointment> {
    // Check if the time slot is available
    const existingAppointment = await Appointment.findOne({
      date: appointmentData.date,
      time: appointmentData.time,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (existingAppointment) {
      throw new Error('This time slot is already booked');
    }

    // Verify service exists
    const service = await Service.findById(appointmentData.service);
    if (!service) {
      throw new Error('Service not found');
    }

    // Create appointment
    const appointment = await Appointment.create(appointmentData);
    await appointment.populate('service', 'name duration price');

    // Send confirmation email
    try {
      const emailContent = emailTemplates.appointmentConfirmation(
        appointmentData.customerInfo.name,
        service.name,
        appointmentData.date.toDateString(),
        appointmentData.time
      );
      
      await sendEmail({
        to: appointmentData.customerInfo.email,
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text
      });
    } catch (error) {
      console.error('Failed to send appointment confirmation email:', error);
    }

    return appointment;
  }

  static async getUserAppointments(userId: string): Promise<IAppointment[]> {
    const appointments = await Appointment.find({ user: userId })
      .populate('service', 'name duration price category')
      .sort({ date: 1, time: 1 });

    return appointments;
  }

  static async updateAppointment(
    appointmentId: string,
    userId: string,
    updateData: Partial<IAppointment>
  ): Promise<IAppointment> {
    const appointment = await Appointment.findOne({
      _id: appointmentId,
      user: userId
    });

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    // If updating date/time, check availability
    if (updateData.date || updateData.time) {
      const checkDate = updateData.date || appointment.date;
      const checkTime = updateData.time || appointment.time;

      const existingAppointment = await Appointment.findOne({
        _id: { $ne: appointmentId },
        date: checkDate,
        time: checkTime,
        status: { $in: ['pending', 'confirmed'] }
      });

      if (existingAppointment) {
        throw new Error('This time slot is already booked');
      }
    }

    Object.assign(appointment, updateData);
    await appointment.save();
    await appointment.populate('service', 'name duration price category');

    return appointment;
  }

  static async cancelAppointment(appointmentId: string, userId: string): Promise<void> {
    const appointment = await Appointment.findOne({
      _id: appointmentId,
      user: userId
    });

    if (!appointment) {
      throw new Error('Appointment not found');
    }

    appointment.status = 'cancelled';
    await appointment.save();
  }

  static async getAppointments(query: AppointmentQuery): Promise<IAppointment[]> {
    const filter: any = {};

    if (query.date) {
      filter.date = new Date(query.date);
    }

    if (query.status) {
      filter.status = query.status;
    }

    if (query.service) {
      filter.service = query.service;
    }

    const appointments = await Appointment.find(filter)
      .populate('user', 'name email phone')
      .populate('service', 'name duration price category')
      .sort({ date: 1, time: 1 });

    return appointments;
  }
}
