import { Request, Response } from 'express';
import { SEO } from '../models';
import { sendSuccess, sendError } from '../utils/response';

export class SEOController {
  static async getSEOSettings(req: Request, res: Response): Promise<void> {
    try {
      let seoSettings = await SEO.findOne();
      
      if (!seoSettings) {
        // Create default SEO settings if none exist
        seoSettings = await SEO.create({
          title: 'MicroLocs - Professional Hair Care Services',
          description: 'Professional microloc and hair care services. Book appointments, shop premium products, and transform your hair with expert care.',
          keywords: 'microlocs, hair care, professional hair services, dreadlocks, hair styling, hair products',
          ogImage: '',
          structuredData: {
            "@context": "https://schema.org",
            "@type": "LocalBusiness",
            "name": "MicroLocs",
            "description": "Professional hair care services",
            "@id": "",
            "url": "",
            "telephone": "",
            "address": {
              "@type": "PostalAddress",
              "streetAddress": "",
              "addressLocality": "",
              "addressRegion": "",
              "postalCode": "",
              "addressCountry": "US"
            }
          }
        });
      }

      sendSuccess(res, 'SEO settings retrieved successfully', seoSettings);
    } catch (error) {
      console.error('Get SEO settings error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateSEOSettings(req: Request, res: Response): Promise<void> {
    try {
      const updateData = req.body;

      let seoSettings = await SEO.findOne();
      
      if (!seoSettings) {
        seoSettings = await SEO.create(updateData);
      } else {
        Object.assign(seoSettings, updateData);
        await seoSettings.save();
      }

      sendSuccess(res, 'SEO settings updated successfully', seoSettings);
    } catch (error) {
      console.error('Update SEO settings error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
