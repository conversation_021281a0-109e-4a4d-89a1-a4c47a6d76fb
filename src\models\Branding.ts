import mongoose, { Schema, Document } from 'mongoose';

export interface IBranding extends Document {
  _id: string;
  global: {
    siteName: string;
    tagline: string;
    logo: string;
    phone: string;
    email: string;
    address: string;
    instagram: string;
    facebook: string;
    twitter: string;
    youtube: string;
  };
  home: {
    heroTitle: string;
    heroSubtitle: string;
    heroImage: string;
    featuredServices: Array<{
      title: string;
      description: string;
      image: string;
    }>;
    testimonialHeading: string;
  };
  services: {
    pageTitle: string;
    pageSubtitle: string;
    serviceCategories: Array<{
      name: string;
      description: string;
    }>;
  };
  shop: {
    pageTitle: string;
    pageSubtitle: string;
    featuredCollectionTitle: string;
  };
  consultation: {
    pageTitle: string;
    pageSubtitle: string;
    formTitle: string;
    formSubtitle: string;
  };
  login: {
    pageTitle: string;
    pageSubtitle: string;
  };
  signup: {
    pageTitle: string;
    pageSubtitle: string;
    agreeTermsText: string;
    termsLinkText: string;
    andText: string;
    privacyLinkText: string;
  };
  cart: {
    pageTitle: string;
    emptyCartMessage: string;
    freeShippingThreshold: string;
    shippingCalculated: string;
  };
  productDetail: {
    addToCartButton: string;
    quantityLabel: string;
    overviewTab: string;
    ingredientsTab: string;
    reviewsTab: string;
  };
  footer: {
    description: string;
    copyrightText: string;
    quickLinks: Array<{
      text: string;
      url: string;
    }>;
  };
  createdAt: Date;
  updatedAt: Date;
}

const brandingSchema = new Schema<IBranding>({
  global: {
    siteName: { type: String, default: 'MicroLocs' },
    tagline: { type: String, default: 'Professional Hair Care Services' },
    logo: { type: String, default: '' },
    phone: { type: String, default: '' },
    email: { type: String, default: '' },
    address: { type: String, default: '' },
    instagram: { type: String, default: '' },
    facebook: { type: String, default: '' },
    twitter: { type: String, default: '' },
    youtube: { type: String, default: '' }
  },
  home: {
    heroTitle: { type: String, default: 'Welcome to MicroLocs' },
    heroSubtitle: { type: String, default: 'Professional hair care services' },
    heroImage: { type: String, default: '' },
    featuredServices: [{
      title: { type: String, required: true },
      description: { type: String, required: true },
      image: { type: String, required: true }
    }],
    testimonialHeading: { type: String, default: 'What Our Clients Say' }
  },
  services: {
    pageTitle: { type: String, default: 'Our Services' },
    pageSubtitle: { type: String, default: 'Professional hair care services' },
    serviceCategories: [{
      name: { type: String, required: true },
      description: { type: String, required: true }
    }]
  },
  shop: {
    pageTitle: { type: String, default: 'Shop' },
    pageSubtitle: { type: String, default: 'Premium hair care products' },
    featuredCollectionTitle: { type: String, default: 'Featured Products' }
  },
  consultation: {
    pageTitle: { type: String, default: 'Book Consultation' },
    pageSubtitle: { type: String, default: 'Schedule your appointment' },
    formTitle: { type: String, default: 'Consultation Form' },
    formSubtitle: { type: String, default: 'Tell us about your needs' }
  },
  login: {
    pageTitle: { type: String, default: 'Login' },
    pageSubtitle: { type: String, default: 'Access your account' }
  },
  signup: {
    pageTitle: { type: String, default: 'Sign Up' },
    pageSubtitle: { type: String, default: 'Create your account' },
    agreeTermsText: { type: String, default: 'I agree to the' },
    termsLinkText: { type: String, default: 'Terms of Service' },
    andText: { type: String, default: 'and' },
    privacyLinkText: { type: String, default: 'Privacy Policy' }
  },
  cart: {
    pageTitle: { type: String, default: 'Shopping Cart' },
    emptyCartMessage: { type: String, default: 'Your cart is empty' },
    freeShippingThreshold: { type: String, default: 'Free shipping on orders over $50' },
    shippingCalculated: { type: String, default: 'Shipping calculated at checkout' }
  },
  productDetail: {
    addToCartButton: { type: String, default: 'Add to Cart' },
    quantityLabel: { type: String, default: 'Quantity' },
    overviewTab: { type: String, default: 'Overview' },
    ingredientsTab: { type: String, default: 'Ingredients' },
    reviewsTab: { type: String, default: 'Reviews' }
  },
  footer: {
    description: { type: String, default: 'Professional hair care services' },
    copyrightText: { type: String, default: '© 2024 MicroLocs. All rights reserved.' },
    quickLinks: [{
      text: { type: String, required: true },
      url: { type: String, required: true }
    }]
  }
}, {
  timestamps: true
});

export const Branding = mongoose.model<IBranding>('Branding', brandingSchema);
