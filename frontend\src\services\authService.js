import apiService from './api.js'

/**
 * Authentication service for handling user authentication
 */
class AuthService {
  /**
   * Register a new user
   */
  async register(userData) {
    try {
      const response = await apiService.post('/auth/register', userData)
      
      if (response.success && response.token) {
        apiService.setAuthToken(response.token, userData.rememberMe)
      }
      
      return response
    } catch (error) {
      console.error('Registration error:', error)
      throw error
    }
  }

  /**
   * Login user
   */
  async login(credentials) {
    try {
      const response = await apiService.post('/auth/login', credentials)
      
      if (response.success && response.token) {
        apiService.setAuthToken(response.token, credentials.rememberMe)
      }
      
      return response
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  /**
   * Logout user
   */
  async logout() {
    try {
      await apiService.post('/auth/logout')
    } catch (error) {
      console.error('Logout error:', error)
      // Continue with local logout even if API call fails
    } finally {
      apiService.removeAuthToken()
      // Clear any other user data from storage
      localStorage.removeItem('rememberedUser')
      sessionStorage.removeItem('currentUser')
      localStorage.removeItem('currentUser') // backward compatibility
    }
  }

  /**
   * Verify current token
   */
  async verifyToken() {
    try {
      const response = await apiService.get('/auth/verify')
      return response
    } catch (error) {
      console.error('Token verification error:', error)
      apiService.removeAuthToken()
      throw error
    }
  }

  /**
   * Request password reset
   */
  async forgotPassword(email) {
    try {
      const response = await apiService.post('/auth/forgot-password', { email })
      return response
    } catch (error) {
      console.error('Forgot password error:', error)
      throw error
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(token, newPassword) {
    try {
      const response = await apiService.post('/auth/reset-password', {
        token,
        password: newPassword
      })
      return response
    } catch (error) {
      console.error('Reset password error:', error)
      throw error
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return !!apiService.getAuthToken()
  }

  /**
   * Get current user info from token
   */
  async getCurrentUser() {
    if (!this.isAuthenticated()) {
      return null
    }

    try {
      const response = await this.verifyToken()
      return response.user
    } catch (error) {
      return null
    }
  }

  /**
   * Change password
   */
  async changePassword(currentPassword, newPassword) {
    try {
      const response = await apiService.put('/users/password', {
        currentPassword,
        newPassword
      })
      return response
    } catch (error) {
      console.error('Change password error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const authService = new AuthService()
export default authService
