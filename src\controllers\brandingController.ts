import { Request, Response } from 'express';
import { Branding } from '../models';
import { sendSuccess, sendError, sendNotFound } from '../utils/response';

export class BrandingController {
  /**
   * Get complete branding configuration in a single request
   * This aggregates all branding data to minimize API calls
   */
  static async getCompleteBranding(req: Request, res: Response): Promise<void> {
    try {
      // Get branding content from database
      let branding = await Branding.findOne();

      if (!branding) {
        // Create default branding if none exists
        branding = await Branding.create({});
      }

      // Aggregate all branding data into a single response
      const completeBranding = {
        // Main branding assets
        branding: {
          logo: branding.global?.logo || 'https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600',
          heroImage: 'https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600',
          favicon: 'https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600',
          heroTitle: branding.home?.heroTitle || 'Beautiful Locs, Beautiful You',
          heroSubtitle: branding.home?.heroSubtitle || 'Professional loc services and natural hair care by Tina',
          aboutTitle: 'About Goldie Locs',
          aboutText: 'With years of experience in natural hair care and loc maintenance, Tina provides personalized services to help you achieve and maintain beautiful, healthy locs.'
        },

        // Business profile information
        business: {
          name: branding.global?.siteName || 'Goldie Locs By Tina',
          tagline: branding.global?.tagline || 'By Tina',
          description: 'Professional locs and natural hair care services. Specializing in micro locs, traditional locs, and natural hair maintenance.',
          phone: branding.global?.phone || '(*************',
          email: branding.global?.email || '<EMAIL>',
          address: {
            street: '123 Beauty Street',
            city: 'Atlanta',
            state: 'GA',
            zip: '30309',
            full: branding.global?.address || '123 Beauty Street, Atlanta, GA 30309'
          },
          social: {
            instagram: branding.global?.instagram || 'https://instagram.com/goldielocs',
            facebook: branding.global?.facebook || 'https://facebook.com/goldielocs',
            twitter: branding.global?.twitter || 'https://twitter.com/goldielocs'
          },
          hours: {
            monday: '9:00 AM - 6:00 PM',
            tuesday: '9:00 AM - 6:00 PM',
            wednesday: '9:00 AM - 6:00 PM',
            thursday: '9:00 AM - 6:00 PM',
            friday: '9:00 AM - 6:00 PM',
            saturday: '9:00 AM - 4:00 PM',
            sunday: 'Closed'
          }
        },

        // Theme settings
        theme: {
          colors: {
            primary: '#008000',      // Green
            secondary: '#f3d016',    // Gold/Yellow
            accent: '#006600',       // Dark Green
            background: '#ffffff',   // White
            text: '#000000',         // Black
            textSecondary: '#666666' // Gray
          },
          fonts: {
            primary: 'Inter, system-ui, sans-serif',
            secondary: 'Inter, system-ui, sans-serif',
            heading: 'Inter, system-ui, sans-serif'
          }
        },

        // Site settings
        site: {
          seo: {
            title: 'Goldie Locs By Tina - Professional Loc Services',
            description: 'Professional locs and natural hair care services in Atlanta, GA. Specializing in micro locs, traditional locs, and natural hair maintenance.',
            keywords: 'locs, dreadlocks, natural hair, hair care, Atlanta, micro locs, loc maintenance'
          },
          features: {
            onlineBooking: true,
            ecommerce: true,
            loyaltyProgram: true,
            giftCards: true,
            reviews: true,
            blog: false
          }
        }
      }

      sendSuccess(res, 'Complete branding configuration retrieved successfully', completeBranding);
    } catch (error) {
      console.error('Get complete branding error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getBrandingContent(req: Request, res: Response): Promise<void> {
    try {
      let branding = await Branding.findOne();

      if (!branding) {
        // Create default branding if none exists
        branding = await Branding.create({});
      }

      sendSuccess(res, 'Branding content retrieved successfully', branding);
    } catch (error) {
      console.error('Get branding content error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getBusinessProfile(req: Request, res: Response): Promise<void> {
    try {
      // Return default business profile
      const businessProfile = {
        name: 'Goldie Locs By Tina',
        tagline: 'By Tina',
        description: 'Professional locs and natural hair care services. Specializing in micro locs, traditional locs, and natural hair maintenance.',
        phone: '(*************',
        email: '<EMAIL>',
        address: {
          street: '123 Beauty Street',
          city: 'Atlanta',
          state: 'GA',
          zip: '30309',
          full: '123 Beauty Street, Atlanta, GA 30309'
        },
        social: {
          instagram: 'https://instagram.com/goldielocs',
          facebook: 'https://facebook.com/goldielocs',
          twitter: 'https://twitter.com/goldielocs'
        },
        hours: {
          monday: '9:00 AM - 6:00 PM',
          tuesday: '9:00 AM - 6:00 PM',
          wednesday: '9:00 AM - 6:00 PM',
          thursday: '9:00 AM - 6:00 PM',
          friday: '9:00 AM - 6:00 PM',
          saturday: '9:00 AM - 4:00 PM',
          sunday: 'Closed'
        }
      }

      sendSuccess(res, 'Business profile retrieved successfully', businessProfile);
    } catch (error) {
      console.error('Error getting business profile:', error);
      sendError(res, 'Failed to get business profile');
    }
  }

  static async getThemeSettings(req: Request, res: Response): Promise<void> {
    try {
      // Return default theme settings
      const themeSettings = {
        colors: {
          primary: '#008000',      // Green
          secondary: '#f3d016',    // Gold/Yellow
          accent: '#006600',       // Dark Green
          background: '#ffffff',   // White
          text: '#000000',         // Black
          textSecondary: '#666666' // Gray
        },
        fonts: {
          primary: 'Inter, system-ui, sans-serif',
          secondary: 'Inter, system-ui, sans-serif',
          heading: 'Inter, system-ui, sans-serif'
        }
      }

      sendSuccess(res, 'Theme settings retrieved successfully', themeSettings);
    } catch (error) {
      console.error('Error getting theme settings:', error);
      sendError(res, 'Failed to get theme settings');
    }
  }

  static async getSiteSettings(req: Request, res: Response): Promise<void> {
    try {
      // Return default site settings
      const siteSettings = {
        seo: {
          title: 'Goldie Locs By Tina - Professional Loc Services',
          description: 'Professional locs and natural hair care services in Atlanta, GA. Specializing in micro locs, traditional locs, and natural hair maintenance.',
          keywords: 'locs, dreadlocks, natural hair, hair care, Atlanta, micro locs, loc maintenance'
        },
        features: {
          onlineBooking: true,
          ecommerce: true,
          loyaltyProgram: true,
          giftCards: true,
          reviews: true,
          blog: false
        }
      }

      sendSuccess(res, 'Site settings retrieved successfully', siteSettings);
    } catch (error) {
      console.error('Error getting site settings:', error);
      sendError(res, 'Failed to get site settings');
    }
  }

  static async updateBrandingContent(req: Request, res: Response): Promise<void> {
    try {
      const updateData = req.body;

      let branding = await Branding.findOne();
      
      if (!branding) {
        branding = await Branding.create(updateData);
      } else {
        Object.assign(branding, updateData);
        await branding.save();
      }

      sendSuccess(res, 'Branding content updated successfully', branding);
    } catch (error) {
      console.error('Update branding content error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateBrandingSection(req: Request, res: Response): Promise<void> {
    try {
      const { section } = req.params;
      const updateData = req.body;

      const validSections = [
        'global', 'home', 'services', 'shop', 'consultation', 
        'login', 'signup', 'cart', 'productDetail', 'footer'
      ];

      if (!validSections.includes(section)) {
        sendError(res, 'Invalid section specified');
        return;
      }

      let branding = await Branding.findOne();
      
      if (!branding) {
        branding = await Branding.create({});
      }

      // Update specific section
      (branding as any)[section] = { ...(branding as any)[section], ...updateData };
      await branding.save();

      sendSuccess(res, `${section} section updated successfully`, branding);
    } catch (error) {
      console.error('Update branding section error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
